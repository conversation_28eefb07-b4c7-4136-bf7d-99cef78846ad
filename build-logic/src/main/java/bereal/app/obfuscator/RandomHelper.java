/*
 * Copyright 2020 <PERSON>
 * Copyright 2023 LSPosed
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*****
 *
 * DISCLAIMER
 *
 * >>> Files to modify in case of changes: <<<
 *
 * build-logic/src/main/java/bereal/app/obfuscator/DeobfuscatorHelper.java
 * build-logic/src/main/java/bereal/app/obfuscator/RandomHelper.java
 * platform/obfuscator/core/src/main/java/bereal/app/obfuscator/DeobfuscatorHelper.java
 * platform/obfuscator/core/src/main/java/bereal/app/obfuscator/RandomHelper.java
 *
 */

package bereal.app.obfuscator;

/**
 * The type Random helper.
 */
public class RandomHelper {
  private RandomHelper() {
    // Cannot be instantiated.
  }

  /**
   * Seed long.
   *
   * @param x the x (initial seed - cannot be changed)
   * @return the long
   */
  public static long seed(final long x) {
    /**
     *    -- 32 bits version
     *         // David Stafford's
     *         // http://zimbry.blogspot.com/2011/09/better-bit-mixing-improving-on.html
     *         // "Mix4" variant of the 64-bit finalizer in Austin Appleby's
     *         // MurmurHash3 algorithm.... “murmur” stands for multiply, rotate, multiply, rotate, because it's exactly what it does!
     *         https://github.com/aappleby/smhasher/blob/master/src/MurmurHash3.cpp
     *
     *         z = (z ^ (z >> 33)).wrapping_mul(0x62A9D9ED799705F5);
     *         z = (z ^ (z >> 28)).wrapping_mul(0xCB24D0A5C88C35B3);
     *         (z >> 32) as u32
     *
     *    -- 64 bits version
     *         Adapted From 64 bits: https://xoshiro.di.unimi.it/splitmix64.c
     */
    final long z = (x ^ (x >>> 30)) * 0xBF58476D1CE4E5B9L;
    return ((z ^ (z >>> 27)) * 0x94D049BB133111EBL) >>> 31;
  }

  /**
   * Next long.
   *
   * @param state the state
   * @return the long
   */
  public static long next(final long state) {
    short s0 = (short) (state & 0xffff);
    short s1 = (short) ((state >>> 16) & 0xffff);
    short next = s0;
    next += s1;
    next = rotl(next, 9);
    next += s0;

    s1 ^= s0;
    s0 = rotl(s0, 13);
    s0 ^= s1;
    s0 ^= (s1 << 5);
    s1 = rotl(s1, 10);

    long result = next;
    result <<= 16;
    result |= s1;
    result <<= 16;
    result |= s0;
    return result;
  }

  private static short rotl(final short x, final int k) {
    return (short) ((x << k) | (x >>> (32 - k)));
  }
}
