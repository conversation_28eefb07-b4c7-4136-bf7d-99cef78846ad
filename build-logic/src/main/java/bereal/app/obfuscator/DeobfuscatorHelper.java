/*
 * Copyright 2020 <PERSON>
 * Copyright 2023 LSPosed
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*****
 *
 * DISCLAIMER
 *
 * >>> Files to modify in case of changes: <<<
 *
 * build-logic/src/main/java/bereal/app/obfuscator/DeobfuscatorHelper.java
 * build-logic/src/main/java/bereal/app/obfuscator/RandomHelper.java
 * platform/obfuscator/core/src/main/java/bereal/app/obfuscator/DeobfuscatorHelper.java
 * platform/obfuscator/core/src/main/java/bereal/app/obfuscator/RandomHelper.java
 *
 */


package bereal.app.obfuscator;

/**
 * The type Deobfuscator helper.
 */
public class DeobfuscatorHelper {
  /**
   * The constant MAX_CHUNK_LENGTH.
   */
  public static final int MAX_CHUNK_LENGTH = 0x1fff;

  private DeobfuscatorHelper() {
    // Cannot be instantiated.
  }

  /**
   * Gets string.
   *
   * @param id     the id
   * @param chunks the chunks
   * @return the string
   */
  public static String getString(final long id, final String[] chunks) {
    long state = RandomHelper.seed(id & 0xffffffffL);
    state = RandomHelper.next(state);
    final long low = (state >>> 32) & 0xffff;
    state = RandomHelper.next(state);
    final long high = (state >>> 16) & 0xffff0000;
    final int index = (int) ((id >>> 32) ^ low ^ high);
    state = getCharAt(index, chunks, state);
    final int length = (int) ((state >>> 32) & 0xffffL);
    final char[] chars = new char[length];

    for (int i = 0; i < length; ++i) {
      state = getCharAt(index + i + 1, chunks, state);
      chars[i] = (char) ((state >>> 32) & 0xffffL);
    }

    return new String(chars);
  }

  private static long getCharAt(final int charIndex, final String[] chunks, final long state) {
    final long nextState = RandomHelper.next(state);
    final String chunk = chunks[charIndex / MAX_CHUNK_LENGTH];
    return nextState ^ ((long) chunk.charAt(charIndex % MAX_CHUNK_LENGTH) << 32);
  }
}
