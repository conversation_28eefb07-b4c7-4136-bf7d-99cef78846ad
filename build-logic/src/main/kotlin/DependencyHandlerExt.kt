import org.gradle.api.artifacts.dsl.DependencyHandler
import org.gradle.kotlin.dsl.project
import bereal.app.gradle.plugin.modules.Modules
import org.gradle.api.Project

fun DependencyHandler.impProject(depName: String) {
    add("implementation", project(depName))
}

fun DependencyHandler.impTestProject(depName: String) {
    add("testImplementation", project(depName))
}

fun DependencyHandler.remoteImage() {
    impProject(Modules.Platform.Image.core)
    impProject(Modules.Platform.Image.Remote.remoteImageCore)
}

fun DependencyHandler.room(project: Project) {
    impProject(Modules.Platform.commonRoom)
    implementation(project.lib("room.runtime"))
    implementation(project.lib("room.ktx"))
    add("testImplementation", project.lib("room.testing"))
    add("ksp", project.lib("room.compiler"))
    implementation(project.lib("sqlcipher"))
}

fun DependencyHandler.implementation(dep: Any) {
    add("implementation", dep)
}

fun DependencyHandler.appDistributionImplementation(dep: Any) {
    add("appDistributionImplementation", dep)
}

fun DependencyHandler.releaseImplementation(dep: Any) {
    add("releaseImplementation", dep)
}

fun DependencyHandler.benchmarkImplementation(dep: Any) {
    add("benchmarkImplementation", dep)
}

fun DependencyHandler.internalImplementation(dep: Any) {
    add("internalImplementation", dep)
}

fun DependencyHandler.externalImplementation(dep: Any) {
    add("externalImplementation", dep)
}

fun DependencyHandler.debugImplementation(dep: Any) {
    add("debugImplementation", dep)
}
