/*
 * Copyright 2021 <PERSON>
 * Copyright 2023 LSPosed
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bereal.app.obfuscator.processor

import org.objectweb.asm.AnnotationVisitor
import org.objectweb.asm.ClassVisitor

class RemoveObfuscateClassPatcher(
  asmApi: Int,
  delegate: ClassVisitor,
) : ClassVisitor(asmApi, delegate) {

  private val obfuscateDescriptor = OBFUSCATE_TYPE.descriptor

  override fun visitAnnotation(desc: String, visible: Boolean): AnnotationVisitor? {
    return if (obfuscateDescriptor != desc) super.visitAnnotation(desc, visible) else null
  }
}
