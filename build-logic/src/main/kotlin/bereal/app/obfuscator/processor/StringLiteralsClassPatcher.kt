/*
 * Copyright 2021 <PERSON>
 * Copyright 2023 LSPosed
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bereal.app.obfuscator.processor

import com.joom.grip.mirrors.toAsmType
import bereal.app.obfuscator.processor.logging.getLogger
import bereal.app.obfuscator.processor.model.Deobfuscator
import org.objectweb.asm.ClassVisitor
import org.objectweb.asm.MethodVisitor
import org.objectweb.asm.commons.GeneratorAdapter

class StringLiteralsClassPatcher(
    private val deobfuscator: Deobfuscator,
    private val stringRegistry: StringRegistry,
    asmApi: Int,
    delegate: ClassVisitor,
) : ClassVisitor(asmApi, delegate) {

  private val logger = getLogger()

  private var className: String = ""

  override fun visit(
    version: Int,
    access: Int,
    name: String,
    signature: String?,
    superName: String?,
    interfaces: Array<out String>?
  ) {
    super.visit(version, access, name, signature, superName, interfaces)
    className = name
  }

  override fun visitMethod(
    access: Int,
    name: String,
    desc: String,
    signature: String?,
    exceptions: Array<out String>?
  ): MethodVisitor {
    val visitor = super.visitMethod(access, name, desc, signature, exceptions)
    return object : GeneratorAdapter(api, visitor, access, name, desc) {
      override fun visitLdcInsn(constant: Any) {
        if (constant is String) {
          replaceStringWithDeobfuscationMethod(constant)
        } else {
          super.visitLdcInsn(constant)
        }
      }

      private fun replaceStringWithDeobfuscationMethod(string: String) {
        logger.info("{}.{}{}:", className, name, desc)
        logger.info("  Obfuscating string literal: \"{}\"", string)
        val stringId = stringRegistry.registerString(string)
        push(stringId)
        invokeStatic(deobfuscator.type.toAsmType(), deobfuscator.deobfuscationMethod)
      }
    }
  }
}
