/*
 * Copyright 2021 <PERSON>
 * Copyright 2023 LSPosed
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bereal.app.obfuscator.plugin

import com.android.build.api.variant.Variant

open class LSParanoidExtension {
    var seed: Int? = null
    var global: Boolean = false
    var includeDependencies: Boolean = false
    var variantFilter: (Variant) -> Boolean = { it.buildType != "debug" }
}
