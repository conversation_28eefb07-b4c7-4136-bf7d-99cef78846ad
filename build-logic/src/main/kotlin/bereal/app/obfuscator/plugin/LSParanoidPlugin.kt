/*
 * Copyright 2020 <PERSON>
 * Copyright 2023 LSPosed
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package bereal.app.obfuscator.plugin

import bereal.app.gradle.plugin.BasePlugin
import com.android.build.api.artifact.ScopedArtifact
import com.android.build.api.variant.AndroidComponentsExtension
import com.android.build.api.variant.ScopedArtifacts.Scope
import com.android.build.gradle.api.AndroidBasePlugin
import org.gradle.api.Project
import org.gradle.api.plugins.JavaPlugin
import org.gradle.api.tasks.compile.JavaCompile
import org.gradle.kotlin.dsl.create
import org.gradle.kotlin.dsl.getByType
import org.gradle.kotlin.dsl.register
import org.gradle.kotlin.dsl.withType
import java.security.SecureRandom

class LSParanoidPlugin : BasePlugin() {
    override fun apply(project: Project) {
        super.apply(project)

        val extension = project.extensions.create<LSParanoidExtension>("lsparanoid")

        project.plugins.withType<AndroidBasePlugin>() {
            val components = project.extensions.getByType(AndroidComponentsExtension::class)
            components.onVariants { variant ->
                if (!extension.variantFilter(variant)) return@onVariants
                val task = project.tasks.register<LSParanoidTask>(
                    "lsparanoid${variant.name.replaceFirstChar { it.uppercase() }}"
                ) {
                    bootClasspath.set(components.sdkComponents.bootClasspath)
                    classpath = variant.compileClasspath
                    seed.set(extension.seed ?: SecureRandom().nextInt())
                    global.set(extension.global)
                    projectName.set("${project.rootProject.name}\$${project.path}")
                }
                variant.artifacts.forScope(if (extension.includeDependencies) Scope.ALL else Scope.PROJECT).use(task)
                    .toTransform(
                        ScopedArtifact.CLASSES,
                        LSParanoidTask::jars,
                        LSParanoidTask::dirs,
                        LSParanoidTask::output,
                    )
            }
            project.addDependencies()
        }

        project.tasks.withType<JavaCompile> {
            options.compilerArgs.add("-XDstringConcat=inline")
        }
        project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile::class) {
            kotlinOptions.freeCompilerArgs += "-Xstring-concat=inline"
        }
    }

    private fun Project.addDependencies() {
        val configurationName = JavaPlugin.IMPLEMENTATION_CONFIGURATION_NAME
        dependencies.add(configurationName, project(":platform:obfuscator:core"))
    }
}
