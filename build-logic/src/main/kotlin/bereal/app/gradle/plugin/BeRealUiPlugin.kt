@file:Suppress("UnstableApiUsage")

package bereal.app.gradle.plugin

import bereal.app.gradle.plugin.modules.Modules
import com.android.build.gradle.internal.lint.AndroidLintAnalysisTask
import com.android.build.gradle.internal.lint.LintModelWriterTask
import impProject
import implementation
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.jetbrains.kotlin.compose.compiler.gradle.ComposeCompilerGradlePluginExtension


class BeRealUiPlugin : BeRealComposePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        with(target) {
            dependencies {
                impProject(Modules.Platform.design)
                impProject(Modules.Platform.translations)
                implementation("google-android-material")

                impProject(Modules.Platform.common)
                impProject(Modules.Platform.commonAndroid)
            }
        }
    }
}
