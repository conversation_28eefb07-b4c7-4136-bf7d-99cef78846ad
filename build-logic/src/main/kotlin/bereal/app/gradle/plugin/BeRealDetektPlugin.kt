package bereal.app.gradle.plugin

import io.gitlab.arturbosch.detekt.Detekt
import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.withType

class BeRealDetektPlugin : BasePlugin() {
    override fun apply(target: Project) {
        with(target) {
            pluginManager.apply(libs.plugins.detekt.get().pluginId)
            extensions.configure<DetektExtension> {
                config.setFrom(files("$rootDir/config/detekt/detekt.yml"))
                parallel = true
                buildUponDefaultConfig = true
                allRules = false
                autoCorrect = true
            }

            tasks.withType<Detekt>().configureEach {
                reports {
                    xml.required.set(true)
                    html.required.set(true)
                }
            }
        }
    }
}
