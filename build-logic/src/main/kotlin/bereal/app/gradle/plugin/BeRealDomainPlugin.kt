package bereal.app.gradle.plugin

import bereal.app.gradle.plugin.modules.Modules
import impProject
import impTestProject
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class BeRealDomainPlugin : BasePlugin() {

    override fun apply(target: Project) {
        super.apply(target)
        with(target) {
            val libraryPlugin = versionCatalog.findPlugin("bereal-library").get().get().pluginId
            pluginManager.apply(libraryPlugin)

            val koinDomainPlugin = versionCatalog.findPlugin("bereal-koin-domain").get().get().pluginId
            pluginManager.apply(koinDomainPlugin)
            pluginManager.apply(target.libs.plugins.bereal.detekt.get().pluginId)

            dependencies {
                impProject(Modules.Platform.common)
                impProject(Modules.Platform.entities)

                implementation("kotlinx.coroutines.core")

                impTestBundle("tests-domain")
                impTestProject(Modules.Platform.commonTest)
            }
        }
    }
}
