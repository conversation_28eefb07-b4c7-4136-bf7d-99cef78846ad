package bereal.app.gradle.plugin

import com.android.build.api.variant.VariantBuilder
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType
import setupAndroidModule
import setupKotlinAndroid
import setupProdSandboxFlavors

class BeRealSampleAppPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.setupAndroidModule(ModuleType.APPLICATION)

        target.extensions.findByType<com.android.build.api.variant.ApplicationAndroidComponentsExtension>()?.apply {
            beforeVariants { variantBuilder: VariantBuilder ->
                // filter out samples other than debug mode
                val buildType = variantBuilder.buildType ?: ""
                if (!buildType.contains("debug") && !buildType.contains("appDistribution")
                ) {
                    variantBuilder.enable = false
                }
            }
        }

        target.setupKotlinAndroid()
        target.setupProdSandboxFlavors() // because we use modules that declare those flavors

        target.dependencies {
            implementation("kotlinx.datetime")
            implementation("timber")
            implementation("google-android-material")
        }
    }
}
