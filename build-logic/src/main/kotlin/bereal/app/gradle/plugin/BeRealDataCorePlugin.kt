package bereal.app.gradle.plugin

import impProject
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import setupAndroidModule
import bereal.app.gradle.plugin.modules.Modules
import bereal.app.gradle.util.getPluginId
import impTestProject

class BeRealDataCorePlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.plugins.apply {
            val libraryPlugin = versionCatalog.getPluginId("bereal-library")
            apply(libraryPlugin)
            val serializationPlugin = versionCatalog.getPluginId("bereal-serialization")
            apply(serializationPlugin)
        }

        target.setupAndroidModule(ModuleType.LIBRARY)

        target.dependencies {
            impProject(Modules.Platform.common)
            impProject(Modules.Platform.commonAndroid)
            impProject(Modules.Platform.entities)
            impProject(Modules.Platform.error) // for generic error mapper
            impProject(Modules.Platform.dataCore)

            implementation("kotlinx.coroutines.core")


            impTestProject(Modules.Platform.commonTest)
            impTestBundle("tests")

        }
    }
}
