package bereal.app.gradle.plugin

import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import setupAndroidModule

class BeRealLibraryPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        if(System.getenv("DEP_ANALYSIS")?.toString() == "true" || System.getProperty("DEP_ANALYSIS")?.toString() == "true") {
            target.plugins.apply("com.autonomousapps.dependency-analysis")
        }

        target.plugins.apply("org.gradle.android.cache-fix")

        target.setupAndroidModule(ModuleType.LIBRARY)

        target.dependencies {
            implementation("kotlinx.datetime")
            implementation("timber")
        }
    }
}
