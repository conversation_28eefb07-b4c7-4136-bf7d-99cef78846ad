package bereal.app.gradle.plugin

import com.android.build.api.variant.AndroidComponentsExtension
import com.google.devtools.ksp.gradle.KspExtension
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType
import org.gradle.kotlin.dsl.getByType
import org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension

class BeRealKoinDataPlugin : BasePlugin() {

    override fun apply(target: Project) {
        super.apply(target)
        with(target) {
            pluginManager.apply(versionCatalog.findPlugin("ksp").get().get().pluginId)
            extensions.findByType<KspExtension>()?.arg("KOIN_DEFAULT_MODULE", "false")
            dependencies {
                impBundle("koin-data")
                ksp("koin.ksp.compiler")
            }
        }
    }
}
