package bereal.app.gradle.plugin

import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class BeRealWithJsonModelsPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.pluginManager.apply(versionCatalog.findPlugin("kotlin.serialization").get().get().pluginId)
        target.dependencies {
            impBundle("kotlinSerialization")
        }
    }
}
