package bereal.app.gradle.plugin

import isBenchmarkEnabled
import org.gradle.api.Project

class BeRealBaselineProfilePlugin : BasePlugin() {

    override fun apply(target: Project) {
        super.apply(target)
        with(target) {
            if(project.isBenchmarkEnabled()) {
                val libraryPlugin =
                    versionCatalog.findPlugin("baselineprofile").get().get().pluginId
                pluginManager.apply(libraryPlugin)
            }
        }
    }
}
