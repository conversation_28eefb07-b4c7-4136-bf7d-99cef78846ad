package bereal.app.gradle.plugin

import bereal.app.gradle.plugin.modules.Modules
import bereal.app.gradle.util.getPluginId
import impProject
import impTestProject
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class BeRealRetrofitPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.plugins.apply {
            val serializationPlugin = versionCatalog.getPluginId("bereal-serialization")
            apply(serializationPlugin)
        }

        target.dependencies {
            impProject(Modules.Platform.commonNetwork)
            implementation("retrofit.json.converter")
            implementation("okhttp")

            debugImplementation("okhttp.logging")
        }
    }
}
