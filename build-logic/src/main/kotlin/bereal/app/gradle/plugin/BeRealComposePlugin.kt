package bereal.app.gradle.plugin

import bereal.app.gradle.util.applyPlugin
import com.android.build.gradle.BaseExtension
import com.android.build.gradle.internal.lint.AndroidLintAnalysisTask
import com.android.build.gradle.internal.lint.LintModelWriterTask
import implementation
import org.gradle.api.Project
import org.gradle.kotlin.dsl.configure
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType
import org.jetbrains.kotlin.compose.compiler.gradle.ComposeCompilerGradlePluginExtension

open class BeRealComposePlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.extensions.findByType<BaseExtension>()?.apply {
            buildFeatures.compose = true
        }

        with(target) {
            applyPlugin(versionCatalog, "kotlin-compose-compiler")
            pluginManager.apply(target.libs.plugins.bereal.detekt.get().pluginId)

            dependencies {
                impPlatform("androidx.compose.bom")
                impBundle("compose")
                impBundle("ktx")
                implementation(libs.androidx.compose.material3.android)
                implementation(libs.androidx.compose.material3)

                debugImplementation("androidx.compose.ui.tooling")
                debugImplementation("androidx.compose.ui.test.manifest")
            }

            configureCompose()
        }
    }
}

private fun Project.composeCompiler(block: ComposeCompilerGradlePluginExtension.() -> Unit) {
    extensions.configure<ComposeCompilerGradlePluginExtension>(block)
}

private fun Project.configureCompose() {
    composeCompiler {
        // Enable 'strong skipping'
        // https://medium.com/androiddevelopers/jetpack-compose-strong-skipping-mode-explained-cbdb2aa4b900
        // TODO STRONG-SKIPPING disabled for now, will need a special MR
        // enableStrongSkippingMode.set(true)
        // stabilityConfigurationFile.set(rootProject.file("compose-stability.conf"))

        // Needed for Layout Inspector to be able to see all of the nodes in the component tree:
        //https://issuetracker.google.com/issues/338842143
        includeSourceInformation.set(true)

        if (project.providers.gradleProperty("bereal.enableComposeCompilerReports").isPresent) {
            val composeReports = layout.buildDirectory.map { it.dir("reports").dir("compose") }
            reportsDestination.set(composeReports)
            metricsDestination.set(composeReports)
        }
    }

    // Workaround for:
    // Task 'generateDebugUnitTestLintModel' uses this output of task
    // 'generateResourceAccessorsForAndroidUnitTest' without declaring an explicit or
    // implicit dependency.
    tasks.matching { it is AndroidLintAnalysisTask || it is LintModelWriterTask }.configureEach {
        mustRunAfter(tasks.matching { it.name.startsWith("generateResourceAccessorsFor") })
    }
}
