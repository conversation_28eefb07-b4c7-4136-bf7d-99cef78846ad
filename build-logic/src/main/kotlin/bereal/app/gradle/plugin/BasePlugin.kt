package bereal.app.gradle.plugin

import org.gradle.api.Plugin
import org.gradle.api.Project
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.api.artifacts.dsl.DependencyHandler
import org.gradle.kotlin.dsl.getByType
import versionCatalog

open class BasePlugin : Plugin<Project>  {

    lateinit var versionCatalog: VersionCatalog

    override fun apply(target: Project) {
        versionCatalog = target.versionCatalog()
    }

    fun DependencyHandler.impPlatform(depName: String) {
        add("implementation", platform(versionCatalog.findLibrary(depName).get()))
    }

    fun DependencyHandler.implementation(depName: String) {
        add("implementation", versionCatalog.findLibrary(depName).get())
    }

    fun DependencyHandler.debugImplementation(depName: String) {
        add("debugImplementation", versionCatalog.findLibrary(depName).get())
    }

    fun DependencyHandler.impBundle(bundle: String) {
        add("implementation", versionCatalog.findBundle(bundle).get())
    }

    fun DependencyHandler.impTestBundle(bundle: String) {
        add("testImplementation", versionCatalog.findBundle(bundle).get())
    }

    fun DependencyHandler.kapt(depName: String) {
        add("kapt", versionCatalog.findLibrary(depName).get())
    }

    fun DependencyHandler.ksp(depName: String) {
        add("ksp", versionCatalog.findLibrary(depName).get())
    }
}
