package bereal.app.gradle.plugin

import bereal.app.gradle.plugin.modules.Modules
import bereal.app.gradle.util.getPluginId
import impProject
import impTestProject
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class BeRealDataImplPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.plugins.apply {
            val libraryPlugin = versionCatalog.getPluginId("bereal-library")
            apply(libraryPlugin)

            val koinDataPlugin = versionCatalog.getPluginId("bereal-koin-data")
            apply(koinDataPlugin)
            apply(target.libs.plugins.bereal.detekt.get().pluginId)
        }

        target.dependencies {
            impProject(Modules.Platform.common)
            impProject(Modules.Platform.commonAndroid)
            impProject(Modules.Platform.entities)
            impProject(Modules.Platform.error) // for generic error mapper
            impProject(Modules.Platform.remoteLoggerCore)
            impProject(Modules.Platform.dataCore)

            implementation("kotlinx.coroutines.core")

            impTestProject(Modules.Platform.commonTest)
            impTestBundle("tests")
        }
    }
}
