package bereal.app.gradle.plugin

import bereal.app.gradle.plugin.modules.Modules
import bereal.app.gradle.util.getPluginId
import impProject
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies

class BeRealDataNoOpPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        target.plugins.apply {
            val libraryPlugin = versionCatalog.getPluginId("bereal-library")
            apply(libraryPlugin)
        }

        target.dependencies {
            impProject(Modules.Platform.common)
            impProject(Modules.Platform.commonAndroid)
            impProject(Modules.Platform.entities)
            impProject(Modules.Platform.error) // for generic error mapper

            impBundle("koin")

            implementation("kotlinx.coroutines.core")
        }
    }
}
