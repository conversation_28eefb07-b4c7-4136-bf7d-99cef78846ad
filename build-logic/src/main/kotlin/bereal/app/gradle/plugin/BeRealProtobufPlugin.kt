package bereal.app.gradle.plugin

import com.google.protobuf.gradle.ProtobufExtension
import com.google.protobuf.gradle.id
import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType
import versionCatalog

class BeRealProtobufPlugin : BasePlugin() {
    override fun apply(target: Project) {
        super.apply(target)

        val versionCatalog = target.versionCatalog()

        target.pluginManager.apply {
            val plugin = versionCatalog.findPlugin("protobuf").get().get().pluginId
            apply(plugin)
        }

        val protobuf = target.extensions.findByType<ProtobufExtension>()
        if(protobuf != null) {
            with(protobuf) {
                protoc {
                    artifact = versionCatalog.findLibrary("protobuf-protoc").get().get().toString()
                }

                val protocGenJava = versionCatalog.findLibrary("grpc.protoc.gen.java").get().get().toString()
                val protocGenKotlin = versionCatalog.findLibrary("grpc.protoc.gen.kotlin").get().get().toString() + ":jdk8@jar"

                plugins {
                    id("java") {
                        artifact = protocGenJava
                    }
                    id("grpc") {
                        artifact = protocGenJava
                    }
                    id("grpckt") {
                        artifact = protocGenKotlin
                    }
                }

                generateProtoTasks {
                    all().forEach {
                        it.doFirst { target.delete(it.outputs) }
                        it.plugins {
                            id("java") {
                                option("lite")
                            }
                            id("grpc") {
                                option("lite")
                            }
                            id("grpckt") {
                                option("lite")
                            }
                        }
                        it.builtins {
                            id("kotlin") {
                                option("lite")
                            }
                        }
                    }
                }
            }
        }

        target.dependencies {
            impBundle("grpc")
        }
    }
}
