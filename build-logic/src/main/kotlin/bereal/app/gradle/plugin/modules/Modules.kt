package bereal.app.gradle.plugin.modules

internal object Modules {
    object Platform {

        private const val container = ":platform"

        const val common = "$container:common"
        const val commonAndroid = "$container:common-android"
        const val commonTest = "$container:common-test"
        const val commonRoom = "$container:common-room"
        const val commonNetwork = "$container:common-network:common-network"
        const val datastore = "$container:datastore"
        const val design = "$container:design:core"
        const val entities = "$container:entities"
        const val lint = "$container:lint"
        const val error = "$container:error"
        const val remoteLoggerCore = "$container:remote-logger:remote-logger"
        const val translations = "$container:translations"
        const val dataCore = "$container:data:core:core"

        object Image {
            private const val container = "${Platform.container}:image"

            const val core = "$container:core-ui"

            object Remote {
                private const val subContainer = "$container:remote-image"
                const val remoteImageCore = "$subContainer:core"
                const val remoteImageCoilImpl = "$subContainer:impl-coil"
            }
        }
    }
}
