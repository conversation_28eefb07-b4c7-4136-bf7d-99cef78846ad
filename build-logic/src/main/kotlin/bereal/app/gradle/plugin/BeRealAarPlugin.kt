package bereal.app.gradle.plugin

import org.gradle.api.Project
import setupAndroidModule

class BeRealAarPlugin : BasePlugin() {

    // to be sure the plugin dependency-analysis-gradle-plugin run I need to change it to a library plugin
    // but then in this case I cannot build an app distri :/ that's why I have this condition

    override fun apply(target: Project) {
        super.apply(target)
        if (System.getenv("DEP_ANALYSIS")?.toString() == "true" || System.getProperty("DEP_ANALYSIS")?.toString() == "true") {
            target.setupAndroidModule(ModuleType.LIBRARY)
        } else {
            // no op
        }
    }
}
