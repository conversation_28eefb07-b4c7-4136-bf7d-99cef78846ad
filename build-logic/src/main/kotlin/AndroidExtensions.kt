import bereal.app.gradle.plugin.modules.Modules
import com.android.build.api.dsl.AndroidResources
import com.android.build.api.dsl.ApplicationExtension
import com.android.build.api.dsl.BuildFeatures
import com.android.build.api.dsl.BuildType
import com.android.build.api.dsl.CommonExtension
import com.android.build.api.dsl.DefaultConfig
import com.android.build.api.dsl.Installation
import com.android.build.api.dsl.LibraryExtension
import com.android.build.api.dsl.ProductFlavor
import com.android.build.gradle.BaseExtension
import org.gradle.api.JavaVersion
import org.gradle.api.NamedDomainObjectContainer
import org.gradle.api.Project
import org.gradle.api.artifacts.VersionCatalog
import org.gradle.api.artifacts.VersionCatalogsExtension
import org.gradle.kotlin.dsl.assign
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.findByType
import org.gradle.kotlin.dsl.getByType
import org.gradle.kotlin.dsl.project
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinAndroidProjectExtension
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import java.io.File
import java.io.FileInputStream
import java.util.Properties

/**
 * Types of Android modules
 */
enum class ModuleType(val pluginId: String) {
    LIBRARY("com.android.library"),
    APPLICATION("com.android.application");
}

/**
 * Common settings for all android-based modules
 */
fun Project.setupAndroidModule(type: ModuleType) {
    pluginManager.apply {
        apply(type.pluginId)
        apply("de.mannodermaus.android-junit5")
    }

    enableCustomLintChecks()

    setupCommonAndroidModule()

    extensions.findByType<BaseExtension>()?.apply {
        compileOptions {
            sourceCompatibility = JavaVersion.VERSION_17
            targetCompatibility = JavaVersion.VERSION_17
        }

        compileSdkVersion(Config.compileSdk)

        defaultConfig {
            minSdk = Config.minSdk
            targetSdk = Config.targetSdk
            versionCode = 1
            versionName = "1.0"

            testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

            when (type) {
                ModuleType.LIBRARY -> consumerProguardFiles("consumer-rules.pro")
                ModuleType.APPLICATION -> {}
            }
        }

        setupBuildTypes()

        packagingOptions {
            resources {
                excludes += "/META-INF/{AL2.0,LGPL2.1}"
            }
        }

        testOptions {
            unitTests.all {
                it.useJUnitPlatform()
            }
            unitTests {
                isIncludeAndroidResources = true
                isReturnDefaultValues = true
            }
            setupKotlinAndroid()
        }

        // https://github.com/JLLeitschuh/ktlint-gradle/issues/524
        // https://github.com/JLLeitschuh/ktlint-gradle/pull/558
        // Adds the given source directories to this set.
        sourceSets.configureEach {
            java.srcDirs("src/$name/kotlin")
        }
    } ?: throw IllegalStateException("Failed to set up Android module.")
}

fun Project.setupCommonAndroidModule() {
    extensions.findByType<ApplicationExtension>()?.apply {
        setupCommonModuleInternal(this@setupCommonAndroidModule)
    }

    extensions.findByType<LibraryExtension>()?.apply {
        setupCommonModuleInternal(this@setupCommonAndroidModule)
    }

    setupKotlinAndroid()
}

private fun <BuildFeaturesT : BuildFeatures,
    BuildTypeT : BuildType,
    DefaultConfigT : DefaultConfig,
    ProductFlavorT : ProductFlavor,
    AndroidResourcesT : AndroidResources,
    InstallationT : Installation
> CommonExtension<
    BuildFeaturesT,
    BuildTypeT,
    DefaultConfigT,
    ProductFlavorT,
    AndroidResourcesT,
    InstallationT>.setupCommonModuleInternal(project: Project) {

    buildFeatures {
        buildConfig = true
    }

    lint {
        // TODO check how we handle this
        disable += listOf("MissingTranslation", "MissingQuantity", "ImpliedQuantity")
        lintConfig = project.file("lint.xml")
    }

}

fun BaseExtension.setupBuildTypes() {
    buildTypes {
        maybeCreate("debug")
        maybeCreate("appDistribution")
        maybeCreate("release")
    }
}

private var cacheLocalProperties: Properties? = null

fun Project.localProperties(forceRead: Boolean = false): Properties {
    val cached = cacheLocalProperties
    return if (cached != null && !forceRead) {
        cached
    } else {
        Properties().apply {
            val localPropertiesFile = File(project.rootProject.rootDir, "local.properties")
            if (localPropertiesFile.exists()) {
                load(FileInputStream(localPropertiesFile))
            }
        }.also {
            cacheLocalProperties = it
        }
    }
}

fun Project.isMinifyEnabled(): Boolean {
    val value = localProperties().getProperty("bereal.minify")?.toBoolean()
        ?: System.getenv("CI_ENABLE_MINIFICATION")?.let { it.toBoolean() } ?: true
    if (this.name == "app") {
        println("bereal.minify = $value")
    }
    return value
}

fun Project.isFakeNativeAdsEnabled(): Boolean {
    val value = localProperties().getProperty("bereal.fakeNativeAds")?.toBoolean() ?: false
    println("bereal.fakeNativeAds = $value")
    return value
}


fun Project.forceDebugSignatureForRelease(): Boolean {
    val value = localProperties().getProperty("bereal.forceDebugSignatureForRelease")?.toBoolean()
        ?: System.getenv("FORCE_DEBUG_SIGNATURE_FOR_RELEASE")?.let { it.toBoolean() } ?: false
    if (this.name == "app") {
        println("bereal.forceDebugSignatureForRelease = $value")
    }
    return value
}

fun <BuildTypeT: BuildType> NamedDomainObjectContainer<BuildTypeT>.internal(action: BuildType.() -> Unit) {
    maybeCreate("internal")
    named("internal") {
        initWith(getByName("release"))
        isMinifyEnabled = false
        isShrinkResources = false
        matchingFallbacks += listOf("release")
        action()
    }
}

fun Project.setupProdSandboxFlavors() {
    extensions.findByType<BaseExtension>()?.apply {
        flavorDimensions("version")
        productFlavors {
            create("prod") {
                // Assigns this product flavor to the "version" flavor dimension.
                // If you are using only one dimension, this property is optional,
                // and the plugin automatically assigns all the module's flavors to
                // that dimension.
                dimension = "version"
                buildConfigField("String", "FLAVOR", "\"Production\"")
            }
            create("sandbox") {
                dimension = "version"
                buildConfigField("String", "FLAVOR", "\"Sandbox\"")
            }
        }
    } ?: throw IllegalStateException("Failed to set up Android module.")
}

/**
 * A wrapper on top of Android kotlin plugin
 */
fun Project.setupKotlinAndroid() {
    pluginManager.apply {
        apply("kotlin-android")
    }

    setupKotlinCompilerAndroid()

    dependencies {
        add("implementation", versionCatalog().findLibrary("kotlinx.collections.immutable").get())
        add("implementation", versionCatalog().findLibrary("kotlinx.datetime").get())
    }
}

fun Project.lib(alias: String) = versionCatalog().findLibrary(alias).get()

fun Project.enableCustomLintChecks() {
    dependencies {
        add("lintChecks", project(Modules.Platform.lint))
    }
}

fun Project.setupKotlinCompiler() {
    tasks.withType<KotlinCompile>().configureEach {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_17
            suppressWarnings = true
        }
    }
}

fun Project.setupKotlinCompilerAndroid() {
    with(extensions.getByType<KotlinAndroidProjectExtension>()) {
        compilerOptions {
            jvmTarget = JvmTarget.JVM_17
            //suppressWarnings = true
            freeCompilerArgs.addAll(listOf(
                "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
                "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            ))

            if (project.hasProperty("bereal.enableComposeCompilerReports")) {
                freeCompilerArgs.addAll(listOf(
                    "-P",
                    "plugin:androidx.compose.compiler.plugins.kotlin:reportsDestination=" +
                        layout.buildDirectory.get().asFile.absolutePath + "/compose_metrics",
                ))
            }
        }
    }
}

fun Project.versionCatalog(): VersionCatalog =
    this.extensions.getByType<VersionCatalogsExtension>().named("libs")

fun execute(command: String) =
    Runtime.getRuntime().exec(command).inputStream.reader().use { it.readText() }

fun isRunningOnCI(): Boolean = System.getenv("CI") != null || System.getProperty("CI") != null

fun Project.isBenchmarkEnabled(): Boolean {
    return findProperty("bereal.benchmark")?.toString()?.toBoolean() ?: false
}

fun isKoverMergeReportEnabled(): Boolean = isRunningOnCI()
fun isApplovinQualityServiceEnabled(): Boolean = isRunningOnCI()
