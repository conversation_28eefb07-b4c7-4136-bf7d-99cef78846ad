import org.gradle.initialization.DependenciesAccessors
import org.gradle.kotlin.dsl.support.serviceOf

plugins {
    `kotlin-dsl`
}

group = "bereal.android.app.build-logic"

dependencies {
    compileOnly(libs.android.gradle.plugin)
    implementation(libs.android.gradle.api)
    compileOnly(libs.kotlin.gradle.plugin)
    compileOnly(libs.detekt.gradlePlugin)
    implementation(libs.kotlin.gradle.plugin.api)
    implementation(libs.grip)
    implementation(libs.asmCommons)
    implementation(libs.gradlePlugin.ksp)
    implementation(libs.protobuf.plugin)
    implementation(libs.kotlin.compose.compiler.plugin)
    implementation(libs.gradle.android.cache.fix.plugin)

    gradle.serviceOf<DependenciesAccessors>().classes.asFiles.forEach {
        compileOnly(files(it.absolutePath))
    }
}

gradlePlugin {
    plugins {
        plugins.register("bereal-library") {
            id = "bereal-library"
            implementationClass = "bereal.app.gradle.plugin.BeRealLibraryPlugin"
        }
        register("bereal-domain") {
            id = "bereal-domain"
            implementationClass = "bereal.app.gradle.plugin.BeRealDomainPlugin"
        }
        plugins.register("bereal-data-impl") {
            id = "bereal-data-impl"
            implementationClass = "bereal.app.gradle.plugin.BeRealDataImplPlugin"
        }
        plugins.register("bereal-data-no-op") {
            id = "bereal-data-no-op"
            implementationClass = "bereal.app.gradle.plugin.BeRealDataNoOpPlugin"
        }
        plugins.register("bereal-data-core") {
            id = "bereal-data-core"
            implementationClass = "bereal.app.gradle.plugin.BeRealDataCorePlugin"
        }
        plugins.register("bereal-ui") {
            id = "bereal-ui"
            implementationClass = "bereal.app.gradle.plugin.BeRealUiPlugin"
        }
        plugins.register("bereal-compose") {
            id = "bereal-compose"
            implementationClass = "bereal.app.gradle.plugin.BeRealComposePlugin"
        }
        plugins.register("bereal-sample-app") {
            id = "bereal-sample-app"
            implementationClass = "bereal.app.gradle.plugin.BeRealSampleAppPlugin"
        }
        plugins.register("bereal-with-json-models") {
            id = "bereal-with-json-models"
            implementationClass = "bereal.app.gradle.plugin.BeRealWithJsonModelsPlugin"
        }
        plugins.register("bereal-base") {
            id = "bereal-base"
            implementationClass = "bereal.app.gradle.plugin.BasePlugin"
        }
        plugins.register("bereal-obfuscator") {
            id = "bereal-obfuscator"
            implementationClass = "bereal.app.obfuscator.plugin.LSParanoidPlugin"
        }
        plugins.register("bereal-koin-base") {
            id = "bereal-koin-base"
            implementationClass = "bereal.app.gradle.plugin.BeRealKoinPlugin"
        }
        plugins.register("bereal-koin-domain") {
            id = "bereal-koin-domain"
            implementationClass = "bereal.app.gradle.plugin.BeRealKoinDomainPlugin"
        }
        plugins.register("bereal-koin-data") {
            id = "bereal-koin-data"
            implementationClass = "bereal.app.gradle.plugin.BeRealKoinDataPlugin"
        }
        plugins.register("bereal-protobuf") {
            id = "bereal-protobuf"
            implementationClass = "bereal.app.gradle.plugin.BeRealProtobufPlugin"
        }
        plugins.register("bereal-retrofit") {
            id = "bereal-retrofit"
            implementationClass = "bereal.app.gradle.plugin.BeRealRetrofitPlugin"
        }
        plugins.register("bereal-datastore") {
            id = "bereal-datastore"
            implementationClass = "bereal.app.gradle.plugin.BeRealDatastorePlugin"
        }
        plugins.register("bereal-baseline-profile") {
            id = "bereal-baseline-profile"
            implementationClass = "bereal.app.gradle.plugin.BeRealBaselineProfilePlugin"
        }
        plugins.register("bereal-aar") {
            id = "bereal-aar"
            implementationClass = "bereal.app.gradle.plugin.BeRealAarPlugin"
        }
        register("bereal-detekt") {
            id = "bereal-detekt"
            implementationClass = "bereal.app.gradle.plugin.BeRealDetektPlugin"
        }
    }
}
