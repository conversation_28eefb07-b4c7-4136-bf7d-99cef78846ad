package bereal.app.benchmark

import android.graphics.Point
import android.util.Log
import androidx.benchmark.macro.CompilationMode
import androidx.benchmark.macro.FrameTimingMetric
import androidx.benchmark.macro.MacrobenchmarkScope
import androidx.benchmark.macro.StartupMode
import androidx.benchmark.macro.junit4.MacrobenchmarkRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.uiautomator.By
import bereal.app.benchmark.config.BenchmarkConfig
import bereal.app.benchmark.tags.BenchmarkTags
import bereal.app.benchmark.utils.waitUntilVisible
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.time.Duration.Companion.seconds

/**
 * This is an example startup benchmark.
 *
 * It navigates to the device's home screen, and launches the default activity.
 *
 * Before running this benchmark:
 * 1) switch your app's active build variant in the Studio (affects Studio runs only)
 * 2) add `<profileable android:shell="true" />` to your app's manifest, within the `<application>` tag
 *
 * Run this benchmark from Studio to see startup measurements, and captured system traces
 * for investigating your app's performance.
 */
@RunWith(AndroidJUnit4::class)
@Config(sdk = [35])
class FrameTimingBenchmark {

    companion object {
        const val LOGS_TAG = "TAG_BENCHMARK"
    }

    @get:Rule
    val benchmarkRule = MacrobenchmarkRule()

    @Test
    fun scrollInFriendsPosts() = benchmarkRule.measureRepeated(
        packageName = BenchmarkConfig.appPackage,
        metrics = listOf(FrameTimingMetric()),
        iterations = 5,
        compilationMode = CompilationMode.None(),
        startupMode = StartupMode.WARM, // restarts activity each iteration
        setupBlock = {
            Log.d(LOGS_TAG, "setupBlock - START")
            pressHome()
            Log.d(LOGS_TAG, "setupBlock - END")
        },
        measureBlock = {
            Log.d(LOGS_TAG, "measureBlock - START")
            startActivityAndWait()
            waitUntilVisible(
                viewTag = BenchmarkTags.Timeline.Friends.list,
                logTag = LOGS_TAG,
                timeoutDuration = 10.seconds,
            )
            waitUntilVisible(
                viewTag = BenchmarkTags.Timeline.Friends.oaBanner,
                logTag = LOGS_TAG,
                timeoutDuration = 10.seconds,
            )
            scroll(BenchmarkTags.Timeline.Friends.list)
            Log.d(LOGS_TAG, "measureBlock - END")
        },
    )

    private fun MacrobenchmarkScope.scroll(scrollableTag: String) {
        val column = device.findObject(By.res(scrollableTag))
        column.setGestureMargin(device.displayWidth / 2)
        repeat(1) {
            column.drag(Point(column.visibleCenter.x, column.visibleBounds.top))
        }
    }
}
