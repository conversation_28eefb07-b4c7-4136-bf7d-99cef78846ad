package bereal.app.benchmark

import androidx.benchmark.macro.CompilationMode
import androidx.benchmark.macro.StartupMode
import androidx.benchmark.macro.StartupTimingMetric
import androidx.benchmark.macro.junit4.MacrobenchmarkRule
import androidx.test.filters.LargeTest
import bereal.app.benchmark.config.BenchmarkConfig
import bereal.app.benchmark.tags.BenchmarkTags
import bereal.app.benchmark.utils.waitUntilVisible
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized

/**
 * This is an example startup benchmark.
 *
 * It navigates to the device's home screen, and launches the default activity.
 *
 * Before running this benchmark:
 * 1) switch your app's active build variant in the Studio (affects Studio runs only)
 * 2) add `<profileable android:shell="true" />` to your app's manifest, within the `<application>` tag
 *
 * Run this benchmark from Studio to see startup measurements, and captured system traces
 * for investigating your app's performance.
 */
@LargeTest
@RunWith(Parameterized::class)
class StartupBenchmark(
    private val compilationMode: CompilationMode,
    private val startupMode: StartupMode,
) {

    @get:Rule
    val benchmarkRule = MacrobenchmarkRule()

    @Test
    fun startup() = benchmarkRule.measureRepeated(
        packageName = BenchmarkConfig.appPackage,
        metrics = listOf(StartupTimingMetric()),
        iterations = 10,
        compilationMode = compilationMode,
        startupMode = startupMode,
        setupBlock = {
            pressHome()
        },
        measureBlock = {
            startActivityAndWait()
            // wait until we have loaded the first friend post
            waitUntilVisible(
                viewTag = BenchmarkTags.Timeline.Friends.post(0),
                logTag = LOG_TAG,
            )
        },
    )

    companion object {

        const val LOG_TAG = "TAG_STARTUP"

        @Parameterized.Parameters(name = "mode={0}_{1}")
        @JvmStatic
        fun parameters(): List<Array<Any>> {
            return listOf(
                arrayOf(CompilationMode.None(), StartupMode.COLD),
                arrayOf(CompilationMode.Partial(), StartupMode.COLD),
                // arrayOf(CompilationMode.Full(), StartupMode.COLD),
                // -- warm
                // arrayOf(CompilationMode.None(), StartupMode.WARM),
                // arrayOf(CompilationMode.Partial(), StartupMode.WARM),
                // arrayOf(CompilationMode.Full(), StartupMode.WARM),
            )
        }
    }
}
