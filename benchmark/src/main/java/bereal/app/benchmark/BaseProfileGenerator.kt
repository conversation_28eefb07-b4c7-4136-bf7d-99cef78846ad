package bereal.app.benchmark

import android.os.Build
import androidx.annotation.RequiresApi
import androidx.benchmark.macro.MacrobenchmarkScope
import androidx.benchmark.macro.junit4.BaselineProfileRule
import androidx.test.filters.LargeTest
import androidx.test.internal.runner.junit4.AndroidJUnit4ClassRunner
import androidx.test.uiautomator.Direction
import bereal.app.benchmark.config.BenchmarkConfig
import bereal.app.benchmark.tags.BenchmarkTags
import bereal.app.benchmark.utils.clickOn
import bereal.app.benchmark.utils.onView
import bereal.app.benchmark.utils.pressBack
import bereal.app.benchmark.utils.waitUntilVisible
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4ClassRunner::class)
@RequiresApi(Build.VERSION_CODES.P)
@LargeTest
class BaseProfileGenerator {
    @get:Rule
    val baselineProfileRule = BaselineProfileRule()

    companion object {
        const val LOG_TAG = "TAG_BASEPROFILE"
    }

    @Test
    fun userJourney() = baselineProfileRule.collect(
        packageName = BenchmarkConfig.appPackage,
        maxIterations = 3,
    ) {
        pressHome()
        startActivityAndWait()

        // Reaction
        clickOn(
            viewTag = BenchmarkTags.Timeline.Friends.myPost,
            logTag = LOG_TAG,
        )
        profileReactionScreen()

        // Friends timeline
        pressBack()
        profileFriendsTimeline()
    }

    private fun MacrobenchmarkScope.profileReactionScreen() {
        val reactionPage = BenchmarkTags.Reaction.page
        waitUntilVisible(viewTag = reactionPage, logTag = LOG_TAG)
        onView(reactionPage)
            .apply {
                setGestureMargin(device.displayWidth / 5)
                scroll(Direction.DOWN, 0.5f, 1000)
            }
    }

    private fun MacrobenchmarkScope.profileFriendsTimeline() {
        waitUntilVisible(viewTag = BenchmarkTags.Timeline.Friends.post(0), logTag = LOG_TAG)
        scrollOn(BenchmarkTags.Timeline.Friends.list)
    }

    private fun MacrobenchmarkScope.scrollOn(resourceName: String) {
        onView(resourceName)
            .apply {
                // Set gesture margin to avoid triggering gesture navigation
                // with input events from automation.
                setGestureMargin(device.displayWidth / 5)

                // Scroll down several times
                repeat(2) { fling(Direction.DOWN) }
                fling(Direction.UP)
            }
    }
}
