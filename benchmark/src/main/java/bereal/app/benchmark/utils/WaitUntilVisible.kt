package bereal.app.benchmark.utils

import android.util.Log
import androidx.benchmark.macro.MacrobenchmarkScope
import androidx.test.uiautomator.Until
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

fun MacrobenchmarkScope.waitUntilVisible(
    viewTag: String,
    logTag: String,
    timeoutDuration: Duration = 5.seconds,
) {
    Log.d(logTag, "START : wait until visible [$viewTag]")
    device.wait(
        Until.hasObject(viewSelector(viewTag)),
        timeoutDuration.inWholeMilliseconds,
    ).also { found ->
        if (found) {
            Log.d(logTag, "END : [SUCCESS] wait until visible [$viewTag]")
        } else {
            Log.e(logTag, "END : wait until visible [$viewTag]")
        }
    }
}
