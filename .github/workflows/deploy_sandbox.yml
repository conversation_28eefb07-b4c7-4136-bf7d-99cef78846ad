name: Deploy to Sandbox App Distribution

on:
  schedule:
    - cron: '0 19 * * 1-6' # Weekday at 7pm UTC
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      releaseNote:
        type: string
        description: Release note
        default: ''
      withDebugTools:
        type: boolean
        description: With debug menu
        default: true
      storeApkArtifact:
        type: boolean
        description: Keep APK as artifact
        default: false
      disableMinify:
        type: boolean
        description: Disable minify for faster build
        default: false
    repository_dispatch:
      types: [ trigger_workflow_deploy_sandbox ]

jobs:

  build:
    runs-on: ['self-hosted', 'large']
    permissions:
      contents: "read" # auth to gcloud
      id-token: "write" # auth to gcloud
    env:
      #FIREBASE_TOKEN: ${{secrets.FIREBASE_SANDBOX_TOKEN}}
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      # Setting up ruby on the machine
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: true

      - name: Check %@ in strings.xml
        run: sh scripts/verif/findErrorsInLocales.sh

      - name: Check placeholder orders on strings.xml
        run: python3 scripts/strings/check_strings.py platform/translations/src/main/res/

      - name: Build an apk to Sandbox App Distribution
        if: ${{ github.event_name != 'schedule' }}
        env:
          APP_DISTRIBUTION_DESCRIPTION: ${{ github.event.inputs.releaseNote }}
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          WITH_DEBUG_TOOLS: ${{ github.event.inputs.withDebugTools }}
          CI_ENABLE_MINIFICATION: ${{ !github.event.inputs.disableMinify }}
        run: |
          echo Compile with fastlane
          bundle exec fastlane buildSandbox

      - name: Build nightly apk to Sandbox App Distribution
        if: ${{ github.event_name == 'schedule' }}
        env:
          APP_DISTRIBUTION_DESCRIPTION: 'Nightly build'
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          WITH_DEBUG_TOOLS: true
        run: |
          echo Compile with fastlane
          bundle exec fastlane buildSandbox

      - uses: google-github-actions/auth@v2
        with:
          create_credentials_file: 'true'
          workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-pool/providers/github-provider'
          service_account: '<EMAIL>'

      - name: Deploy an apk to Sandbox App Distribution
        env:
          APP_DISTRIBUTION_DESCRIPTION: ${{ github.event.inputs.releaseNote }}
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          WITH_DEBUG_TOOLS: ${{ github.event.inputs.withDebugTools }}
        run: |
          echo Compile with fastlane
          bundle exec fastlane deploySandbox

      - name: Upload APK to Github Artifacts
        if: ${{ github.event.inputs.storeApkArtifact }}
        uses: actions/upload-artifact@v4
        with:
          name: Sandbox AppDistribution APK
          path: app/build/outputs/apk/sandbox/appDistribution/app-sandbox-appDistribution.apk

