name: Check project health

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:

  build:
    runs-on: ['self-hosted', 'large']
    env:
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      - name: run analysis
        continue-on-error: true
        run: |
          ./scripts/project_health.sh

      - name: Verify file existence
        run: |
          if [ ! -f "build/reports/dependency-analysis/build-health-report.txt" ]; then
            echo "Report file not found!"
            exit 1
          fi
          echo "Report found"

      - name: Upload Analysis Result to Github Artifacts
        uses: actions/upload-artifact@v4
        if: always() # always run even if the previous step fails
        with:
          name: Result
          path: build/reports/dependency-analysis/build-health-report.txt
