name: Generate Instant Release Candidate

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      keepMapping:
        type: boolean
        description: Keep mapping
        default: false

jobs:
  buildAndDeployInstantPlayStore:
    runs-on: ['self-hosted', 'large']
    env:
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      # Setting up ruby on the machine
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: true

      - name: Create git profile
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "CI Bot"

      # https://stefma.medium.com/how-to-store-a-android-keystore-safely-on-github-actions-f0cef9413784
      - name: Restore secrets
        run: |
          echo "${{ secrets.GOOGLE_PLAY_CONSOLE_KEY }}" > google-play-console-key.json.asc
          gpg -d  --passphrase "${{ secrets.GOOGLE_PLAY_CONSOLE_KEY_PASSPHRASE }}" --batch google-play-console-key.json.asc > app/google-play-console-key.json
          echo "${{ secrets.SIGNING_STORE }}" > bereal.keystore.asc
          gpg -d  --passphrase "${{ secrets.SIGNING_STORE_PASSPHRASE }}" --batch bereal.keystore.asc > app/bereal.keystore

      - name: Check %@ in strings.xml
        run: sh scripts/verif/findErrorsInLocales.sh

      - name: Second check on strings.xml
        run: python3 scripts/strings/check_strings.py platform/translations/src/main/res/

      - name: Extract existing version code and version name
        id: version_details
        run: |
          ./gradlew :features:instant-light-flow:app:getInstantVersionCode
          version_code=$(cat features/instant-light-flow/versionCode)
          version_name=$(grep versionName features/instant-light-flow/app/build.gradle.kts | head -1 | awk '{print $3}')

          echo "version_code=${version_code}" >> $GITHUB_OUTPUT
          echo "version_name=${version_name}" >> $GITHUB_OUTPUT

      - name: setupGradle
        run: ./scripts/ci/configure_ci_gradle_properties.sh

      - name: Assemble an AAB for Playstore
        #TO DEBUG : uncomment this to keep the .aab and skip the assemble
        #if: steps.cache.outputs.cache-hit != 'true'
        env:
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
        run: |
          bundle exec fastlane assembleInstantPlayStoreVersion

      - name: Deploy an AAB to Playstore
        env:
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          DEBUG: 1
        run: |
          bundle exec fastlane deployInstantPlayStore --verbose

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v3
        if: failure() # always run even if the previous step fails
        with:
          report_paths: '**/build/test-results/**/TEST-*.xml'

      - name: Tag version
        run: |
          git tag "rc/v${{steps.version_details.outputs.version_name}}-${{steps.version_details.outputs.version_code}}"
          git push --tags

      - name: Upload Manifest to Github Artifacts
        uses: actions/upload-artifact@v4
        if: ${{ github.event.inputs.keepMapping }}
        with:
          name: Prod Release Manifest
          path: features/instant-light-flow/app/build/outputs/mapping/release/mapping.txt

#  buildAndDeployAppDistribBuilds:
#    needs: buildAndDeployInstantPlayStore
#    runs-on: ['self-hosted', 'large']
#    env:
#      SLACK_URL: ${{secrets.SLACK_URL}}
#    steps:
#      - name: Trigger Deploy Sandbox APK to Sandbox App Distribution
#        uses: actions/github-script@v5
#        with:
#          script: |
#            const owner = process.env.GITHUB_REPOSITORY.split("/")[0]
#            const repo = process.env.GITHUB_REPOSITORY.split("/")[1]
#            const branch = process.env.GITHUB_REF.replace('refs/heads/', '')
#            await github.rest.actions.createWorkflowDispatch({
#              owner: owner,
#              repo: repo,
#              workflow_id: 'deploy_sandbox.yml',
#              ref: branch,
#              inputs: {
#                withDebugTools: true
#              }
#            })
#
#      - name: Trigger Deploy Prod APK to Sandbox App Distribution
#        uses: actions/github-script@v5
#        with:
#          script: |
#            const owner = process.env.GITHUB_REPOSITORY.split("/")[0]
#            const repo = process.env.GITHUB_REPOSITORY.split("/")[1]
#            const branch = process.env.GITHUB_REF.replace('refs/heads/', '')
#            await github.rest.actions.createWorkflowDispatch({
#              owner: owner,
#              repo: repo,
#              workflow_id: 'deploy_prod.yml',
#              ref: branch,
#              inputs: {
#                withDebugTools: true
#              }
#            })
