name: Build Internal APK

on:
  schedule:
    - cron: '0 4 * * 1-6'
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      releaseNote:
        type: string
        description: Release note
        default: ''
      testersGroup:
        type: string
        description: Tester groups
        default: 'internal'
      storeApkArtifact:
        type: boolean
        description: Keep APK as artifact
        default: false

jobs:

  build:
    runs-on: ['self-hosted', 'large']
    permissions:
      contents: "read" # auth to gcloud
      id-token: "write" # auth to gcloud
    env:
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # - name: Test firebase connection
      #   env:
      #     FIREBASE_TOKEN: ${{ secrets.FIREBASE_PROD_TOKEN_V2 }}
      #   uses: joinflux/firebase-tools@v9.16.0
      #   with:
      #     args: projects:list

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      # Setting up ruby on the machine
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: true

      - name: Check %@ in strings.xml
        run: sh scripts/verif/findErrorsInLocales.sh

      - name: Check placeholder orders on strings.xml
        run: python3 scripts/strings/check_strings.py platform/translations/src/main/res/

      - name: Build an internal apk
        env:
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          WITH_DEBUG_TOOLS: true
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          CI_ENABLE_MINIFICATION: true
          APP_DISTRIBUTION_DESCRIPTION: ${{ github.event.inputs.releaseNote }}
        run: |
          echo Compile with fastlane
          bundle exec fastlane buildInternal

      - name: Upload APK to Github Artifacts
        uses: actions/upload-artifact@v4
        if: ${{ github.event.inputs.storeApkArtifact }}
        with:
          name: Prod Internal APK
          path: app/build/outputs/apk/prod/internal/app-prod-internal.apk

      # for now just as artifact

      - uses: google-github-actions/auth@v2
        with:
          create_credentials_file: 'true'
          workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-pool/providers/github-provider'
          service_account: '<EMAIL>'

      - name: Deploy an internal apk to App Distribution
        if: ${{ github.event_name != 'schedule' }}
        env:
          APP_DISTRIBUTION_DESCRIPTION: ${{ github.event.inputs.releaseNote }}
          APP_DISTRIBUTION_GROUP: ${{ github.event.inputs.testersGroup }}
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
        run: |
          echo Deploy with fastlane
          bundle exec fastlane deployInternal

      - name: Deploy an internal apk to App Distribution
        if: ${{ github.event_name == 'schedule' }}
        env:
          APP_DISTRIBUTION_DESCRIPTION: 'Nightly build'
          APP_DISTRIBUTION_GROUP: 'internal'
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
        run: |
          echo Deploy with fastlane
          bundle exec fastlane deployInternal
