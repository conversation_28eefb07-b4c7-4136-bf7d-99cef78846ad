name: Lokalize Checks

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  lokalize_checks:
    if: github.event.pull_request.draft == false && github.actor != 'dependabot[bot]'
    runs-on: ['self-hosted', 'tiny']
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check %@ in strings.xml
        run: sh scripts/verif/findErrorsInLocales.sh

      - name: Check placeholder orders on strings.xml
        run: python3 scripts/strings/check_strings.py platform/translations/src/main/res/
