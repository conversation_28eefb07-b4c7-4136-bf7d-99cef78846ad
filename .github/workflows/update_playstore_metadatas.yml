name: Update playstore metadatas

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      track:
        type: choice
        description: Track
        options:
          - production
          - internal

jobs:

  build:
    runs-on: ['self-hosted', 'tiny']
    env:
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # Setting up ruby on the machine
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: false
      # Gem caching
      - name: Gem caching
        id: gem_cache
        uses: actions/cache@v2
        continue-on-error: true
        with:
          path: vendor/bundle
          key: ${{ runner.os }}-gems-${{ hashFiles('**/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-gems-
      # Installing bundler to use fastlane on the machine
      - name: Setup fastlane
        id: set_up_fastlane
        run: |
          gem install bundler -v 2.4.22
          bundle config path vendor/bundle
          bundle install --jobs 4 --retry 3

      # https://stefma.medium.com/how-to-store-a-android-keystore-safely-on-github-actions-f0cef9413784
      - name: Restore secrets
        run: |
          echo "${{ secrets.GOOGLE_PLAY_CONSOLE_KEY }}" > google-play-console-key.json.asc
          gpg -d  --passphrase "${{ secrets.GOOGLE_PLAY_CONSOLE_KEY_PASSPHRASE }}" --batch google-play-console-key.json.asc > app/google-play-console-key.json
          echo "${{ secrets.SIGNING_STORE }}" > bereal.keystore.asc
          gpg -d  --passphrase "${{ secrets.SIGNING_STORE_PASSPHRASE }}" --batch bereal.keystore.asc > app/bereal.keystore

      - uses: google-github-actions/auth@v2
        with:
          create_credentials_file: 'true'
          workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-pool/providers/github-provider'
          service_account: '<EMAIL>'

      - name: Update last version metadatas on play store
        run: |
          bundle exec fastlane updateLastVersionMetadatas track:${{ github.event.inputs.track }}
