name: Run Tests

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  unit_tests:
    if: github.event.pull_request.draft == false
    runs-on: ['self-hosted', 'large']
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      - name: setupGradle
        run: ./scripts/ci/configure_ci_gradle_properties.sh

      - name: Unit tests with coverage
        run: ./gradlew :app:koverXmlReportProdDebug --warning-mode=none --quiet

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v3
        if: success() || failure() # always run even if the previous step fails
        with:
          report_paths: '**/build/test-results/**/TEST-*.xml'

      - name: Add coverage report to PR
        if: github.actor != 'dependabot[bot]'
        id: kover
        uses: mi-kas/kover-report@v1
        with:
          path: ${{ github.workspace }}/app/build/reports/kover/reportProdDebug.xml
          token: ${{ secrets.GITHUB_TOKEN }}
          title: Code Coverage
          update-comment: true
          min-coverage-overall: 10
          min-coverage-changed-files: 10
