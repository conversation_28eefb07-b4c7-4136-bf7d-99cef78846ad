name: Generate Release Candidate

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
    inputs:
      keepMapping:
        type: boolean
        description: Keep mapping
        default: false

jobs:
  buildAndDeployPlayStore:
    runs-on: ['self-hosted', 'large']
    env:
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup GooglePlay Console identifier
        # Extract the google console service account from our github-secrets
        # and write it inside `google-play-console-key.json` to be used by fastlane
        # this has been saved as base64 into the github secrets
        run: |
          echo "${{ secrets.GOOGLE_PLAY_CONSOLE_KEY }}" | base64 -d > app/google-play-console-key.json

      # https://stefma.medium.com/how-to-store-a-android-keystore-safely-on-github-actions-f0cef9413784
      - name: Retrieve keystore from secrets
        # Extract the keystore from our github-secrets
        run: |
          echo "${{ secrets.SIGNING_STORE }}" > bereal.keystore.asc
          gpg -d  --passphrase "${{ secrets.SIGNING_STORE_PASSPHRASE }}" --batch bereal.keystore.asc > app/bereal.keystore

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      # Setting up ruby on the machine
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: true

      - name: Create git profile
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "CI Bot"

      - name: Check %@ in strings.xml
        run: sh scripts/verif/findErrorsInLocales.sh

      - name: Second check on strings.xml
        run: python3 scripts/strings/check_strings.py platform/translations/src/main/res/

      - name: Extract existing version code and version name
        id: version_details
        run: |
          ./gradlew :app:writeVersionCode
          version_code=$(cat versionCode)
          version_name=$(grep versionName app/build.gradle.kts | head -1 | awk '{print $3}')

          echo "version_code=${version_code}" >> $GITHUB_OUTPUT
          echo "version_name=${version_name}" >> $GITHUB_OUTPUT

      - name: setupGradle
        run: ./scripts/ci/configure_ci_gradle_properties.sh

      # TO DEBUG : uncomment this to keep the .aab and skip the assemble
      #- uses: actions/cache/restore@v3
      #  id: cache
      #  with:
      #    path: app/build/outputs/bundle/prodRelease/app-prod-release.aab
      #    key: ${{ github.workflow }}-${{ github.ref }}

      - name: Assemble an AAB for Playstore
        #TO DEBUG : uncomment this to keep the .aab and skip the assemble
        #if: steps.cache.outputs.cache-hit != 'true'
        env:
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
        run: |
          bundle exec fastlane assemblePlayStoreVersion

      # TO DEBUG : uncomment this to keep the .aab and skip the assemble
      # keep in cache APK file
      #- uses: actions/cache/save@v3
      #  if: steps.cache.outputs.cache-hit != 'true'
      #  with:
      #    path: app/build/outputs/bundle/prodRelease/app-prod-release.aab
      #    key: ${{ github.workflow }}-${{ github.ref }}

      - name: Deploy an AAB to Playstore
        env:
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          DEBUG: 1
        run: |
          bundle exec fastlane deployPlayStore --verbose

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v3
        if: failure() # always run even if the previous step fails
        with:
          report_paths: '**/build/test-results/**/TEST-*.xml'

      - name: Tag version
        run: |
          git tag "rc/v${{steps.version_details.outputs.version_name}}-${{steps.version_details.outputs.version_code}}"
          git push --tags

      - name: Upload Manifest to Github Artifacts
        uses: actions/upload-artifact@v4
        if: ${{ github.event.inputs.keepMapping }}
        with:
          name: Prod Release Manifest
          path: app/build/outputs/mapping/prodRelease/mapping.txt