name: Test firebase connection

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  build:
    runs-on: ['self-hosted']
    permissions:
      contents: "read" # auth to gcloud
      id-token: "write" # auth to gcloud

    steps:
      - uses: actions/checkout@v4 # always before auth
      - uses: actions/setup-node@v4
        with:
          node-version: 18
      - run: npm install -g firebase-tools

      # Test sandbox
      - uses: google-github-actions/auth@v2
        with:
          create_credentials_file: 'true'
          workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-pool/providers/github-provider'
          service_account: '<EMAIL>'

      - run: |
          firebase projects:list

      # Test production
      - uses: google-github-actions/auth@v2
        with:
          create_credentials_file: 'true'
          workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-pool/providers/github-provider'
          service_account: '<EMAIL>'

      - run: |
          firebase projects:list

