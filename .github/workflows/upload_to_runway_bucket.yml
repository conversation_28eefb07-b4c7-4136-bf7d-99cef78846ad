name: Upload to Runway bucket

on:
  workflow_dispatch:
    inputs:
      bucket:
        type: string
        description: Runway Bucket
        default: ''

jobs:
  upload_to_runway:
    runs-on: ['self-hosted', 'large']
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      - name: setupGradle
        run: ./scripts/ci/configure_ci_gradle_properties.sh

      - name: Generate prod app distribution apk
        run: ./gradlew assembleProdAppDistribution

      - name: Upload apk to artifacts
        uses: actions/upload-artifact@v3
        with:
          name: app-prod-appDistribution
          path: app/build/outputs/apk/prod/appDistribution/app-prod-appDistribution.apk
