name: Coding Style

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  coding_style:
    if: github.event.pull_request.draft == false && github.actor != 'dependabot[bot]'
    runs-on: ['self-hosted', 'large']
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      - name: Check coding style
        run: ./gradlew ktlintCheck && ./gradlew detekt --continue

      - uses: yutailang0119/action-ktlint@v3
        with:
          report-path: "**/build/reports/ktlint/**/*.xml" # Support glob patterns by https://www.npmjs.com/package/@actions/glob
        continue-on-error: false # If annotations contain error of severity, action-ktlint exit 1.
