name: Check Pull Request Title

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened, edited, ready_for_review]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

permissions:
  pull-requests: read

jobs:
  main:
    name: Validate PR title
    runs-on: ['self-hosted', 'tiny']
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat:
            fix:
            docs:
            style:
            refact:
            perf:
            test:
            build:
            ci:
            revert:
            backmerge:
            release:
            bump:
          requireScope: false
          headerPattern: '^.*(feat:|fix:|docs:|style:|refact:|perf:|test:|build:|ci:|revert:|backmerge:|release:|bump:)(.+)$'
          headerPatternCorrespondence: type, subject