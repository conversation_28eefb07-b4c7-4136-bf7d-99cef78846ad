name: Retrieve Release Keystore

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  buildAndDeployPlayStore:
    runs-on: ['self-hosted', 'tiny']
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Create git profile
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "CI Bot"

      # https://stefma.medium.com/how-to-store-a-android-keystore-safely-on-github-actions-f0cef9413784
      - name: Retrieve keystore from github secrets
        run: |
          echo "${{ secrets.SIGNING_STORE }}" > bereal.keystore.asc
          gpg -d  --passphrase "${{ secrets.SIGNING_STORE_PASSPHRASE }}" --batch bereal.keystore.asc > app/bereal.keystore

      - name: Upload Keystore to Github Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: Release Keystore
          path: app/bereal.keystore

      - name: Write secrets into file
        run: |
          echo "storePassword=${{ secrets.SIGNING_STORE_PASSWORD }}" >> secrets.properties
          echo "keyAlias=${{ secrets.SIGNING_KEY_ALIAS }}" >> secrets.properties
          echo "keyPassword=${{ secrets.SIGNING_KEY_PASSWORD }}" >> secrets.properties

      - name: Upload secrets.properties to Github Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: secrets.properties
          path: secrets.properties
