name: Build External APK

on:
  # TODO launch after a release success (creation of tag starting by release_)
  workflow_dispatch:
    inputs:
      storeApkArtifact:
        type: boolean
        description: Keep APK as artifact
        default: true

jobs:

  build:
    runs-on: ['self-hosted', 'large']
    permissions:
      contents: "read" # auth to gcloud
      id-token: "write" # auth to gcloud
    env:
      SLACK_URL: ${{secrets.SLACK_URL}}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      # Setting up ruby on the machine
      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: true

      - name: Check %@ in strings.xml
        run: sh scripts/verif/findErrorsInLocales.sh

      - name: Check placeholder orders on strings.xml
        run: python3 scripts/strings/check_strings.py platform/translations/src/main/res/

      - name: Build an external apk
        env:
          ALLOW_VERSION_NAMES_FOLLOWING_GIT: true
          WITH_DEBUG_TOOLS: false
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          CI_ENABLE_MINIFICATION: true
          APP_DISTRIBUTION_DESCRIPTION: ${{ github.event.inputs.releaseNote }}
          SIGNING_EXTERNAL_STORE_PASSWORD: ${{ secrets.SIGNING_EXTERNAL_STORE_PASSWORD }}
          SIGNING_EXTERNAL_KEY_PASSWORD: ${{ secrets.SIGNING_EXTERNAL_KEY_PASSWORD }}
        run: |
          echo Compile with fastlane
          bundle exec fastlane buildExternal

      - name: Upload APK to Github Artifacts
        uses: actions/upload-artifact@v4
        if: ${{ github.event.inputs.storeApkArtifact }}
        with:
          name: Prod External APK
          path: app/build/outputs/apk/prod/external/app-prod-external.apk

      # TODO upload to the external app store (waiting for doc)
