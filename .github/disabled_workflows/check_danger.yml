name: Check Danger

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened, edited, ready_for_review]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  check-danger:
    if: github.event.pull_request.draft == false
    runs-on: ['self-hosted', 'tiny']
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Danger
        uses: docker://ghcr.io/danger/danger-kotlin:1.2.0
        with:
          args: --failOnErrors --no-publish-check
        env:
          GITHUB_TOKEN: ${{ secrets.DANGER_TOKEN }}
