name: Check Lin<PERSON>

on:
  push:
    tags:
      - 'v*'
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  check-lint:
    if: github.event.pull_request.draft == false
    runs-on: ['self-hosted', 'large']
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.10.0
        with:
          access_token: ${{ github.token }}

      - uses: actions/checkout@v4

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - uses: gradle/actions/setup-gradle@v4

      - name: setupGradle
        run: ./scripts/ci/configure_ci_gradle_properties.sh

      - name: lint
        run: ./gradlew lint
