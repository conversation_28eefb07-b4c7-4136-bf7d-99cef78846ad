version: 2
updates:
  - package-ecosystem: "gradle"
    directory: "/"
    schedule:
      interval: "daily"
      time: "05:00"
      timezone: "Europe/Paris"
    commit-message:
      prefix: "🤖build: [Dependabot]"
    groups:
      dependencies:
        patterns:
          - "*"
    ignore:
      - dependency-name: "com.github.yalantis:ucrop"
        versions: ["2.2.8-native", "2.2.9-native"]
      - dependency-name: "androidx.datastore:datastore-preferences"
        versions: ["1.1.0", "1.1.1"]
      - dependency-name: "androidx.biometric:biometric"
        versions: ["1.4.0-alpha01"]
