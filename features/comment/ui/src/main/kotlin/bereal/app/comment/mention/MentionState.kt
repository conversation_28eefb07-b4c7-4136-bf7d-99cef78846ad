package bereal.app.comment.mention

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import bereal.app.design.model.MentionnableUiModel
import timber.log.Timber
import kotlin.math.max
import kotlin.math.min

@Immutable
class MentionState {
    private var mentionStarted: MentionStart? = null

    private var lastValue: TextFieldValue? = null

    var state: State by mutableStateOf(State.NotMentioning)

    private val _mentioned: MutableState<List<MentionnableUiModel>> = mutableStateOf(listOf())
    val mentioned: androidx.compose.runtime.State<List<MentionnableUiModel>> = _mentioned

    fun onTextChanged(textFieldValue: TextFieldValue) {
        if (this.lastValue != textFieldValue) {
            if (mentionStarted == null) {
                mentionStarted = startMentioningIfNeeded(
                    text = textFieldValue.text,
                    selectionEnd = textFieldValue.selection.end,
                )
            }
            mentionStarted?.let {
                updateMentionState(text = textFieldValue.text, mentionStart = it)
            }
        }

        this.lastValue = textFieldValue
    }

    private fun updateMentionState(text: String, mentionStart: MentionStart) {
        try {
            if (mentionStart.startIndex >= text.length) {
                // mention finished
                reset()
                return
            }

            val startMentionIndexText = text.substring(startIndex = mentionStart.startIndex)
            val endMentionIndex =
                startMentionIndexText.indexOf(' ').takeIf { it != -1 }
                    ?: startMentionIndexText.length
            val mentionText =
                startMentionIndexText.substring(startIndex = 0, endIndex = endMentionIndex)

            val size = mentionText.length

            state = State.Mentioning(
                text = mentionText,
                endIndex = mentionStart.startIndex + size,
                startIndex = mentionStart.startIndex,
            )
        } catch (t: Throwable) {
            Timber.e(t)
            reset()
        }
    }

    private fun startMentioningIfNeeded(text: String, selectionEnd: Int): MentionStart? {
        return try {
            if (text.isEmpty() || selectionEnd == 0 || (state == State.NotMentioning && (text[selectionEnd - 1] == Typography.nbsp || text[selectionEnd - 1] == ' '))) return null
            val lastMentionText = text.split("@").lastOrNull()
            lastMentionText?.let {
                val alreadyMentioned = mentioned.value.lastOrNull { mention -> mention.userName == it }
                if (alreadyMentioned != null && alreadyMentioned.userName.length == it.length) return null

                val startIndex = (selectionEnd - 1) - it.length
                val endIndex = (selectionEnd - 1)
                when {
                    startIndex == endIndex -> MentionStart(startIndex = selectionEnd)
                    startIndex < 0 || endIndex < startIndex -> null
                    else -> MentionStart(startIndex = selectionEnd - it.length)
                }
            }
        } catch (e: Throwable) {
            Timber.e(e)
            null
        }
    }

    private fun reset() {
        mentionStarted = null
        state = State.NotMentioning
    }

    fun addNewMention(textFieldValue: TextFieldValue, mentionnable: MentionnableUiModel): TextFieldValue {
        return try {
            when (val s = state) {
                is State.Mentioning -> {
                    try {
                        if (!_mentioned.value.contains(mentionnable))
                            _mentioned.value = mentioned.value + mentionnable

                        val value = textFieldValue.annotatedString

                        if (s.endIndex > value.length) {
                            return textFieldValue
                        }

                        val textBeforeMention = value.substring(startIndex = 0, endIndex = max(0, s.startIndex - 1)) // minus the @
                        val textAfterMention = value.substring(startIndex = s.endIndex, endIndex = min(s.endIndex, value.length))

                        val userNameWithoutSpaces = mentionnable.userName.replace(' ', Typography.nbsp)
                        val textBeforeSelection = textBeforeMention + "@$userNameWithoutSpaces" + Typography.nbsp
                        val prefixAndMentionSize = textBeforeSelection.length
                        reset()

                        textFieldValue.copy(
                            text = textBeforeSelection + textAfterMention,
                            selection = TextRange(
                                start = prefixAndMentionSize,
                                end = prefixAndMentionSize,
                            ),
                        )
                    } catch (e: IllegalArgumentException) {
                        Timber.e(e)
                        textFieldValue
                    }
                }

                is State.NotMentioning -> {
                    // no-op
                    textFieldValue
                }
            }
        } catch (e: Throwable) {
            Timber.e(e)
            textFieldValue
        }
    }

    fun addReplyToMentioned(mentionnable: MentionnableUiModel, text: String) {
        val userNameWithAt = "@${mentionnable.userName}"
        val start = text.indexOf(userNameWithAt)

        if (start == -1) {
            Timber.e("MentionState.addReplyToMentioned: could not find @$mentionnable in text")
            return
        }

        val end = start + userNameWithAt.length

        state = State.Mentioning(
            text = mentionnable.userName,
            startIndex = start,
            endIndex = end,
        )

        addNewMention(TextFieldValue(text, TextRange(text.length)), mentionnable)
    }

    data class MentionStart(
        val startIndex: Int,
    )

    sealed interface State {
        data object NotMentioning : State
        data class Mentioning(
            val text: String,
            val startIndex: Int,
            val endIndex: Int,
        ) : State
    }
}
