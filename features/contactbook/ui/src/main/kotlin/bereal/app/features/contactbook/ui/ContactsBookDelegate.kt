package bereal.app.features.contactbook.ui

import bereal.app.common.Either
import bereal.app.common.combines
import bereal.app.common.list.RefreshType
import bereal.app.entities.CoreContact
import bereal.app.entities.RelationshipStatus
import bereal.app.features.contactbook.domain.models.ContactBookError
import bereal.app.features.contactbook.domain.usecases.ClearAddedContactsUseCase
import bereal.app.features.contactbook.domain.usecases.FetchActiveContactUseCase
import bereal.app.features.contactbook.domain.usecases.HideContactRecommendationsUseCase
import bereal.app.features.contactbook.domain.usecases.ObserveContactsNotOnBeRealUseCase
import bereal.app.features.contactbook.domain.usecases.ObserveContactsOnBeRealUseCase
import bereal.app.settings.model.SettingsFriendRecommendationsDomainModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import org.koin.core.annotation.Factory

@Factory
class ContactsBookDelegate(
    private val fetchActiveContactUseCase: FetchActiveContactUseCase,
    private val clearAddedContactsUseCase: ClearAddedContactsUseCase,
    private val hideContactRecommendationsUseCase: HideContactRecommendationsUseCase,
    observeContactsOnBeRealUseCase: ObserveContactsOnBeRealUseCase,
    observeContactsNotOnBeRealUseCase: ObserveContactsNotOnBeRealUseCase,
) {
    private val addedContactIds = MutableStateFlow<List<String>>(emptyList())
    val contactsOnBeReal: Flow<List<CoreContact>> =
        combines(observeContactsOnBeRealUseCase(), addedContactIds)
            .map { (contacts, addedIds) ->
                contacts.map {
                    it.copy(
                        associatedUser = it.associatedUser?.copy(
                            status = if (it.associatedUser?.uid in addedIds) {
                                RelationshipStatus.Sent
                            } else {
                                RelationshipStatus.Unknown
                            },
                        ),
                    )
                }
            }
    val contactsNotOnBeReal: Flow<List<CoreContact>> = observeContactsNotOnBeRealUseCase()

    suspend fun fetchActiveContactsOnBeReal(refreshType: RefreshType): Either<ContactBookError, Unit> {
        return fetchActiveContactUseCase(
            refreshType = refreshType,
            experiment = SettingsFriendRecommendationsDomainModel.DEFAULT_BACKEND_VARIANT,
        )
    }

    fun onContactOnBeRealAdded(userId: String) {
        addedContactIds.update { it + userId }
    }

    fun onContactOnBeRealRemoved(userId: String) {
        addedContactIds.update { it - userId }
    }

    suspend fun clearAddedContacts() {
        clearAddedContactsUseCase(addedContactIds.value)
        addedContactIds.update { emptyList() }
    }

    suspend fun removeContact(userId: String) {
        hideContactRecommendationsUseCase(userId)
    }
}
