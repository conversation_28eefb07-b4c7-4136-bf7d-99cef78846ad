package bereal.app.features.contactbook.data.utils

import bereal.app.entities.CoreContact
import org.koin.core.annotation.Factory
import java.security.MessageDigest

@Factory
class ContactBookEncoder {
    private val digest = MessageDigest.getInstance("SHA-256")

    @OptIn(ExperimentalStdlibApi::class)
    fun hashPhoneNumber(phoneNumber: String): String {
        val hashedByteArray = digest.digest(phoneNumber.encodeToByteArray())
        return hashedByteArray.toHexString()
    }

    fun computeContactBookHashGraph(contacts: List<CoreContact>): String {
        val string = contacts.sortedBy { it.phoneNumber }
            .joinToString("|") { "${it.name}:${it.phoneNumber}:${!it.photoUri.isNullOrEmpty()}" }

        val hashBytes = digest.digest(string.toByteArray(Charsets.UTF_8))

        return hashBytes.joinToString("") { "%02x".format(it) }
    }
}
