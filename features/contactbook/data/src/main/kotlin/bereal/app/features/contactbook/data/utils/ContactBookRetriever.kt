package bereal.app.features.contactbook.data.utils

import android.content.Context
import android.telephony.PhoneNumberUtils
import bereal.app.common.Either
import bereal.app.common.Failure
import bereal.app.common.Success
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.CoreContact
import bereal.app.entities.error.GenericError
import com.alexstyl.contactstore.ContactColumn
import com.alexstyl.contactstore.ContactStore
import com.alexstyl.contactstore.thumbnailUri
import com.google.i18n.phonenumbers.PhoneNumberUtil
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory
import timber.log.Timber
import java.util.Locale

@Factory
class ContactBookRetriever(
    context: Context,
    private val dispatcherProvider: DispatcherProvider,
    private val contactBookEncoder: ContactBookEncoder,
) {

    private val defaultCountry = Locale.getDefault().country
    private var countryCode: String? = null
    private var userPhoneNumber: String? = null
    private val store = ContactStore.newInstance(context)
    private val phoneNumberUtils = PhoneNumberUtil.getInstance()

    suspend fun getDeviceContactBook(): Either<GenericError, List<CoreContact>> {
        return withContext(dispatcherProvider.data) {
            try {
                val contacts = store.fetchContacts(
                    columnsToFetch = listOf(
                        ContactColumn.Names,
                        ContactColumn.Image,
                        ContactColumn.Phones,
                    ),
                ).blockingGet().mapNotNull { contact ->
                    mapToCoreContact(
                        contact,
                        countryCode ?: defaultCountry,
                        userPhoneNumber ?: "",
                    )
                }
                Success(contacts)
            } catch (t: Throwable) {
                Timber.e(t)
                Failure(GenericError.Unhandled(t))
            }
        }
    }

    private fun mapToCoreContact(
        contact: com.alexstyl.contactstore.Contact,
        countryCode: String,
        userPhoneNumber: String,
    ): CoreContact? {
        val displayName = contact.displayName
        val name = if (contact.phones.any { it.value.raw == displayName }) {
            ""
        } else {
            displayName
        }
        val image = contact.thumbnailUri.toString()
        if (contact.phones.isEmpty()) return null
        val phoneNumbers = contact.phones.mapNotNull phoneLoop@{
            val formattedPhoneNumber =
                PhoneNumberUtils.formatNumberToE164(
                    it.value.raw,
                    countryCode,
                )
            formattedPhoneNumber ?: return@phoneLoop null
            if (formattedPhoneNumber == userPhoneNumber) return@phoneLoop null
            phoneNumberUtils.parse(formattedPhoneNumber, countryCode)
                .also {
                    val type = phoneNumberUtils.getNumberType(it)
                    if (type != PhoneNumberUtil.PhoneNumberType.FIXED_LINE_OR_MOBILE &&
                        type != PhoneNumberUtil.PhoneNumberType.MOBILE
                    ) return@phoneLoop null
                }
            formattedPhoneNumber
        }.distinct()
        return if (phoneNumbers.isEmpty()) {
            null
        } else {
            CoreContact(
                name = name,
                photoUri = image,
                phoneNumber = phoneNumbers.first(),
                hashedPhoneNumber = contactBookEncoder.hashPhoneNumber(phoneNumbers.first()),
                associatedUser = null,
            )
        }
    }
}
