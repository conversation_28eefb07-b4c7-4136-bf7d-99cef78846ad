package bereal.app.features.contactbook.data

import bereal.app.common.Either
import bereal.app.common.Failure
import bereal.app.common.list.RefreshType
import bereal.app.data.core.ThrottleJobsManager
import bereal.app.entities.ContactBookInfo
import bereal.app.entities.CoreContact
import bereal.app.entities.RelationshipStatus
import bereal.app.entities.error.GenericError
import bereal.app.entities.error.throwable
import bereal.app.features.contactbook.data.local.ContactBookLocalDataSource
import bereal.app.features.contactbook.data.models.UploadContact
import bereal.app.features.contactbook.data.remote.ContactBookRemoteDataSource
import bereal.app.features.contactbook.data.utils.ContactBookEncoder
import bereal.app.features.contactbook.data.utils.ContactBookRetriever
import bereal.app.features.contactbook.domain.ContactBookRepository
import bereal.app.features.contactbook.domain.models.ContactBookError
import bereal.app.features.friendrecommendations.data.remote.FriendRecommendationsRemoteDataSource
import bereal.app.features.friendrecommendations.domain.FriendRecommendationsRepository
import bereal.app.permissions.PermissionManager
import bereal.app.permissions.model.BeRealPermission
import bereal.app.remote.logger.RemoteLogger
import bereal.app.remote.logger.ext.logError
import bereal.app.remote.logger.ext.logFailureWithParams
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Single
import timber.log.Timber

@Single
class ContactBookRepositoryImpl(
    private val remoteDataSource: ContactBookRemoteDataSource,
    private val localDataSource: ContactBookLocalDataSource,
    private val contactBookEncoder: ContactBookEncoder,
    private val contactBookRetriever: ContactBookRetriever,
    private val permissionManager: PermissionManager,
    private val throttleJobsManager: ThrottleJobsManager,
    private val friendRecommendationsRemoteDataSource: FriendRecommendationsRemoteDataSource,
    private val remoteLogger: RemoteLogger,
    private val friendRecommendationRepository: FriendRecommendationsRepository,
) : ContactBookRepository {

    override suspend fun fetchActiveContacts(
        refreshType: RefreshType,
        countryCode: String?,
        userPhoneNumber: String?,
        experiment: String,
    ): Either<ContactBookError, Unit> {
        val isContactPermissionAccepted: Boolean = isContactPermissionAccepted()

        if (!isContactPermissionAccepted) {
            return Failure(ContactBookError.PermissionNotGranted)
        }

        return throttleJobsManager.throttle(
            refreshType = refreshType,
            jobTag = "LOAD_FRIEND_RECOMMENDATIONS_CONTACTS_JOB",
        ) {
            friendRecommendationsRemoteDataSource.getActiveContacts(experiment = experiment)
                .mapSuccess { activeContacts ->
                    if (activeContacts.isNotEmpty()) {
                        val currentContacts = localDataSource.getAllContacts()
                        val contactsOnBeReal = activeContacts.mapNotNull { activeContact ->
                            currentContacts.firstOrNull { it.hashedPhoneNumber == activeContact.user.hashedPhoneNumber }
                                ?.hashedPhoneNumber
                                ?.let { hashedPhoneNumber -> hashedPhoneNumber to activeContact.user }
                        }.toMap()

                        localDataSource.matchContactWithBeRealUser(contactsOnBeReal)
                    }
                }
        }
            .mapFailure {
                remoteLogger.logError(TAG, "fetchActiveContacts", it.throwable)
                ContactBookError.Generic(it)
            }
    }

    override suspend fun uploadContactBook(): Either<ContactBookError, Unit> {
        return contactBookRetriever.getDeviceContactBook()
            .mapFailure {
                Timber.e("error getting contacts on device: ${it.throwable.message}")
                ContactBookError.Generic(it)
            }
            .mapSuccess { contacts ->
                val contactHashGraph = contactBookEncoder.computeContactBookHashGraph(contacts)
                localDataSource.saveAllContacts(contacts = contacts, graphHash = contactHashGraph)
                val uploadContacts = contacts.map { contact ->
                    UploadContact(
                        displayName = contact.name,
                        hashedPhoneNumber = contact.hashedPhoneNumber,
                        hasProfilePicture = !contact.photoUri.isNullOrBlank(),
                    )
                }
                remoteDataSource.uploadContactBook(uploadContacts)
            }
    }

    override suspend fun hideContactRecommendation(
        userId: String,
    ): Either<GenericError, Unit> {
        val contact = localDataSource.getContactWithUserId(userId)
            ?: return Failure(GenericError.Unhandled("Contact with userId $userId not found"))

        localDataSource.removeContactRecommendation(userId)

        return friendRecommendationsRemoteDataSource.hideFriendRecommendation(userId).alsoFailure {
            localDataSource.saveContact(contact)
        }
            .logFailureWithParams(remoteLogger, TAG, "hideContactRecommendation", userId)
    }

    override suspend fun getContactBookInfo(): ContactBookInfo? {
        return localDataSource.getContactBookInfo()
    }

    override suspend fun computeCurrentContactBookHash(): Either<ContactBookError, String> {
        val isContactPermissionAccepted: Boolean = isContactPermissionAccepted()

        if (!isContactPermissionAccepted) {
            return Failure(ContactBookError.PermissionNotGranted)
        }

        return contactBookRetriever.getDeviceContactBook()
            .mapSuccess { contactBookEncoder.computeContactBookHashGraph(it) }
            .mapFailure { ContactBookError.Generic(it) }
    }

    override suspend fun clearAddedContacts(userIds: List<String>) {
        return localDataSource.clearAddedContacts(userIds)
    }

    override suspend fun removeUserFromContact(userId: String) {
        return localDataSource.removeUserFromContact(userId)
    }

    override fun observeContactNotOnBeReal(): Flow<List<CoreContact>> {
        return localDataSource.observeContactsNotOnBeReal()
    }

    override fun observeContactOnBeReal(): Flow<List<CoreContact>> {
        return localDataSource.observeContactsOnBeReal().map {
            it.filter { contact ->
                (
                    contact.associatedUser?.status == RelationshipStatus.Unknown ||
                        contact.associatedUser?.status == RelationshipStatus.Sent
                    )
            }
        }
    }

    override suspend fun getAllContacts(): List<CoreContact> {
        return localDataSource.getAllContacts()
    }

    private suspend fun isContactPermissionAccepted(): Boolean {
        val isDeviceContactPermissionAccepted =
            permissionManager.isPermissionAccepted(BeRealPermission.Contact).first().accepted

        val isServerContactPermissionAccepted =
            friendRecommendationRepository.observeFriendRecommendationsSettings()
                .firstOrNull()?.noContactsSharing != true

        return isDeviceContactPermissionAccepted && isServerContactPermissionAccepted
    }

    companion object {
        private const val TAG = "ContactBookRepositoryImpl"
    }
}
