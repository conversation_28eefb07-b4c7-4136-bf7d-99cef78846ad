plugins {
    id(libs.plugins.bereal.data.impl.get().pluginId)
}

android {
    namespace = "bereal.app.features.contactbook.data"
}

dependencies {
    implementation(projects.features.myUser.domain)
    implementation(projects.features.contactbook.domain)
    implementation(projects.features.friendRecommendations.data)
    implementation(projects.features.friendRecommendations.domain)

    implementation(projects.platform.data.coreStorage.domain)

    implementation(projects.platform.permissions.core)
    implementation(projects.platform.common)
    implementation(projects.platform.entities)
    implementation(projects.platform.remoteLogger.remoteLogger)

    implementation(libs.contactStore)
    implementation(libs.libphonenumber)
}
