package bereal.app.features.contactbook.domain.usecases

import bereal.app.entities.CoreContact
import bereal.app.features.contactbook.domain.ContactBookRepository
import kotlinx.coroutines.flow.Flow
import org.koin.core.annotation.Factory

@Factory
class ObserveContactsNotOnBeRealUseCase(
    private val contactBookRepository: ContactBookRepository,
) {

    operator fun invoke(): Flow<List<CoreContact>> = contactBookRepository.observeContactNotOnBeReal()
}
