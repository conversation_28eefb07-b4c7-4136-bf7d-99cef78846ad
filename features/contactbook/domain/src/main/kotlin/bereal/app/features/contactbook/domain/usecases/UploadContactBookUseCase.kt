package bereal.app.features.contactbook.domain.usecases

import bereal.app.common.Either
import bereal.app.common.Success
import bereal.app.common.failure
import bereal.app.features.contactbook.domain.ContactBookRepository
import bereal.app.features.contactbook.domain.models.ContactBookError
import bereal.app.time.provider.BeRealTimeProvider
import org.koin.core.annotation.Factory
import java.util.concurrent.TimeUnit

/*
scenarios to upload the contacts:
 * 1. No contact book info available (first time) or Force refresh is requested
 * 2. Contact book has changed (different hash)
 * 3. The last contacts upload was more than 3 days ago
*/
@Factory
class UploadContactBookUseCase(
    private val repository: ContactBookRepository,
    private val beRealTimeProvider: BeRealTimeProvider,
) {
    suspend operator fun invoke(forceRefresh: Boolean = false): Either<ContactBookError, Unit> {
        val contactBookInfo = repository.getContactBookInfo()
        val now = beRealTimeProvider.currentTimeMs
        val contactBookHasChanged = repository.computeCurrentContactBookHash()
            .fold(
                doOnSuccess = { contactBookInfo?.graphHash != it },
                doOnFailure = {
                    when (it) {
                        // if somehow we did not manage to compute the hash, we keep going in order to check other conditions
                        is ContactBookError.Generic -> false
                        // if contact permission is not granted, we exit immediately
                        ContactBookError.PermissionNotGranted -> return it.failure()
                    }
                },
            )

        return if (contactBookInfo == null || forceRefresh || contactBookHasChanged ||
            (now - contactBookInfo.lastFetchedAt > MINIMUM_SEND_CONTACT_INTERVAL)
        ) {
            repository.uploadContactBook()
        } else {
            Success(Unit)
        }
    }

    companion object {
        private val MINIMUM_SEND_CONTACT_INTERVAL = TimeUnit.DAYS.toMillis(3)
    }
}
