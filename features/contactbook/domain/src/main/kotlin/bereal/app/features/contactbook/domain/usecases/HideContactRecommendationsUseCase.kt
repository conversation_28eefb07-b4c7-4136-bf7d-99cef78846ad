package bereal.app.features.contactbook.domain.usecases

import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import bereal.app.features.contactbook.domain.ContactBookRepository
import org.koin.core.annotation.Factory

@Factory
class HideContactRecommendationsUseCase(
    private val repository: ContactBookRepository,
) {
    suspend operator fun invoke(userId: String): Either<GenericError, Unit> =
        repository.hideContactRecommendation(userId = userId)
}
