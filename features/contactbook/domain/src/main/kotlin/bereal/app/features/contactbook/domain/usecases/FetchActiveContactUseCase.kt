package bereal.app.features.contactbook.domain.usecases

import bereal.app.common.Either
import bereal.app.common.list.RefreshType
import bereal.app.features.contactbook.domain.ContactBookRepository
import bereal.app.features.contactbook.domain.models.ContactBookError
import bereal.app.user.usecases.GetMyUserPhoneNumberUseCase
import bereal.app.user.usecases.GetMyUserUseCase
import org.koin.core.annotation.Factory

@Factory
class FetchActiveContactUseCase(
    private val repository: ContactBookRepository,
    private val getMyUserUseCase: GetMyUserUseCase,
    private val getMyUserPhoneNumberUseCase: GetMyUserPhoneNumberUseCase,
) {
    suspend operator fun invoke(
        refreshType: RefreshType,
        experiment: String,
    ): Either<ContactBookError, Unit> {
        return repository.fetchActiveContacts(
            refreshType = refreshType,
            countryCode = getMyUserUseCase().countryCode,
            userPhoneNumber = getMyUserPhoneNumberUseCase(),
            experiment = experiment,
        )
    }
}
