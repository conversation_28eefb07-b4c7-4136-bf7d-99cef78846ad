plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.compose.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
}

android {
    namespace = "bereal.features.berealview.ui"
}

dependencies {
    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.design.core)
    implementation(projects.platform.image.coreUi)
    implementation(projects.platform.image.remoteImage.core)
    implementation(projects.platform.haptic.core)
    implementation(projects.platform.translations)
    implementation(projects.platform.entities)

    implementation(projects.features.bts.domain)
    implementation(projects.features.bts.ui)
    implementation(projects.features.video.ui)

    implementation(libs.exoplayer)

    implementation(libs.androidx.constraintLayout)
    implementation(libs.lottie)
    implementation(libs.media3.common)
}
