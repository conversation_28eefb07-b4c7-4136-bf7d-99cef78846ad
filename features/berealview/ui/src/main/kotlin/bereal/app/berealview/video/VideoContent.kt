package bereal.app.berealview.video

import androidx.compose.animation.Crossfade
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import kotlinx.collections.immutable.persistentListOf

@Composable
internal fun VideoContent(
    modifier: Modifier = Modifier.fillMaxSize(),
    player: StablePlayer,
    playerState: BeRealVideoPlayerState,
    imageLoader: StableImageLoader,
    onEvent: (interacting: Boolean) -> Unit = { },
    onVideoClick: (() -> Unit)?,
    stopClick: () -> Unit,
    playButtonState: PlayButtonState,
    muteButtonState: MuteButtonState,
    containerDataId: String,
    muteButtonPadding: PaddingValues,
    thumbnailData: BeRealViewDataUiModel?,
    shouldBlurThumbnail: Boolean,
    overrideOtherPlayer: Boolean,
    progressBar: ProgressBarState,
) {
    val rememberedState = remember(playerState) {
        playerState
    }

    val lifecycleOwner = rememberUpdatedState(LocalLifecycleOwner.current)

    DisposableEffect(containerDataId, stopClick) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_PAUSE) {
                stopClick()
            }
        }
        val lifecycle = lifecycleOwner.value.lifecycle
        lifecycle.addObserver(observer)

        onDispose {
            stopClick()
            lifecycle.removeObserver(observer)
        }
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        VideoThumbnail(
            shouldBlur = shouldBlurThumbnail,
            thumbnailData = thumbnailData,
            imageLoader = imageLoader,
        )

        when (rememberedState) {
            is BeRealVideoPlayerState.Playing, BeRealVideoPlayerState.Paused, BeRealVideoPlayerState.NearStopped -> {
                PlayingOrPausedState(
                    player = player,
                    playerState = playerState,
                    onEvent = onEvent,
                    playButtonState = playButtonState,
                    muteButtonState = muteButtonState,
                    onVideoClick = onVideoClick,
                    containerDataId = containerDataId,
                    muteButtonPadding = muteButtonPadding,
                    overrideOtherPlayer = overrideOtherPlayer,
                )
            }

            is BeRealVideoPlayerState.Loading -> {
                LoadingState(
                    muteButtonState = muteButtonState,
                    muteButtonPadding = muteButtonPadding,
                )
            }

            is BeRealVideoPlayerState.Stopped -> {
                StopState(
                    playButtonState = playButtonState,
                    muteButtonState = muteButtonState,
                    muteButtonPadding = muteButtonPadding,
                )
                onEvent(false)
            }
        }

        ProgressIfPaused(
            modifier = Modifier.fillMaxWidth(),
            playerState = playerState,
            progressBar = progressBar,
            player = player,
        )
    }
}

@Composable
private fun ProgressIfPaused(
    playerState: BeRealVideoPlayerState,
    progressBar: ProgressBarState,
    player: StablePlayer,
    modifier: Modifier = Modifier,
) {
    val progressBarIfPaused = remember(playerState, progressBar) {
        when (progressBar) {
            ProgressBarState.Hidden -> progressBar
            ProgressBarState.Visible -> progressBar.takeIf {
                when (playerState) {
                    BeRealVideoPlayerState.Paused -> true
                    BeRealVideoPlayerState.Loading,
                    BeRealVideoPlayerState.NearStopped,
                    BeRealVideoPlayerState.Playing,
                    BeRealVideoPlayerState.Stopped,
                    -> false
                }
            } ?: ProgressBarState.Hidden
        }
    }
    Crossfade(
        targetState = progressBarIfPaused,
        modifier = modifier,
        animationSpec = tween(200, easing = LinearEasing),
        label = "progressBar",
    ) { progressBar ->
        when (progressBar) {
            ProgressBarState.Hidden -> {
                // no op
            }

            ProgressBarState.Visible -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = remember {
                                Brush.verticalGradient(
                                    colors = persistentListOf(
                                        Color.Black.copy(alpha = 0f),
                                        Color.Black.copy(alpha = 0.3f),
                                    ),
                                )
                            },
                        ),
                ) {
                    PlayerProgressBar(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .padding(all = 30.dp)
                            .height(8.dp),
                        player = player,
                    )
                }
            }
        }
    }
}
