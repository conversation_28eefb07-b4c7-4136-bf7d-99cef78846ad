package bereal.app.dualview.dualvideo.players.singleplayer

import android.annotation.SuppressLint
import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.VideoDurationUiModel
import bereal.app.bts.ui.model.toUi
import bereal.app.bts.ui.player.SimpleBeRealVideoPlayer
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

@SuppressLint("UnsafeOptInUsageError")
@Immutable
internal data class DualViewPlayerTwoVideosImpl(
    private val scope: CoroutineScope,
    private val simpleBeRealVideoPlayer: SimpleBeRealVideoPlayer,
    private val isPrimary: Boolean,
) : DualViewPlayer {
    override fun toggleMuted() {
        simpleBeRealVideoPlayer.toggleMute()
    }

    override val playerType = DualViewPlayers.PlayerType.DualVideo

    override val longPressToPause: Boolean = true

    override val playerMuteState = simpleBeRealVideoPlayer.playerMuteState

    override val exoplayer: StablePlayer =
        StablePlayer(if (isPrimary) simpleBeRealVideoPlayer.primaryPlayer else simpleBeRealVideoPlayer.secondaryPlayer)

    override val playerState: StateFlow<BeRealVideoPlayerState> = flowOf(isPrimary)
        .flatMapLatest {
            if (it) {
                simpleBeRealVideoPlayer.primaryPlayerState
            } else {
                simpleBeRealVideoPlayer.secondaryPlayerState
            }
        }
        .stateIn(scope, SharingStarted.Companion.Lazily, BeRealVideoPlayerState.Loading)

    override val primaryPlayerDuration: Flow<VideoDurationUiModel> = simpleBeRealVideoPlayer.primaryDuration.map {
        toUi(it)
    }
}
