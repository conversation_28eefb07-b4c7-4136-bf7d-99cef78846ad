package bereal.app.berealview

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.media3.common.util.UnstableApi
import bereal.app.berealview.bts.BtsOverlay
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.image.BeRealImageType
import bereal.app.berealview.image.BeRealImageViewFromGallery
import bereal.app.berealview.image.rememberBeRealViewState
import bereal.app.berealview.primary.PrimaryView
import bereal.app.berealview.secondary.SecondaryView
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.bts.ui.LocalBTSVideoPlayer
import bereal.app.bts.ui.model.BtsVideoUiModel
import bereal.app.bts.ui.model.toVideoData
import bereal.app.bts.ui.player.BTSVideoPlayer
import bereal.app.bts.ui.view.BtsView
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent
import bereal.app.design.berealimageview.model.isMediaFromGallery
import bereal.app.design.berealimageview.model.toVideoData
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.isInPreview
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.model.StablePlayer

@Composable
fun BeRealView(
    modifier: Modifier = Modifier,
    primaryViewData: BeRealViewDataUiModel,
    secondaryViewData: BeRealViewDataUiModel?,
    btsContent: BtsVideoUiModel?,
    state: BeRealViewUiState = rememberBeRealViewState(),
    areImagesBlurred: Boolean = false,
    allowHardware: Boolean = true, // for taking screenshot of the view https://github.com/coil-kt/coil/issues/159
    imageLoader: StableImageLoader,
    primaryImagePlaceholder: String? = null,
    secondaryImagePlaceholder: String? = null,
    withLoading: Boolean = true,
    withPrimaryBorder: () -> Boolean = { false },
    onImagePropertiesChange: ((BeRealImageProperties) -> Unit)? = null,
    sustainedLook: Boolean = false,
    videoActionsPadding: PaddingValues = PaddingValues(),
    design: BeRealViewDesign = BeRealViewDesign.V1,
) {
    val stateEvents = remember { state.eventsCollectors }
    var imageProperties by remember { mutableStateOf(BeRealImageProperties(0f, design = design)) }
    val visibleLargeImage by state.visibleLargeContent

    LaunchedEffect(imageProperties, onImagePropertiesChange) {
        stateEvents.onImagePropertiesChanged(imageProperties, primaryViewData.aspectRatio)
        onImagePropertiesChange?.let { onImagePropertiesChange(imageProperties) }
    }

    BoxWithConstraints(
        modifier,
    ) {
        imageProperties = BeRealImageProperties(
            primaryWidthDp = this.maxWidth.value,
            design = design,
            type = when {
                else -> BeRealImageType.Regular
            },
        )

        if (primaryViewData.containerData.isMediaFromGallery()) {
            BeRealImageViewFromGallery(
                primaryViewData = primaryViewData,
                state = state,
                areImagesBlurred = areImagesBlurred,
                allowHardware = allowHardware,
                imageLoader = imageLoader,
                primaryImagePlaceholder = null,
                imageProperties = imageProperties,
                withLoading = withLoading,
                withPrimaryBorder = withPrimaryBorder,
                videoActionsPadding = videoActionsPadding,
            )
        } else {
            BeRealStackView(
                primaryViewData = primaryViewData,
                secondaryViewData = secondaryViewData,
                btsVideoUiModel = btsContent,
                shouldBlurImage = areImagesBlurred,
                allowHardware = allowHardware,
                imageLoader = imageLoader,
                imageProperties = imageProperties,
                visibleLargeImage = visibleLargeImage,
                state = state,
                primaryImagePlaceholder = primaryImagePlaceholder,
                secondaryImagePlaceholder = secondaryImagePlaceholder,
                withLoading = withLoading,
                withPrimaryBorder = withPrimaryBorder,
                videoActionsPadding = videoActionsPadding,
                isSustainedLookBtsPost = sustainedLook,
            )
        }
    }
}

@Composable
private fun BeRealViewForPreview(
    imageProperties: BeRealImageProperties?,
    modifier: Modifier = Modifier,
    secondaryImage: BeRealViewDataUiModel?,
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(imageProperties?.primaryRadiusDp?.dp ?: 12.dp))
            .background(Color.Gray)
            .fillMaxSize(),
    ) {
        secondaryImage?.let {
            Box(
                modifier = Modifier
                    .padding(
                        top = imageProperties?.secondaryMarginDp?.dp ?: 8.dp,
                        start = imageProperties?.secondaryMarginDp?.dp ?: 8.dp,
                    )
                    .clip(RoundedCornerShape(imageProperties?.secondaryRadiusDp?.dp ?: 8.dp))
                    .width(imageProperties?.secondaryWidthDp?.dp ?: 80.dp)
                    .aspectRatio(secondaryImage.aspectRatio)
                    .background(Color.DarkGray),
            )
        }
    }
}

@Preview
@Preview(heightDp = 300)
@Composable
private fun BeRealViewForPreview_Preview() {
    val primaryImage = BeRealViewDataUiModel.preview(url = "")
    BeRealTheme {
        BeRealViewForPreview(
            imageProperties = null,
            secondaryImage = BeRealViewDataUiModel.preview(url = ""),
            modifier = Modifier.aspectRatio(primaryImage.aspectRatio),
        )
    }
}

@Preview
@Preview(heightDp = 300)
@Composable
private fun BeRealView_WithoutSecondary_Preview() {
    val primaryImage = BeRealViewDataUiModel.preview(url = "")
    BeRealTheme {
        BeRealViewForPreview(
            imageProperties = null,
            secondaryImage = null,
            modifier = Modifier.aspectRatio(primaryImage.aspectRatio),
        )
    }
}

@UnstableApi
@Composable
fun BeRealView(
    modifier: Modifier = Modifier,
    state: BeRealViewUiState,
    primaryImage: BeRealViewDataUiModel,
    secondaryImage: BeRealViewDataUiModel?,
    btsVideoUiModel: BtsVideoUiModel?,
    shouldBlurImage: Boolean = false,
    allowHardware: Boolean = true, // for taking screenshot of the view https://github.com/coil-kt/coil/issues/159
    imageLoader: StableImageLoader,
    imageProperties: BeRealImageProperties,
    withLoading: Boolean = true,
    withPrimaryBorder: () -> Boolean = { false },
    isSustainedLookBtsPost: Boolean = false,
    videoActionsPadding: PaddingValues = PaddingValues(),
) {
    if (isInPreview) {
        BeRealViewForPreview(
            modifier = modifier,
            imageProperties = imageProperties,
            secondaryImage = secondaryImage,
        )
        return
    }
    val stateEvents = remember { state.eventsCollectors }
    val visibleLargeImage by state.visibleLargeContent

    val beRealImageProperties by remember(imageProperties) {
        mutableStateOf(
            imageProperties.copy(
                type = BeRealImageType.Regular,
            ),
        )
    }

    LaunchedEffect(beRealImageProperties) {
        stateEvents.onImagePropertiesChanged(beRealImageProperties, primaryImage.aspectRatio)
    }

    if (primaryImage.containerData.isMediaFromGallery()) {
        BeRealImageViewFromGallery(
            modifier = modifier,
            primaryViewData = primaryImage,
            state = state,
            areImagesBlurred = shouldBlurImage,
            allowHardware = allowHardware,
            imageLoader = imageLoader,
            primaryImagePlaceholder = null,
            imageProperties = beRealImageProperties,
            withLoading = withLoading,
            withPrimaryBorder = withPrimaryBorder,
            videoActionsPadding = videoActionsPadding,
        )
    } else {
        Box(
            modifier,
        ) {
            BeRealStackView(
                primaryViewData = primaryImage,
                secondaryViewData = secondaryImage,
                btsVideoUiModel = btsVideoUiModel,
                shouldBlurImage = shouldBlurImage,
                allowHardware = allowHardware,
                imageLoader = imageLoader,
                imageProperties = beRealImageProperties,
                visibleLargeImage = visibleLargeImage,
                state = state,
                withLoading = withLoading,
                withPrimaryBorder = withPrimaryBorder,
                videoActionsPadding = videoActionsPadding,
                isSustainedLookBtsPost = isSustainedLookBtsPost,
            )
        }
    }
}

@UnstableApi
@Composable
private fun BeRealStackView(
    state: BeRealViewUiState,
    primaryViewData: BeRealViewDataUiModel,
    secondaryViewData: BeRealViewDataUiModel?,
    btsVideoUiModel: BtsVideoUiModel?,
    shouldBlurImage: Boolean,
    videoActionsPadding: PaddingValues,
    allowHardware: Boolean, // for taking screenshot of the view https://github.com/coil-kt/coil/issues/159
    imageLoader: StableImageLoader,
    imageProperties: BeRealImageProperties,
    visibleLargeImage: VisibleBerealViewLargeContent,
    primaryImagePlaceholder: String? = null,
    secondaryImagePlaceholder: String? = null,
    withPrimaryBorder: () -> Boolean,
    withLoading: Boolean,
    isSustainedLookBtsPost: Boolean,
) {
    val btsVideoPlayer: BTSVideoPlayer? = btsVideoUiModel?.let {
        val player: BTSVideoPlayer = LocalBTSVideoPlayer.current

        DisposableEffect(Unit) {
            onDispose {
                player.stop()
            }
        }
        player
    }

    val shouldPlay = state.btsState?.shouldPlay?.value
    LaunchedEffect(shouldPlay) {
        if (shouldPlay == true) {
            btsVideoPlayer?.play(btsVideoUiModel.toVideoData())
        } else {
            btsVideoPlayer?.stop()
        }
    }

    val isPrimaryImageTransparent by remember {
        state.primary.isPrimaryImageTransparent
    }

    val isSecondaryVisible by remember {
        state.secondary.isVisible
    }

    if (btsVideoUiModel != null && btsVideoPlayer != null && shouldPlay == true) {
        BtsView(
            containerId = btsVideoUiModel.containerData.id,
            aspectRatio = primaryViewData.aspectRatio, // use the primaryViewData aspect ratio to prevent having a video with a different size / scale that its container, it will then center crom
            player = StablePlayer(btsVideoPlayer.player),
            cornerRadiusInDp = imageProperties.primaryRadiusDp.dp,
        )
    }

    PrimaryView(
        primaryData = when (visibleLargeImage) {
            VisibleBerealViewLargeContent.Primary -> primaryViewData
            VisibleBerealViewLargeContent.Secondary -> when (secondaryViewData) {
                null -> primaryViewData // fallback to primaryImage if secondary is null
                is BeRealViewDataUiModel.Video -> {
                    // TODO altering data like that is not the best option, should be done in the view state mapper
                    // I hate this part of code
                    (primaryViewData as? BeRealViewDataUiModel.Video)?.videoSettings?.let {
                        secondaryViewData.copy(
                            videoSettings = it,
                        )
                    } ?: secondaryViewData
                }

                else -> secondaryViewData
            }
        },
        secondaryData = when (visibleLargeImage) {
            VisibleBerealViewLargeContent.Primary -> secondaryViewData ?: primaryViewData // fallback to primaryImage if secondary is null
            VisibleBerealViewLargeContent.Secondary -> primaryViewData
        },
        shouldBlurImage = shouldBlurImage,
        isPrimaryImageTransparent = isPrimaryImageTransparent,
        allowHardware = allowHardware,
        imageLoader = imageLoader,
        state = state,
        imageProperties = imageProperties,
        imagePlaceholder = primaryImagePlaceholder,
        withLoading = withLoading,
        withPrimaryBorder = withPrimaryBorder,
        muteButtonPadding = videoActionsPadding,
    )

    secondaryViewData?.let { ensuredSecondaryImage ->
        SecondaryView(
            data = when (visibleLargeImage) {
                VisibleBerealViewLargeContent.Primary -> ensuredSecondaryImage
                VisibleBerealViewLargeContent.Secondary -> {
                    // TODO altering data like that is not the best option, should be done in the view state mapper
                    when (primaryViewData) {
                        is BeRealViewDataUiModel.Video -> {
                            primaryViewData.copy(
                                videoSettings = primaryViewData.videoSettings.copy(
                                    isPlayable = (ensuredSecondaryImage as? BeRealViewDataUiModel.Video)?.videoSettings?.isPlayable ?: false,
                                    shouldShowMuteButton = false,
                                    shouldShowPlayButton = false,
                                ),
                            )
                        }

                        else -> primaryViewData
                    }
                }
            },
            shouldBlurImage = shouldBlurImage,
            allowHardware = allowHardware,
            imageLoader = imageLoader,
            state = state,
            imageProperties = imageProperties,
            imagePlaceholder = secondaryImagePlaceholder,
        )
    }

    if (btsVideoUiModel != null && btsVideoPlayer != null) {
        BtsOverlay(
            modifier = Modifier.padding(videoActionsPadding).fillMaxSize(),
            provideIsVisible = { btsVideoUiModel.btsSettings.isOverlayDisplayed && isSecondaryVisible },
            contentMargin = imageProperties.secondaryMarginDp.dp,
            player = btsVideoPlayer,
            containerData = btsVideoUiModel.containerData.toVideoData(),
            videoSettings = btsVideoUiModel.videoSettings,
            btsSettingsUiModel = btsVideoUiModel.btsSettings,
            isSustainedLookBtsPost = isSustainedLookBtsPost,
        )
    }
}

/**
 * @return a static size based on the screen width to always keep in the cache a unique image
 */
@Composable
internal fun getCacheSizeInPx(
    aspectRatio: Float,
): ImageSize {
    val density = LocalDensity.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    return remember(density, screenWidthDp) {
        with(density) {
            Dp(screenWidthDp.toFloat()).toPx()
        }.let { widthInPx ->
            ImageSize.Fixed(
                width = widthInPx,
                height = (widthInPx / aspectRatio),
            )
        }
    }
}
