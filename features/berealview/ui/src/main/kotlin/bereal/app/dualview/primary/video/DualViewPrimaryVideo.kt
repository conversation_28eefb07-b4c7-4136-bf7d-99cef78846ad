package bereal.app.dualview.primary.video

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.key
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.isInPreview
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.PlayerAction
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.dualview.player.singleplayer.DualViewPlayerNoOp
import bereal.app.dualview.primary.image.DualViewPrimaryImage
import bereal.app.dualview.primary.video.progress.ProgressIfPaused
import bereal.app.dualview.primary.video.progress.getProgressBarState
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.VideoView
import bereal.app.video.ui.model.BeRealVideoPlayerState

@Composable
internal fun DualViewPrimaryVideo(
    media: DualViewData.Media.Video,
    isLongPressingPrimary: () -> Boolean,
    player: DualViewPlayer,
    withThumbnail: Boolean = false,
    imageLoader: StableImageLoader,
    onPlayerAction: (PlayerAction) -> Unit,
    modifier: Modifier = Modifier,
    shouldFitImageIntoContainer: Boolean = false,
) {
    val longPressToPause = remember(player) {
        player.longPressToPause
    }

    val progressBarState = getProgressBarState(
        isLongPressingPrimary = isLongPressingPrimary,
        media = media,
        player = player,
    )

    if (longPressToPause) {
        PauseOnLongPress(
            isLongPressingPrimary = isLongPressingPrimary,
            play = {
                onPlayerAction(PlayerAction.Play)
            },
            pause = {
                onPlayerAction(PlayerAction.Pause)
            },
        )
    }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        if (withThumbnail) {
            media.thumbnail?.let { thumbnail ->
                DualViewPrimaryImage(
                    modifier = Modifier.matchParentSize(),
                    media = thumbnail,
                    imageLoader = imageLoader,
                    imageSize = ImageSize.Original,
                )
            }
        }

        if (isInPreview) {
            Box(modifier = Modifier.matchParentSize(), contentAlignment = Alignment.Center) {
                Box(modifier = Modifier.padding(top = 150.dp)) {
                    Text(
                        modifier = Modifier.background(Color.Black),
                        text = "video player", color = Color.White,
                    )
                }
            }
        } else {
            key(player) {
                VideoView(
                    modifier = Modifier.matchParentSize(),
                    playerVideo = player.exoplayer,
                    onClick = null,
                    tag = player.hashCode().toString() + media.toString(),
                    shouldFitImageIntoContainer = shouldFitImageIntoContainer,
                )
            }
        }

        if (longPressToPause) {
            ProgressIfPaused(
                modifier = Modifier.matchParentSize(),
                progressBar = progressBarState,
                player = player,
            )
        }
    }
}

@Preview
@Composable
private fun DualViewPrimaryVideoPreview() {
    BeRealTheme {
        DualViewPrimaryVideo(
            media = DualViewData.primaryVideoPreview(),
            isLongPressingPrimary = { false },
            imageLoader = LocalStableImageLoader.current,
            player = DualViewPlayerNoOp(
                isMutedValue = true,
                playerStateValue = BeRealVideoPlayerState.Playing,
            ),
            onPlayerAction = {},
            modifier = Modifier.fillMaxSize(),
        )
    }
}
