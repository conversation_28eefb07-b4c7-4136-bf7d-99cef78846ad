package bereal.app.berealview.primary

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.width
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import bereal.app.design.theme.BeRealTheme
import kotlinx.coroutines.delay

@Composable
internal fun PrimaryLoading(modifier: Modifier = Modifier) {
    var visible by remember { mutableStateOf(false) }
    if (visible) {
        Box(modifier = modifier, contentAlignment = Alignment.Center) {
            LinearProgressIndicator(
                modifier = Modifier.width(142.dp),
                color = BeRealTheme.colors.onBackground,
            )
        }
    }
    LaunchedEffect(Unit) {
        // Don't show loader if it's really quick
        delay(200)
        visible = true
    }
}
