package bereal.app.dualview.player

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.design.StadiumShape
import bereal.app.design.theme.BeRealTheme

@Composable
fun DualViewPlayersLoadingOverlay(
    players: DualViewPlayers,
    isCurrentlyVisible: State<Boolean>,
    modifier: Modifier = Modifier,
) {
    val downloadState by players.downloadState.collectAsStateWithLifecycle()
    DualViewPlayersLoadingOverlay(
        getPercentage = {
            downloadState.percent
        },
        isCurrentlyVisible = isCurrentlyVisible,
        modifier = modifier,
    )
}

@Composable
fun DualViewPlayersLoadingOverlay(
    getPercentage: () -> Float,
    isCurrentlyVisible: State<Boolean>,
    modifier: Modifier = Modifier,
) {
    val isVisible by isCurrentlyVisible
    val percentValue = getPercentage()

    AnimatedVisibility(
        modifier = modifier,
        visible = isVisible && percentValue < 1f && percentValue > 0f,
        enter = fadeIn(),
        exit = fadeOut(),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(BeRealTheme.colors.background.copy(alpha = 0.75f)),
            contentAlignment = Alignment.Center,
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(fraction = 0.5f)
                    .height(4.dp)
                    .clip(StadiumShape)
                    .background(BeRealTheme.colors.grayScale.realWhite.copy(alpha = 0.2f))
                    .drawWithCache {
                        onDrawWithContent {
                            val newWidth = this.size.width * percentValue
                            val newHeight = this.size.height
                            drawContent()
                            drawRect(
                                color = Color.White,
                                style = Fill,
                                topLeft = Offset.Zero,
                                size = Size(newWidth, newHeight),
                            )
                        }
                    },
            )
        }
    }
}

@Preview
@Composable
private fun DualViewPlayersLoadingOverlay_Preview() {
    BeRealTheme {
        DualViewPlayersLoadingOverlay(
            modifier = Modifier.fillMaxSize(),
            getPercentage = {
                0.4f
            },
            isCurrentlyVisible = rememberUpdatedState(true),
        )
    }
}
