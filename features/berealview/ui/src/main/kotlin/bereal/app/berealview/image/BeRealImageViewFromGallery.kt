package bereal.app.berealview.image

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import bereal.app.berealview.primary.PrimaryView
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.design.berealimageview.model.BeRealViewConstants
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.toImageData
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.CrossFade
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
fun BeRealImageViewFromGallery(
    modifier: Modifier = Modifier,
    primaryViewData: BeRealViewDataUiModel,
    state: BeRealViewUiState = rememberBeRealViewState(),
    areImagesBlurred: Boolean = false,
    allowHardware: Boolean = true, // for taking screenshot of the view https://github.com/coil-kt/coil/issues/159
    imageLoader: StableImageLoader,
    primaryImagePlaceholder: String? = null,
    imageProperties: BeRealImageProperties,
    withLoading: Boolean = true,
    withPrimaryBorder: () -> Boolean = { false },
    videoActionsPadding: PaddingValues = PaddingValues(),
) {
    Box(
        modifier,
    ) {
        if (primaryViewData is BeRealViewDataUiModel.Image && !areImagesBlurred) {
            RemoteImage(
                modifier = Modifier.fillMaxSize().clip(RoundedCornerShape(imageProperties.primaryRadiusDp.dp)),
                data = primaryViewData.toImageData(),
                imageLoader = imageLoader,
                options = ImageOptions(
                    crossfade = CrossFade.Enabled(),
                    hardware = false,
                    size = ImageSize.Original,
                    blurred = Blur(
                        radius = BeRealViewConstants.BLUR_RADIUS,
                        sampling = 10f,
                    ),
                ),
                contentDescription = null,
                contentScale = ContentScale.Crop,
            )
        }

        PrimaryView(
            primaryData = primaryViewData,
            secondaryData = null,
            shouldBlurImage = areImagesBlurred,
            isPrimaryImageTransparent = false,
            allowHardware = allowHardware,
            imageLoader = imageLoader,
            state = state,
            imageProperties = imageProperties,
            imagePlaceholder = primaryImagePlaceholder,
            withLoading = withLoading,
            withPrimaryBorder = withPrimaryBorder,
            muteButtonPadding = videoActionsPadding,
        )
    }
}
