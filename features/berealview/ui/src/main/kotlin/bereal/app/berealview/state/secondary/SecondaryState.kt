package bereal.app.berealview.state.secondary

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.State
import androidx.compose.ui.geometry.Offset
import kotlinx.coroutines.flow.Flow

@Immutable
interface SecondaryState {
    val isVisible: State<Boolean>
    val onClicked: Flow<Unit>
    val isDragging: State<Boolean>

    //region animatable
    val offset: State<Offset>
    val alpha: State<Float>

    val secondaryOffsetMax: State<Offset>
    //endregion
}
