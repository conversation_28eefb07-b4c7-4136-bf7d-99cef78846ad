package bereal.app.dualview.primary

import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.design.image.model.options.ImageSize
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.PlayerAction
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.dualview.primary.image.DualViewPrimaryImage
import bereal.app.dualview.primary.video.DualViewPrimaryVideo
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.model.BeRealVideoPlayerState

@Composable
internal fun DualPrimaryView(
    modifier: Modifier = Modifier,
    onPlayerAction: (PlayerAction) -> Unit,
    imageLoader: StableImageLoader,
    player: DualViewPlayer?,
    imageSize: ImageSize,
    isLongPressingPrimary: () -> <PERSON><PERSON><PERSON>,
    media: DualViewData.Media,
    shouldFitIntoContainer: Boolean,
    onLoaded: () -> Unit,
) {
    val rememberedOnLoaded by rememberUpdatedState(onLoaded)
    val playerState: State<BeRealVideoPlayerState>? = player?.playerState?.collectAsStateWithLifecycle()
    LaunchedEffect(playerState?.value) {
        if (playerState?.value == BeRealVideoPlayerState.Playing) {
            rememberedOnLoaded()
        }
    }
    when (media) {
        is DualViewData.Media.Image -> {
            Box(modifier) {
                if (shouldFitIntoContainer) {
                    DualViewPrimaryImage(
                        modifier = Modifier.matchParentSize(),
                        imageLoader = imageLoader,
                        media = media,
                        imageSize = imageSize,
                        isBlurred = true,
                        onLoaded = onLoaded,
                    )
                }

                DualViewPrimaryImage(
                    modifier = Modifier.matchParentSize(),
                    imageLoader = imageLoader,
                    media = media,
                    imageSize = imageSize,
                    shouldFitImageIntoContainer = shouldFitIntoContainer,
                    onLoaded = rememberedOnLoaded,
                )
            }
        }

        is DualViewData.Media.Video -> {
            Box(modifier) {
                if (shouldFitIntoContainer && media.thumbnail != null) {
                    DualViewPrimaryImage(
                        modifier = Modifier.matchParentSize(),
                        imageLoader = imageLoader,
                        media = media.thumbnail,
                        imageSize = imageSize,
                        isBlurred = true,
                        onLoaded = onLoaded,
                    )
                }

                if (player != null) {
                    DualViewPrimaryVideo(
                        modifier = Modifier.matchParentSize(),
                        media = media,
                        imageLoader = imageLoader,
                        onPlayerAction = onPlayerAction,
                        player = player,
                        isLongPressingPrimary = isLongPressingPrimary,
                        shouldFitImageIntoContainer = shouldFitIntoContainer,
                    )
                } else if (media.thumbnail != null) {
                    DualViewPrimaryImage(
                        modifier = Modifier.matchParentSize(),
                        imageLoader = imageLoader,
                        media = media.thumbnail,
                        imageSize = imageSize,
                        onLoaded = rememberedOnLoaded,
                    )
                }
            }
        }
    }
}
