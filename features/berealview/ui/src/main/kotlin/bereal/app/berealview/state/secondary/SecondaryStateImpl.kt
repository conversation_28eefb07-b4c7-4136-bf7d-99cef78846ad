package bereal.app.berealview.state.secondary

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.VectorConverter
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.geometry.Offset
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.map

@Immutable
class SecondaryStateImpl : SecondaryState {

    internal val secondaryOffset = Animatable(Offset.Zero, Offset.VectorConverter)
    internal val secondaryAlpha = Animatable(0f)

    internal val onSecondaryClicked = MutableSharedFlow<Long?>()
    override val onClicked = onSecondaryClicked.map { }

    override val isVisible = mutableStateOf(true)
    override val isDragging = mutableStateOf(false)
    internal fun updateIsDraggingSecondary(isDragging: Boolean) {
        this.isDragging.value = isDragging
    }

    override val offset = secondaryOffset.asState()
    override val alpha = secondaryAlpha.asState()
    override val secondaryOffsetMax = mutableStateOf(Offset.Infinite)

    internal var secondaryMinOffsetXAnchor = 0f
}
