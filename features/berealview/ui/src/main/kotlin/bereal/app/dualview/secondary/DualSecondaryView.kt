package bereal.app.dualview.secondary

import androidx.compose.foundation.border
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import bereal.app.commonandroid.thenIf
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.isInPreview
import bereal.app.dualview.model.DualViewConfig
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.dualview.secondary.image.DualViewSecondaryImage
import bereal.app.dualview.secondary.video.DualViewSecondaryVideo
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun DualSecondaryView(
    modifier: Modifier = Modifier,
    imageLoader: StableImageLoader,
    player: DualViewPlayer?,
    dualViewConfig: DualViewConfig,
    size: ImageSize,
    media: DualViewData.Media,
) {
    val secondaryShape = remember(dualViewConfig) {
        RoundedCornerShape(dualViewConfig.secondaryCornerRadius)
    }

    var isImageLoaded by remember(media) {
        mutableStateOf(false)
    }

    val secondaryModifier = applySecondaryDesign(
        modifier = modifier,
        isImageLoaded = isImageLoaded,
        dualViewConfig = dualViewConfig,
        secondaryShape = secondaryShape,
    )

    when (media) {
        is DualViewData.Media.Image -> {
            DualViewSecondaryImage(
                modifier = secondaryModifier,
                media = media,
                onLoaded = {
                    isImageLoaded = true
                },
                imageLoader = imageLoader,
                size = size,
            )
        }

        is DualViewData.Media.Video -> {
            if (player != null && player.playerType != DualViewPlayers.PlayerType.BTS) { // we do not play the bts video on the secondary view
                DualViewSecondaryVideo(
                    modifier = secondaryModifier,
                    media = media,
                    imageLoader = imageLoader,
                    player = player,
                    onThumbnailLoaded = {
                        isImageLoaded = true
                    },
                )
            } else if (media.thumbnail != null) {
                DualViewSecondaryImage(
                    modifier = secondaryModifier,
                    imageLoader = imageLoader,
                    media = media.thumbnail,
                    onLoaded = {
                        isImageLoaded = true
                    },
                    size = size,
                )
            }
        }
    }
}

@Composable
private fun applySecondaryDesign(
    modifier: Modifier = Modifier,
    isImageLoaded: Boolean,
    dualViewConfig: DualViewConfig,
    secondaryShape: RoundedCornerShape,
): Modifier {
    return modifier
        .thenIf(isImageLoaded || isInPreview) { // if we display before the image is loaded it has a small glitch
            Modifier
                .shadow(
                    shape = secondaryShape,
                    elevation = dualViewConfig.secondaryElevation,
                )
                .border(
                    width = dualViewConfig.secondaryBorderWidth,
                    color = dualViewConfig.secondaryBorderColor,
                    shape = secondaryShape,
                )
        }
        .clip(secondaryShape)
}
