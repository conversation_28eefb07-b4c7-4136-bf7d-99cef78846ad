package bereal.app.berealview.video

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.size
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import bereal.app.commonandroid.thenIf
import bereal.app.commonandroid.toPx
import bereal.app.design.R
import bereal.app.design.theme.BeRealTheme

@Composable
fun PlayButton(
    modifier: Modifier = Modifier,
    radius: Float = 24.dp.toPx(),
    state: PlayButtonState.Visible,
) {
    val circleColor = BeRealTheme.colors.grayScale.gray300
    val interactionSource = remember { MutableInteractionSource() }
    val indication = ripple(bounded = false)

    Image(
        modifier = modifier
            .size(32.dp)
            .drawBehind {
                drawCircle(
                    color = circleColor,
                    alpha = 0.5f,
                    radius = radius,
                )
            }
            .thenIf(state is PlayButtonState.Visible.Clickable) {
                Modifier.clickable(
                    interactionSource = interactionSource,
                    indication = indication,
                ) {
                    (state as? PlayButtonState.Visible.Clickable)?.onClick?.invoke()
                }
            },
        painter = painterResource(id = R.drawable.baseline_play_arrow_24),
        contentDescription = "",
    )
}

@Immutable
sealed interface PlayButtonState {
    @Immutable
    data object Hidden : PlayButtonState

    @Immutable
    sealed interface Visible : PlayButtonState {
        @Immutable
        data object NotClickable : Visible

        @Immutable
        data class Clickable(
            val onClick: () -> Unit,
        ) : Visible
    }
}

@Immutable
sealed interface ProgressBarState {
    @Immutable
    data object Hidden : ProgressBarState

    @Immutable
    data object Visible : ProgressBarState
}
