package bereal.app.berealview.video

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
internal fun BoxScope.StopState(
    playButtonState: PlayButtonState,
    muteButtonPadding: PaddingValues,
    muteButtonState: MuteButtonState,
) {
    if (playButtonState is PlayButtonState.Visible) {
        PlayButton(
            state = playButtonState,
        )
    }

    if (muteButtonState is MuteButtonState.Visible) {
        MuteButton(
            modifier = Modifier.align(Alignment.TopEnd).padding(muteButtonPadding).padding(12.dp),
            state = muteButtonState,
        )
    }
}
