package bereal.app.berealview.primary

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.media3.common.util.UnstableApi
import bereal.app.berealview.BeRealViewDesign
import bereal.app.berealview.getCacheSizeInPx
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.image.ImageTransformation
import bereal.app.berealview.image.PrimaryImageContent
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.berealview.video.MuteButtonState
import bereal.app.berealview.video.PlayButtonState
import bereal.app.berealview.video.ProgressBarState
import bereal.app.berealview.video.StopState
import bereal.app.berealview.video.VideoContent
import bereal.app.berealview.video.VideoThumbnail
import bereal.app.bts.ui.model.toVideoData
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.isMediaFromGallery
import bereal.app.design.berealimageview.model.toImageData
import bereal.app.design.berealimageview.model.toVideoData
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.LocalVideoPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import kotlinx.coroutines.launch

@UnstableApi
@Composable
internal fun PrimaryContent(
    primaryData: BeRealViewDataUiModel,
    secondaryData: BeRealViewDataUiModel?,
    shouldBlurImage: Boolean,
    isPrimaryImageTransparent: Boolean,
    allowHardware: Boolean,
    imageLoader: StableImageLoader,
    muteButtonPadding: PaddingValues,
    imageProperties: BeRealImageProperties,
    imagePlaceholder: String? = null,
    onLoaded: (() -> Unit)? = null,
    withLoading: Boolean,
    state: BeRealViewUiState,
    maskColor: Color? = null,
    imageTransformation: ImageTransformation? = null,
    onEvent: (interacting: Boolean) -> Unit = { },
) {
    when (primaryData) {
        is BeRealViewDataUiModel.Image -> {
            val cacheSize = getCacheSizeInPx(aspectRatio = primaryData.aspectRatio)
            PrimaryImageContent(
                imageData = primaryData.toImageData(),
                cacheSize = cacheSize,
                shouldBlurImage = shouldBlurImage,
                isTransparent = isPrimaryImageTransparent,
                allowHardware = allowHardware,
                imageLoader = imageLoader,
                imageProperties = imageProperties,
                withLoading = withLoading,
                imagePlaceholder = imagePlaceholder,
                onLoaded = onLoaded,
                maskColor = maskColor,
                imageTransformation = imageTransformation,
                shouldFitIntoContainer = primaryData.containerData.isMediaFromGallery(),
            )
        }

        is BeRealViewDataUiModel.Video -> {
            val player = LocalVideoPlayer.current.stableValue

            val primaryPlayer = remember(player) {
                player.primaryPlayer
            }

            val isPlayable = remember(primaryData) {
                primaryData.videoSettings.isPlayable
            }

            val thumbnailData = remember(primaryData) {
                primaryData.thumbnail
            }

            val shouldBlurThumbnail = remember(primaryData) {
                primaryData.videoSettings.shouldBlurThumbnail
            }

            if (primaryPlayer != null && isPlayable) {
                val playerState by player.observePrimaryPlayerState(primaryData.containerData.id)
                    .collectAsStateWithLifecycle(BeRealVideoPlayerState.Stopped)

                val muteState by player.observePlayerMuteState()
                    .collectAsStateWithLifecycle(BeRealVideoPlayerMuteState.Unmuted)

                val primaryVideoData = remember(primaryData) {
                    primaryData.toVideoData()
                }

                val scope = rememberCoroutineScope()

                // TODO Video: Move Player outside UI and move that logic with it.
                val secondaryVideoData = remember(secondaryData) {
                    (secondaryData as? BeRealViewDataUiModel.Video)?.toVideoData()
                }

                val containerDataId = remember(primaryData) {
                    primaryData.containerData.id
                }

                PauseOnLongPress(
                    state = state,
                    secondaryVideoData = secondaryVideoData,
                    primaryVideoData = primaryVideoData,
                    player = player,
                    containerDataId = containerDataId,
                    design = imageProperties.design,
                )

                val playButtonState = remember(primaryData) {
                    if (primaryData.videoSettings.shouldShowPlayButton) {
                        PlayButtonState.Visible.Clickable(
                            onClick = {
                                scope.launch {
                                    // TODO VIDEO: to remove and listen to player state in BeRealImageState?
                                    // TODO Video: Move Player outside UI and move that logic with it.
                                    when (secondaryVideoData) {
                                        null -> player.playPrimary(primaryVideoData)
                                        else -> player.playBoth(
                                            primaryVideoData,
                                            secondaryVideoData,
                                        )
                                    }
                                }
                            },
                        )
                    } else {
                        PlayButtonState.Hidden
                    }
                }

                val pauseBehavior = remember(imageProperties.design) {
                    when (imageProperties.design) {
                        BeRealViewDesign.V1 -> PauseBehavior.ClickToPause
                        BeRealViewDesign.V2 -> PauseBehavior.LongPressToPause
                    }
                }

                val progressBar = remember(pauseBehavior) {
                    when (pauseBehavior) {
                        PauseBehavior.ClickToPause -> ProgressBarState.Hidden
                        PauseBehavior.LongPressToPause -> ProgressBarState.Visible
                    }
                }

                val muteButtonState = remember(primaryData, muteState) {
                    if (primaryData.videoSettings.shouldShowMuteButton) {
                        MuteButtonState.Visible(
                            isMuted = muteState == BeRealVideoPlayerMuteState.Muted,
                            onClick = {
                                player.toggleMute()
                            },
                        )
                    } else {
                        MuteButtonState.Hidden
                    }
                }

                VideoContent(
                    player = StablePlayer(primaryPlayer),
                    playerState = playerState,
                    imageLoader = imageLoader,
                    thumbnailData = thumbnailData,
                    shouldBlurThumbnail = shouldBlurThumbnail,
                    onEvent = onEvent,
                    progressBar = progressBar,
                    onVideoClick = {
                        player.pausePlayers(containerDataId)
                    }.takeIf { pauseBehavior == PauseBehavior.ClickToPause },
                    stopClick = {
                        player.stopPlayers(containerDataId)
                    },
                    playButtonState = playButtonState,
                    muteButtonState = muteButtonState,
                    containerDataId = containerDataId,
                    muteButtonPadding = muteButtonPadding,
                    overrideOtherPlayer = false,
                )
            } else {
                val muteButtonState = MuteButtonState.Hidden
                val playButtonState = remember(primaryData) {
                    if (primaryData.videoSettings.shouldShowPlayButton) {
                        PlayButtonState.Visible.NotClickable
                    } else {
                        PlayButtonState.Hidden
                    }
                }

                Box(
                    modifier = Modifier
                        .fillMaxSize(),
                    contentAlignment = Alignment.Center,
                ) {
                    VideoThumbnail(
                        shouldBlur = shouldBlurThumbnail,
                        thumbnailData = thumbnailData,
                        imageLoader = imageLoader,
                    )

                    StopState(
                        muteButtonState = muteButtonState,
                        playButtonState = playButtonState,
                        muteButtonPadding = muteButtonPadding,
                    )
                }
            }
        }
    }
}
