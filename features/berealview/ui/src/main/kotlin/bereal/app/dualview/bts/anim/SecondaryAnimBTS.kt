package bereal.app.dualview.bts.anim

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.commonandroid.toPx
import bereal.app.dualview.bts.players.DualViewPlayersBts
import bereal.app.video.ui.model.BeRealVideoPlayerState

@Composable
internal fun secondaryTranslationXBTS(
    players: DualViewPlayersBts,
    isSecondaryVisibleWhenNotPlaying: Boolean,
    isCurrentlyVisible: State<Boolean>,
): State<Float> {
    val isCurrentlyVisibleValue by isCurrentlyVisible
    val playCounter = players.playCounter.collectAsStateWithLifecycle() // each time we request to play, it increments this counter so I can reset the anim
    var showSecondary by remember(players, playCounter, isCurrentlyVisibleValue /* to reset */) {
        // resets the isVideoPlayed every time the view becomes not visible
        mutableStateOf(isSecondaryVisibleWhenNotPlaying)
    }

    val playState: BeRealVideoPlayerState by players.primary.playerState.collectAsStateWithLifecycle()
    if (isSecondaryVisibleWhenNotPlaying) {
        LaunchedEffect(playState) {
            when (playState) {
                BeRealVideoPlayerState.NearStopped -> {
                    // starts the anim only when the video is near stopped
                    showSecondary = true
                }
                BeRealVideoPlayerState.Playing -> showSecondary = false
                BeRealVideoPlayerState.Loading,
                BeRealVideoPlayerState.Paused,
                BeRealVideoPlayerState.Stopped,
                -> { /* no op */ }
            }
        }
    } else {
        LaunchedEffect(playState) {
            when (playState) {
                BeRealVideoPlayerState.NearStopped -> {
                    // starts the anim only when the video is near stopped
                    showSecondary = true
                }
                BeRealVideoPlayerState.Loading -> {
                    showSecondary = false
                }
                BeRealVideoPlayerState.Stopped,
                BeRealVideoPlayerState.Playing,
                BeRealVideoPlayerState.Paused,
                -> { /* no op */ }
            }
        }
    }

    val outsideTranslation = -200.dp.toPx()
    val secondaryTranslationX = animateFloatAsState(
        targetValue = if (showSecondary) 0f else outsideTranslation,
        tween(durationMillis = 300),
    )

    return secondaryTranslationX
}
