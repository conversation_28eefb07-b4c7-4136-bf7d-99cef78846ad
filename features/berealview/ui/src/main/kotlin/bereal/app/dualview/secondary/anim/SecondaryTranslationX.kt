package bereal.app.dualview.secondary.anim

import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import bereal.app.dualview.bts.anim.secondaryTranslationXBTS
import bereal.app.dualview.bts.players.DualViewPlayersBts
import bereal.app.dualview.player.DualViewPlayers

@Composable
internal fun getSecondaryTranslationX(
    isCurrentlyVisible: State<Boolean>,
    isSecondaryVisibleWhenNotPlaying: <PERSON><PERSON><PERSON>,
    players: DualViewPlayers,
): State<Float> {
    return when (players) {
        is DualViewPlayersBts -> {
            secondaryTranslationXBTS(
                players = players,
                isCurrentlyVisible = isCurrentlyVisible,
                isSecondaryVisibleWhenNotPlaying = isSecondaryVisibleWhenNotPlaying,
            )
        }
        else -> return remember(players) {
            mutableFloatStateOf(0f)
        }
    }
}
