package bereal.app.berealview.primary

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import bereal.app.berealview.BeRealViewDesign
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.design.berealimageview.model.LongPressInteractionStateEventUiModel
import bereal.app.video.ui.model.VideoUiModel
import bereal.app.video.ui.player.BeRealVideoPlayer

@Composable
internal fun PauseOnLongPress(
    state: BeRealViewUiState,
    secondaryVideoData: VideoUiModel?,
    player: BeRealVideoPlayer,
    primaryVideoData: VideoUiModel,
    containerDataId: String,
    design: BeRealViewDesign,
) {
    when (design) {
        BeRealViewDesign.V1 -> return
        BeRealViewDesign.V2 -> {
            // continue
        }
    }

    val isLongPressed: LongPressInteractionStateEventUiModel by state.primary.longPressingState
    var hasLongPressed by remember {
        mutableStateOf(false)
    }
    LaunchedEffect(isLongPressed, hasLongPressed) {
        when (isLongPressed) {
            is LongPressInteractionStateEventUiModel.Released -> {
                if (hasLongPressed) {
                    when (secondaryVideoData) {
                        null -> player.playPrimary(primaryVideoData)
                        else -> player.playBoth(primaryVideoData, secondaryVideoData)
                    }
                    hasLongPressed = false
                }
            }

            is LongPressInteractionStateEventUiModel.Started -> {
                player.pausePlayers(containerDataId)
                hasLongPressed = true
            }
        }
    }
}
