package bereal.app.berealview.image

import androidx.compose.runtime.Immutable
import bereal.app.berealview.BeRealViewDesign
import bereal.app.image.core.BeRealImageSizes
import kotlin.math.max
import kotlin.math.pow

@Immutable
data class BeRealImageProperties(
    val primaryWidthDp: Float,
    val type: BeRealImageType = BeRealImageType.Regular,
    val design: BeRealViewDesign,
) {

    companion object {
        fun computeBeRealPrimaryRadius(primaryWidthDp: Float, design: BeRealViewDesign): Float {
            return max(
                0f,
                primaryWidthDp * primaryWidthDp * -6.42f * 10.0f.pow(-5.0f) + 0.0639f * primaryWidthDp + 2.57f,
            ).let {
                it * if (design == BeRealViewDesign.V2) 2f else 1f
            }.coerceAtMost(40f)
        }
    }

    val primaryRadiusDp = computeBeRealPrimaryRadius(primaryWidthDp, design)

    val secondaryWidthDp = when (design) {
        BeRealViewDesign.V1 -> primaryWidthDp * 0.3f
        BeRealViewDesign.V2 -> primaryWidthDp * 0.35f
    }

    val secondaryRadiusDp = if (design == BeRealViewDesign.V2) {
        primaryRadiusDp * 0.6f
    } else {
        primaryRadiusDp
    }.let { primaryRadius ->
        max(
            0f,
            primaryRadius * primaryRadius * 0.0211f + 0.5028f * primaryRadius - 0.2099f,
        )
    }.coerceAtMost(24f)

    private val _secondaryMarginDp = if (primaryWidthDp >= 390f) {
        (primaryWidthDp * if (design == BeRealViewDesign.V2) 0.05f else 0.03f)
    } else {
        (primaryWidthDp * if (design == BeRealViewDesign.V2) 0.055f else 0.035f)
    }

    val secondaryMarginDp = _secondaryMarginDp

    val secondaryBorderDp = when {
        secondaryWidthDp < 58f -> 1f
        secondaryWidthDp < 65f -> 1.25f
        secondaryWidthDp < 75f -> 1.5f
        secondaryWidthDp < 95f -> 1.75f
        else -> 2f
    }

    val dragOffsetMaxDp: Float = primaryWidthDp - secondaryWidthDp

    val primaryHeightDp = primaryWidthDp / BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT
}
