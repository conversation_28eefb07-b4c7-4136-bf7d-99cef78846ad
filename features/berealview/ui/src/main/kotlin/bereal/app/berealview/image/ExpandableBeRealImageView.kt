package bereal.app.berealview.image

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import bereal.app.berealview.BeRealView
import bereal.app.bts.ui.model.BtsVideoUiModel
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.SecondaryAnimationType
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent
import bereal.app.design.image.ImageScaleAnimation
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
fun ExpandableBeRealImageView(
    modifier: Modifier = Modifier,
    imagePadding: PaddingValues,
    imageLoaders: StableImageLoader,
    postId: String,
    primaryImage: BeRealViewDataUiModel,
    secondaryImage: BeRealViewDataUiModel?,
    btsContent: BtsVideoUiModel?,
    imageScaleAnim: ImageScaleAnimation,
    onTap: () -> Unit = {},
    onDoubleTap: () -> Unit = {},
    onPrimaryZoomed: () -> Unit = {},
    onSecondaryClicked: (BeRealViewContainerDataUiModel, VisibleBerealViewLargeContent) -> Unit,
) {
    val beRealImageViewState = rememberBeRealViewState(
        secondaryPhotoAnimation = SecondaryAnimationType.None,
        allowUserInteractions = if (imageScaleAnim.isFullyExpanded.value) {
            AllowInteractions.AllInteractions
        } else {
            AllowInteractions.OnlyTap
        },
        onTap = onTap,
        onDoubleTap = onDoubleTap,
        onZoom = onPrimaryZoomed,
        onSecondaryClicked = onSecondaryClicked,
        postId = postId,
        btsContainerId = btsContent?.containerData?.id,
        isBtsPlayable = btsContent?.videoSettings?.isPlayable ?: false,
    )

    Box(modifier.padding(imagePadding)) {
        BeRealView(
            primaryViewData = primaryImage,
            secondaryViewData = secondaryImage,
            btsContent = btsContent,
            imageLoader = imageLoaders,
            state = beRealImageViewState,
            modifier = Modifier
                .height(imageScaleAnim.imageHeightDp.value.dp)
                .width((imageScaleAnim.imageHeightDp.value * primaryImage.aspectRatio).dp)
                .aspectRatio(primaryImage.aspectRatio),
        )
    }
}
