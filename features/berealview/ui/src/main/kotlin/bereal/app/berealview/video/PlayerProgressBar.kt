package bereal.app.berealview.video

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.design.StadiumShape
import bereal.app.design.theme.BeRealTheme
import bereal.app.video.ui.model.StablePlayer
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

@Composable
fun PlayerProgressBar(
    modifier: Modifier = Modifier,
    player: StablePlayer,
) {
    val percent = playerCurrentProgress(player)

    PlayerProgressBar(
        modifier = modifier,
        percent = {
            val percentValue by percent
            percentValue
        },
    )
}

@Composable
private fun playerCurrentProgress(player: StablePlayer): State<Float> {
    val percent = remember {
        mutableFloatStateOf(0f)
    }
    LaunchedEffect(key1 = player) {
        val p = player.stable
        try {
            while (isActive) {
                percent.floatValue = ((p.currentPosition.toFloat() / p.duration.toFloat())).coerceIn(0f, 1f)
                delay(300)
            }
        } catch (t: Throwable) {
            t.printStackTrace()
        }
    }
    return percent
}

@Composable
private fun PlayerProgressBar(
    modifier: Modifier = Modifier,
    percent: @Composable () -> Float,
) {
    val percentValue = percent()
    Box(
        modifier = modifier
            .clip(StadiumShape)
            .background(Color.White.copy(alpha = 0.2f))
            .drawWithCache {
                onDrawWithContent {
                    val newWidth = this.size.width * percentValue
                    val newHeight = this.size.height
                    drawContent()
                    drawRect(
                        color = Color.White,
                        style = Fill,
                        topLeft = Offset.Zero,
                        size = Size(newWidth, newHeight),
                    )
                }
            },
    )
}

@Composable
@Preview
private fun PlayerProgressBarPreview() {
    BeRealTheme {
        PlayerProgressBar(
            percent = { 0.2f },
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = 30.dp)
                .height(8.dp),
        )
    }
}
