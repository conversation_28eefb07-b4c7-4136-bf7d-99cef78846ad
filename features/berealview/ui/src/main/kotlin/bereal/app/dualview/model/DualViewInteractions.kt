package bereal.app.dualview.model

data class DualViewInteractions(
    val tap: <PERSON>olean,
    val doubleTap: Boolean,
    val drag: <PERSON>olean,
    val zoom: Boolean,
    val longPressPrimary: Boolean,
) {
    companion object {
        val Disabled = DualViewInteractions(
            tap = false,
            drag = false,
            zoom = false,
            longPressPrimary = false,
            doubleTap = false,
        )
        val OnlyTap = DualViewInteractions(
            tap = true,
            drag = false,
            zoom = false,
            longPressPrimary = false,
            doubleTap = false,
        )
        val TapAndLongPress = DualViewInteractions(
            tap = true,
            drag = false,
            zoom = false,
            longPressPrimary = true,
            doubleTap = false,
        )
        val AllInteractions = DualViewInteractions(
            tap = true,
            drag = true,
            zoom = true,
            longPressPrimary = true,
            doubleTap = true,
        )
    }

    val hasPressGesture = tap || doubleTap || longPressPrimary
}
