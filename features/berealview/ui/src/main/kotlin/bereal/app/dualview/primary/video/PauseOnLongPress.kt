package bereal.app.dualview.primary.video

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue

@Composable
internal fun PauseOnLongPress(
    isLongPressingPrimary: () -> <PERSON><PERSON><PERSON>,
    play: () -> Unit,
    pause: () -> Unit,
) {
    val playCallback by rememberUpdatedState(newValue = play)
    val pauseCallback by rememberUpdatedState(newValue = pause)

    val isLongPressingPrimaryValue = isLongPressingPrimary()

    var hasLongPressed by remember {
        mutableStateOf(false)
    }
    LaunchedEffect(isLongPressingPrimaryValue, hasLongPressed) {
        when (isLongPressingPrimaryValue) {
            false -> {
                if (hasLongPressed) {
                    playCallback()
                    hasLongPressed = false
                }
            }

            true -> {
                pauseCallback()
                hasLongPressed = true
            }
        }
    }
}
