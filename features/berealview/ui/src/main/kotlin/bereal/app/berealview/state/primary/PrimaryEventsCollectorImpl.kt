package bereal.app.berealview.state.primary

import androidx.compose.runtime.State
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import bereal.app.design.berealimageview.model.LongPressInteractionStateEventUiModel
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

internal class PrimaryEventsCollectorImpl(
    private val allowUserInteractions: State<AllowInteractions>,
    private val scope: CoroutineScope,
    private val primary: PrimaryStateImpl,
) : PrimaryEventsCollector {

    override fun onPrimaryClicked() {
        scope.launch {
            primary.onPrimaryClicked.emit(System.currentTimeMillis())
        }
    }

    override fun onPrimaryDoubleClick() {
        scope.launch {
            primary.onPrimaryDoubleClick.emit(System.currentTimeMillis())
        }
    }

    override fun onPrimaryLoaded() {
        scope.launch {
            // the delay is calculated with (image crossfade duration + image render duration + a small protection delay)
            delay(400) // without delay the composable has not time to render (with crossfade) the new image before telling others the primary is loaded
            primary.isImageLoaded.value = true
        }
    }

    override fun onPrimaryZooming(isZooming: Boolean) {
        if (allowUserInteractions.value == AllowInteractions.AllInteractions) {
            primary.isZooming.value = isZooming
        }
    }

    override fun onStartPressPrimary() {
        if (allowUserInteractions.value == AllowInteractions.AllInteractions) {
            primary.isUserInteracting.value = true
        }
    }

    override fun onEndLongPressPrimary() {
        if (allowUserInteractions.value == AllowInteractions.AllInteractions) {
            primary.isUserInteracting.value = false
            // There is no event to say long-press is over so we need trigger the event ourself.
            primary.longPressingState.value = LongPressInteractionStateEventUiModel.Released
        }
    }

    override fun onUserInteractingWithPrimaryImage(isInteracting: Boolean) {
        if (allowUserInteractions.value == AllowInteractions.AllInteractions) {
            primary.isUserInteracting.value = isInteracting
        }
    }

    override fun onLongPress(
        containerData: BeRealViewContainerDataUiModel,
        visibleLargeContent: VisibleBerealViewLargeContent,
    ) {
        if (allowUserInteractions.value == AllowInteractions.AllInteractions) {
            primary.isUserInteracting.value = true
            primary.longPressingState.value = LongPressInteractionStateEventUiModel.Started(
                containerData = containerData,
                visibleLargeContent = visibleLargeContent,
            )
        }
    }
}
