package bereal.app.dualview.secondary.video

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.isInPreview
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.dualview.player.singleplayer.DualViewPlayerNoOp
import bereal.app.dualview.secondary.image.DualViewSecondaryImage
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.VideoView
import bereal.app.video.ui.model.BeRealVideoPlayerState

@Composable
internal fun DualViewSecondaryVideo(
    media: DualViewData.Media.Video,
    player: DualViewPlayer,
    onThumbnailLoaded: () -> Unit,
    imageLoader: StableImageLoader,
    modifier: Modifier = Modifier,
) {
    val onThumbnailLoadedCallback by rememberUpdatedState(newValue = onThumbnailLoaded)

    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        media.thumbnail?.let { thumbnail ->
            DualViewSecondaryImage(
                modifier = Modifier.matchParentSize(),
                media = thumbnail,
                imageLoader = imageLoader,
                onLoaded = onThumbnailLoadedCallback,
                size = ImageSize.Original,
            )
        } ?: run {
            LaunchedEffect(key1 = media) {
                onThumbnailLoadedCallback()
            }
        }

        if (isInPreview) {
            Box(modifier = Modifier.matchParentSize(), contentAlignment = Alignment.Center) {
                Text(
                    text = "video player",
                    modifier = Modifier.background(Color.Black),
                    color = Color.White,
                )
            }
        } else {
            key(player) {
                VideoView(
                    modifier = Modifier.matchParentSize(),
                    playerVideo = player.exoplayer,
                    onClick = null,
                    tag = player.hashCode().toString() + media.toString(),
                    overrideOtherPlayer = true, // TO be sure we can clip
                )
            }
        }
    }
}

@Preview
@Composable
private fun DualViewSecondaryVideoPreview() {
    BeRealTheme {
        DualViewSecondaryVideo(
            media = DualViewData.primaryVideoPreview(),
            imageLoader = LocalStableImageLoader.current,
            player = DualViewPlayerNoOp(
                playerStateValue = BeRealVideoPlayerState.Playing,
            ),
            modifier = Modifier.fillMaxSize(),
            onThumbnailLoaded = {},
        )
    }
}
