package bereal.app.berealview.delegate

import bereal.app.analytics.AEvent
import bereal.app.analytics.model.AnalyticsEvent
import bereal.app.analytics.model.AnalyticsMediaType
import bereal.app.analytics.model.AnalyticsParam
import bereal.app.analytics.model.AnalyticsPostType
import bereal.app.analytics.model.AnalyticsView
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent

// TODO should be moved to a domain module
// BeRealImageView should have its own module
sealed class BeRealViewEventAnalyticsDomainModel(
    override val name: String,
    override val params: List<AnalyticsParam?> = emptyList(),
) : AnalyticsEvent {

    data class PrimaryLongPress(
        val momentId: String,
        val userId: String,
        val postId: String,
        val isMain: Boolean,
        val isLate: Boolean,
        val isMediaTypeVideo: Boolean,
        val mainContent: VisibleBerealViewLargeContent,
        val view: AnalyticsView,
        val hasBts: <PERSON><PERSON><PERSON>,
    ) : BeRealViewEventAnalyticsDomainModel(
        name = "actionPostHold",
        params = listOf(
            AnalyticsParam(AEvent.KEY_MOMENT_ID_VALUE, momentId),
            AnalyticsParam(AEvent.KEY_USER_ID_VALUE, userId),
            AnalyticsParam(AEvent.KEY_VIEW_VALUE, view.value),
            AnalyticsParam(AEvent.KEY_POST_ID_VALUE, postId),
            AnalyticsParam(
                AEvent.KEY_POST_TYPE_VALUE,
                AnalyticsPostType.toPostType(isMain = isMain, isLate = isLate).value,
            ),
            AnalyticsParam(AEvent.KEY_MEDIA_TYPE_VALUE, AnalyticsMediaType.toMediaType(isMediaTypeVideo).value),
            AnalyticsParam("onDisplay", mainContent.name.lowercase()),
            AnalyticsParam(AEvent.KEY_BTS_ENABLED, hasBts),
        ),
    )

    data class SecondaryClick(
        val momentId: String,
        val userId: String,
        val postId: String,
        val isMain: Boolean,
        val isLate: Boolean,
        val isMediaTypeVideo: Boolean,
        val mainContent: VisibleBerealViewLargeContent,
        val view: AnalyticsView,
        val hasBts: Boolean,
    ) : BeRealViewEventAnalyticsDomainModel(
        name = "actionPostSwap",
        params = listOf(
            AnalyticsParam(AEvent.KEY_MOMENT_ID_VALUE, momentId),
            AnalyticsParam(AEvent.KEY_USER_ID_VALUE, userId),
            AnalyticsParam(AEvent.KEY_VIEW_VALUE, view.value),
            AnalyticsParam(AEvent.KEY_POST_ID_VALUE, postId),
            AnalyticsParam(
                AEvent.KEY_POST_TYPE_VALUE,
                AnalyticsPostType.toPostType(isMain = isMain, isLate = isLate).value,
            ),
            AnalyticsParam(AEvent.KEY_MEDIA_TYPE_VALUE, AnalyticsMediaType.toMediaType(isMediaTypeVideo).value),
            AnalyticsParam("onDisplay", mainContent.name.lowercase()),
            AnalyticsParam(AEvent.KEY_BTS_ENABLED, hasBts),
        ),
    )
}
