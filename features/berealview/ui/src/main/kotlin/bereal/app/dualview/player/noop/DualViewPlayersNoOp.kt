package bereal.app.dualview.player.noop

import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.DownloadState
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.PlayerAction
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import kotlinx.coroutines.flow.MutableStateFlow

@Immutable
data class DualViewPlayersNoOp(
    override val primary: DualViewPlayer? = null,
    override val secondary: DualViewPlayer? = null,
) : DualViewPlayers {

    override val playerType: DualViewPlayers.PlayerType = DualViewPlayers.PlayerType.DualVideo

    override val playerMuteState = MutableStateFlow(BeRealVideoPlayerMuteState.Muted)

    override val downloadState = MutableStateFlow<DownloadState>(DownloadState.Downloaded)

    override fun toggleMuted() {
        // no op
    }

    override fun play() {
        // no op
    }

    override fun resume() {
        // no op
    }

    override fun pause() {
        // no op
    }

    override fun onPlayerAction(action: PlayerAction) {
        // no op
    }

    override suspend fun preload(data: DualViewData) {
        // no op
    }

    override fun dispose() {
        // no op
    }

    override fun onViewPaused() {
        // no op
    }

    override fun onViewResumed() {
        // no op
    }
}
