package bereal.app.dualview.interactions

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.dualview.bts.players.DualViewPlayersBts
import bereal.app.dualview.model.DualViewInteractions
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.video.ui.model.BeRealVideoPlayerState

/**
 * Takes the real interactions state, and adjust it depending on the video playing
 */
@Composable
internal fun transformDualViewInteractions(
    players: DualViewPlayers,
    interactions: DualViewInteractions,
): DualViewInteractions {
    return when (players) {
        is DualViewPlayersBts -> {
            transformInteractionDependingOnBTS(
                players = players,
                interactions = interactions,
            )
        }

        else -> interactions // dont change it
    }
}

@Composable
private fun transformInteractionDependingOnBTS(
    players: DualViewPlayersBts,
    interactions: DualViewInteractions,
): DualViewInteractions {
    val playState: BeRealVideoPlayerState by players.primary.playerState.collectAsStateWithLifecycle()
    val disabled = remember(playState) {
        when (playState) {
            BeRealVideoPlayerState.Loading,
            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Playing,
            -> true

            BeRealVideoPlayerState.NearStopped -> false
            BeRealVideoPlayerState.Stopped -> false
        }
    }

    return remember(disabled) {
        if (disabled) {
            DualViewInteractions.Disabled
        } else {
            interactions
        }
    }
}
