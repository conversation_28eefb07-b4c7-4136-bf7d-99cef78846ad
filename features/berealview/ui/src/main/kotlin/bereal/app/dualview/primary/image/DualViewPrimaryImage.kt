package bereal.app.dualview.primary.image

import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.design.berealimageview.model.BeRealViewConstants.BLUR_RADIUS
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.model.DualViewData
import bereal.app.image.core.BeRealImageSizes
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun DualViewPrimaryImage(
    media: DualViewData.Media.Image,
    imageLoader: StableImageLoader,
    modifier: Modifier = Modifier,
    imageSize: ImageSize,
    isBlurred: Boolean = false,
    shouldFitImageIntoContainer: Boolean = false,
    onLoaded: () -> Unit = {},
) {
    RemoteImage(
        data = media.image,
        modifier = modifier,
        imageLoader = imageLoader,
        contentDescription = null,
        contentScale = if (shouldFitImageIntoContainer) ContentScale.Fit else ContentScale.Crop,
        placeHolder = null,
        onLoaded = onLoaded,
        onError = {},
        options = ImageOptions(
            hardware = true,
            size = imageSize,
            blurred = if (isBlurred) Blur(
                radius = BLUR_RADIUS,
                sampling = 10f,
            ) else null,
        ),
    )
}

@Composable
@Preview
private fun DualViewPrimaryImagePreview() {
    BeRealTheme {
        DualViewPrimaryImage(
            modifier = Modifier.aspectRatio(BeRealImageSizes.NewDesign.ASPECT_RATIO_WIDTH_ON_HEIGHT),
            media = DualViewData.primaryImagePreview(),
            imageLoader = LocalStableImageLoader.current,
            imageSize = ImageSize.Original,
        )
    }
}

@Composable
@Preview
private fun DualViewPrimaryImagePreview_blurred() {
    BeRealTheme {
        DualViewPrimaryImage(
            modifier = Modifier.aspectRatio(BeRealImageSizes.NewDesign.ASPECT_RATIO_WIDTH_ON_HEIGHT),
            media = DualViewData.primaryImagePreview(),
            imageLoader = LocalStableImageLoader.current,
            isBlurred = true,
            imageSize = ImageSize.Original,
        )
    }
}
