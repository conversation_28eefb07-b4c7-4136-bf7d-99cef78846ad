package bereal.app.berealview.secondary

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.media3.common.util.UnstableApi
import bereal.app.berealview.BeRealViewDesign
import bereal.app.berealview.getCacheSizeInPx
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.image.SecondaryImageContent
import bereal.app.berealview.video.MuteButtonState
import bereal.app.berealview.video.PlayButtonState
import bereal.app.berealview.video.ProgressBarState
import bereal.app.berealview.video.StopState
import bereal.app.berealview.video.VideoContent
import bereal.app.berealview.video.VideoThumbnail
import bereal.app.bts.ui.model.toVideoData
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.toImageData
import bereal.app.design.berealimageview.model.toVideoData
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.video.ui.LocalVideoPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import kotlinx.coroutines.launch

@UnstableApi
@Composable
internal fun SecondaryContent(
    modifier: Modifier = Modifier,
    data: BeRealViewDataUiModel,
    shouldBlurImage: Boolean,
    allowHardware: Boolean,
    imageProperties: BeRealImageProperties,
    imageLoader: StableImageLoader,
    imagePlaceholder: String? = null,
    canClick: Boolean,
    onClick: () -> Unit,
) {
    val secondaryAspectRatio = 3f / 4f // the secondary is forced on this ratio

    val internalModifier = modifier
        .padding(imageProperties.secondaryMarginDp.dp)
        .width(imageProperties.secondaryWidthDp.dp)
        .aspectRatio(secondaryAspectRatio)
        .then(
            when (imageProperties.design) {
                BeRealViewDesign.V1 ->
                    Modifier
                        .clip(RoundedCornerShape(imageProperties.secondaryRadiusDp.dp))
                        .border(
                            width = imageProperties.secondaryBorderDp.dp,
                            color = BeRealTheme.colors.background,
                            shape = RoundedCornerShape(imageProperties.secondaryRadiusDp.dp),
                        )
                        .clipToBounds()

                BeRealViewDesign.V2 ->
                    Modifier
                        .shadow(
                            shape = RoundedCornerShape(imageProperties.secondaryRadiusDp.dp),
                            elevation = 10.dp,
                        )
                        .clip(RoundedCornerShape(imageProperties.secondaryRadiusDp.dp))
                        .border(
                            width = 1.dp,
                            color = BeRealTheme.colors.grayScale.realWhite.copy(alpha = 0.2f),
                            shape = RoundedCornerShape(imageProperties.secondaryRadiusDp.dp),
                        )
            },
        )

    when (data) {
        is BeRealViewDataUiModel.Image -> {
            val cacheSize = getCacheSizeInPx(aspectRatio = secondaryAspectRatio)
            SecondaryImageContent(
                image = data.toImageData(),
                aspectRatio = secondaryAspectRatio,
                cacheSize = cacheSize,
                shouldBlurImage = shouldBlurImage,
                allowHardware = allowHardware,
                imageProperties = imageProperties,
                imageLoader = imageLoader,
                canClick = canClick,
                onClick = onClick,
                imagePlaceholder = imagePlaceholder,
                modifier = internalModifier,
            )
        }

        is BeRealViewDataUiModel.Video -> {
            val player = LocalVideoPlayer.current.stableValue

            val secondaryPlayer = remember(player) {
                player.secondaryPlayer
            }

            val isPlayable = remember(data) {
                data.videoSettings.isPlayable
            }

            val thumbnailData = remember(data) {
                data.thumbnail
            }

            val shouldBlurThumbnail = remember(data) {
                data.videoSettings.shouldBlurThumbnail
            }

            if (secondaryPlayer != null && isPlayable) {
                val playerState by player.observeSecondaryPlayerState(data.containerData.id)
                    .collectAsStateWithLifecycle(BeRealVideoPlayerState.Stopped)

                val muteState by player.observePlayerMuteState()
                    .collectAsStateWithLifecycle(BeRealVideoPlayerMuteState.Unmuted)

                val scope = rememberCoroutineScope()

                val videoData = remember(data) {
                    data.toVideoData()
                }

                val containerDataId = remember(data) {
                    data.containerData.id
                }

                val playButtonState = remember(data) {
                    if (data.videoSettings.shouldShowPlayButton) {
                        PlayButtonState.Visible.Clickable(
                            onClick = {
                                scope.launch {
                                    player.playSecondary(videoData)
                                }
                            },
                        )
                    } else {
                        PlayButtonState.Hidden
                    }
                }

                val muteButtonState = remember(data, muteState) {
                    if (data.videoSettings.shouldShowMuteButton) {
                        MuteButtonState.Visible(
                            isMuted = muteState == BeRealVideoPlayerMuteState.Muted,
                            onClick = {
                                player.toggleMute()
                            },
                        )
                    } else {
                        MuteButtonState.Hidden
                    }
                }

                VideoContent(
                    modifier = internalModifier.debounceClickable {
                        onClick()
                    },
                    player = StablePlayer(secondaryPlayer),
                    playerState = playerState,
                    imageLoader = imageLoader,
                    onVideoClick = {
                        onClick()
                    },
                    stopClick = {
                        player.stopPlayers(containerDataId)
                    },
                    playButtonState = playButtonState,
                    muteButtonState = muteButtonState,
                    containerDataId = containerDataId,
                    thumbnailData = thumbnailData,
                    shouldBlurThumbnail = shouldBlurThumbnail,
                    overrideOtherPlayer = true,
                    muteButtonPadding = PaddingValues(),
                    progressBar = ProgressBarState.Hidden,
                )
            } else {
                val muteButtonState = MuteButtonState.Hidden
                val playButtonState = remember(data) {
                    if (data.videoSettings.shouldShowPlayButton) {
                        PlayButtonState.Visible.NotClickable
                    } else {
                        PlayButtonState.Hidden
                    }
                }

                Box(
                    modifier = internalModifier.clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = ripple(bounded = false),
                    ) {
                        onClick()
                    },
                    contentAlignment = Alignment.Center,
                ) {
                    VideoThumbnail(
                        shouldBlur = shouldBlurThumbnail,
                        thumbnailData = thumbnailData,
                        imageLoader = imageLoader,
                    )

                    StopState(
                        muteButtonState = muteButtonState,
                        playButtonState = playButtonState,
                        muteButtonPadding = PaddingValues(),
                    )
                }
            }
        }
    }
}
