package bereal.app.berealview.secondary

import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.media3.common.util.UnstableApi
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.commonandroid.thenIf
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.LongPressInteractionStateEventUiModel
import bereal.app.image.core.imageloader.StableImageLoader

@UnstableApi
@Composable
internal fun SecondaryView(
    modifier: Modifier = Modifier,
    data: BeRealViewDataUiModel,
    state: BeRealViewUiState,
    shouldBlurImage: <PERSON>olean,
    allowHardware: Boolean,
    imageLoader: StableImageLoader,
    imageProperties: BeRealImageProperties,
    imagePlaceholder: String? = null,
) {
    val stateEvents = remember {
        state.eventsCollectors.secondary
    }

    val allowUserInteractions by state.allowUserInteractions
    val canDrag by remember {
        derivedStateOf {
            allowUserInteractions == AllowInteractions.AllInteractions
        }
    }
    val canClick by remember {
        derivedStateOf {
            allowUserInteractions == AllowInteractions.AllInteractions &&
                !state.primary.isZooming.value &&
                state.primary.longPressingState.value is LongPressInteractionStateEventUiModel.Released
        }
    }

    val secondaryModifier = modifier
        .graphicsLayer {
            translationX = state.secondary.offset.value.x
            translationY = state.secondary.offset.value.y
            alpha = state.secondary.alpha.value
        }
        .thenIf(canDrag) {
            Modifier
                .pointerInput(imageProperties) {
                    detectDragGestures(
                        onDragStart = {
                            stateEvents.onStartSecondaryDrag()
                        },
                        onDragEnd = {
                            stateEvents.onEndSecondaryDrag()
                        },
                        onDragCancel = {
                            stateEvents.onDragSecondaryCancel()
                        },
                    ) { change, dragAmount ->
                        change.consume()
                        stateEvents.onDragSecondary(dragAmount)
                    }
                }
        }

    SecondaryContent(
        modifier = secondaryModifier,
        data = data,
        shouldBlurImage = shouldBlurImage,
        allowHardware = allowHardware,
        imageLoader = imageLoader,
        imageProperties = imageProperties,
        canClick = canClick,
        onClick = {
            stateEvents.onSecondaryClicked(
                containerData = data.containerData,
            )
        },
        imagePlaceholder = imagePlaceholder,
    )
}
