package bereal.app.smalldualview.mapper

import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.dualview.model.DualViewData
import bereal.app.entities.CorePost
import bereal.app.entities.PostType
import bereal.app.entities.thumbnailOrPicture
import bereal.app.smalldualview.model.SmallDualViewData

fun CorePost.smallDualViewData() = SmallDualViewData(
    primary = DualViewData.Media.Image(
        image = ImageDataModel.Url(this.primary.thumbnailOrPicture()),
    ),
    secondary = this.secondary?.let {
        DualViewData.Media.Image(
            image = ImageDataModel.Url(it.thumbnailOrPicture()),
        )
    },
    type = when (type) {
        PostType.Regular -> DualViewData.Type.Images
        PostType.FreeRide -> DualViewData.Type.Images
        PostType.Bts -> DualViewData.Type.Images
        PostType.DualVideo -> DualViewData.Type.Images
        PostType.MediaFromGallery -> DualViewData.Type.MediaFromGallery
        PostType.MediaFromGalleryVideo -> DualViewData.Type.Images
        PostType.SingleVideo -> DualViewData.Type.Images
        PostType.SinglePhoto -> DualViewData.Type.Images
    },
)
