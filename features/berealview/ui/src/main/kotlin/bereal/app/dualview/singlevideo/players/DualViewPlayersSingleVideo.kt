package bereal.app.dualview.singlevideo.players

import android.annotation.SuppressLint
import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.DownloadState
import bereal.app.bts.ui.player.SimpleSingleVideoPlayer
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.video
import bereal.app.dualview.model.videoAnalyticsParams
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.PlayerAction
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.dualview.singlevideo.players.singleplayer.DualViewSinglePlayerImpl
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

@SuppressLint("UnsafeOptInUsageError")
@Immutable
internal data class DualViewPlayersSingleVideo(
    private val player: SimpleSingleVideoPlayer,
) : DualViewPlayers {

    override val playerType = DualViewPlayers.PlayerType.SingleVideo

    override val downloadState: StateFlow<DownloadState> = MutableStateFlow(DownloadState.Downloaded)

    override val primary: DualViewPlayer = DualViewSinglePlayerImpl(
        simpleSingleVideoPlayer = player,
    )

    override val secondary: DualViewPlayer? = null

    override val playerMuteState: StateFlow<BeRealVideoPlayerMuteState> = player.playerMuteState

    override fun toggleMuted() {
        player.toggleMute()
    }

    override fun play() {
        player.play()
    }

    override fun resume() {
        player.resume()
    }

    override fun pause() {
        player.pause()
    }

    override fun dispose() {
        player.release()
    }

    override suspend fun preload(data: DualViewData) {
        data.primary.video()?.let { player.prepare(it, data.videoAnalyticsParams()) }
    }

    override fun onPlayerAction(action: PlayerAction) {
        when (action) {
            is PlayerAction.Play -> play()
            is PlayerAction.Pause -> pause()
        }
    }

    private var pausedOnLifecycle = false

    override fun onViewPaused() {
        when (player.playerState.value) {
            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Stopped,
            -> {
                // no op, only if playing
            }

            BeRealVideoPlayerState.NearStopped,
            BeRealVideoPlayerState.Loading,
            BeRealVideoPlayerState.Playing,
            -> {
                pausedOnLifecycle = true
                pause()
            }
        }
        pausedOnLifecycle = true
        pause()
    }

    override fun onViewResumed() {
        if (pausedOnLifecycle) {
            pausedOnLifecycle = false
            player.play()
        }
    }
}
