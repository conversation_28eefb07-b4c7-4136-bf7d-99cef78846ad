package bereal.app.dualview

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.commonandroid.UpdateViewConfiguration
import bereal.app.commonandroid.toPx
import bereal.app.design.button.BeRealButton
import bereal.app.design.button.ButtonSize
import bereal.app.design.button.ButtonStyle
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.blur.DualViewBlurredOverlay
import bereal.app.dualview.interactions.transformDualViewInteractions
import bereal.app.dualview.layout.DualViewLayout
import bereal.app.dualview.medias.getMediasToDisplay
import bereal.app.dualview.medias.getSingleMediaToDisplay
import bereal.app.dualview.model.DualViewConfig
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.DualViewInteractions
import bereal.app.dualview.model.SecondaryAnchor
import bereal.app.dualview.model.shouldFitIntoContainer
import bereal.app.dualview.model.whiteBorderDualViewConfig
import bereal.app.dualview.player.BindPlayerToVisibility
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.DualViewPlayersLoadingOverlay
import bereal.app.dualview.player.ResetIsMediaSwitchedOnVisibilityChangedForBTS
import bereal.app.dualview.player.rememberDualViewPlayers
import bereal.app.dualview.primary.DualPrimaryView
import bereal.app.dualview.secondary.DualSecondaryView
import bereal.app.dualview.secondary.anim.getSecondaryTranslationX
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
fun DualView(
    data: DualViewData,
    modifier: Modifier = Modifier,
    isCurrentlyVisible: State<Boolean>,
    blurOverlay: Boolean = false,
    dualViewConfig: DualViewConfig = whiteBorderDualViewConfig,
    players: DualViewPlayers = rememberDualViewPlayers(
        data = data,
    ),
    isSecondaryVisibleWhenBtsNotPlaying: Boolean = false, // default pager behavior
    onLongPress: (isLongPressing: Boolean) -> Unit = {},
    shouldHideSecondary: State<Boolean> = rememberUpdatedState(false),
    canSwitchMedia: State<Boolean> = rememberUpdatedState(true),
    canSwitchMediaFromPrimaryClick: State<Boolean> = rememberUpdatedState(true),
    onPrimaryLoaded: () -> Unit = {},
    onTapPrimary: () -> Unit = {},
    onTapSecondary: () -> Unit = {},
    onDoubleTapPrimary: () -> Unit = {},
    onDraggingSecondary: (Boolean) -> Unit = {},
    onSwapImages: (isMediaSwitched: Boolean) -> Unit = {},
    imageLoader: StableImageLoader,
    onSecondaryAnchorChanged: (anchor: SecondaryAnchor) -> Unit = { },
    onZoomPrimary: (isZooming: Boolean) -> Unit = {},
    allowUserInteractions: DualViewInteractions = DualViewInteractions.AllInteractions,
) {
    Box(modifier = modifier) {
        DualViewNotBlurred(
            data = data,
            modifier = Modifier
                .matchParentSize(),
            dualViewConfig = dualViewConfig,
            onLongPress = onLongPress,
            onTapPrimary = onTapPrimary,
            onTapSecondary = onTapSecondary,
            onDoubleTapPrimary = onDoubleTapPrimary,
            onDraggingSecondary = onDraggingSecondary,
            onSwapImages = onSwapImages,
            imageLoader = imageLoader,
            shouldHideSecondary = shouldHideSecondary,
            onSecondaryAnchorChanged = onSecondaryAnchorChanged,
            onZoomPrimary = onZoomPrimary,
            allowUserInteractions = allowUserInteractions,
            players = players,
            isCurrentlyVisible = isCurrentlyVisible,
            canSwitchMedia = canSwitchMedia,
            isSecondaryVisibleWhenBtsNotPlaying = isSecondaryVisibleWhenBtsNotPlaying,
            canSwitchMediaFromPrimaryClick = canSwitchMediaFromPrimaryClick,
            onPrimaryLoaded = onPrimaryLoaded,
        )

        DualViewPlayersLoadingOverlay(
            players = players,
            isCurrentlyVisible = isCurrentlyVisible,
            modifier = Modifier
                .matchParentSize(),
        )

        // if the dual view is blurred, we add a blurred version of the pictures (or placeholders)
        // on top of the video or the images, and then perform a fade out (if the value was false and now true, and the view is actively visible)
        DualViewBlurredOverlay(
            data = data,
            blurOverlay = blurOverlay,
            imageLoader = imageLoader,
            modifier = Modifier
                .matchParentSize(),
        )
    }

    BindPlayerToVisibility(
        players = players,
        isCurrentlyVisible = isCurrentlyVisible,
        isDataBlurred = blurOverlay,
    )
}

@Composable
private fun DualViewNotBlurred(
    data: DualViewData,
    modifier: Modifier = Modifier,
    players: DualViewPlayers,
    onLongPress: (isLongPressing: Boolean) -> Unit = {},
    onTapPrimary: () -> Unit = {},
    onTapSecondary: () -> Unit = {},
    onDoubleTapPrimary: () -> Unit = {},
    onDraggingSecondary: (Boolean) -> Unit = {},
    onSwapImages: (isMediaSwitched: Boolean) -> Unit = {},
    imageLoader: StableImageLoader,
    shouldHideSecondary: State<Boolean>,
    isSecondaryVisibleWhenBtsNotPlaying: Boolean,
    isCurrentlyVisible: State<Boolean>,
    onSecondaryAnchorChanged: (anchor: SecondaryAnchor) -> Unit = { },
    onZoomPrimary: (isZooming: Boolean) -> Unit = {},
    allowUserInteractions: DualViewInteractions = DualViewInteractions.AllInteractions,
    dualViewConfig: DualViewConfig,
    canSwitchMedia: State<Boolean>,
    canSwitchMediaFromPrimaryClick: State<Boolean>,
    onPrimaryLoaded: () -> Unit = {},
) {
    val isPrimaryLongPressing = remember {
        mutableStateOf(false)
    }
    val isPrimaryZooming = remember {
        mutableStateOf(false)
    }

    // disable interactions during the BTS video play
    val allowInteractionsTransformed = transformDualViewInteractions(
        interactions = allowUserInteractions,
        players = players,
    )

    UpdateViewConfiguration(
        doubleTapTimeoutMillis = 120L,
        longPressTimeoutMillis = 200L,
        // touchSlop = 120f, // Default value, depending of the phone, is around 20.
    ) {
        if (data.secondary != null) {
            val isMediaSwitched = remember(data) {
                mutableStateOf(false)
            }

            ResetIsMediaSwitchedOnVisibilityChangedForBTS(
                isCurrentlyVisible = isCurrentlyVisible,
                setIsMediaSwitched = {
                    isMediaSwitched.value = it
                },
                players = players,
            )

            // returns a model that contains medias to display on the primary and secondary view
            // with their video players
            // it can be reverted if we update the isMediaSwitched (front <-> back)
            val mediasToDisplay = getMediasToDisplay(
                primary = data.primary,
                secondary = data.secondary,
                players = players,
                isMediaSwitched = {
                    isMediaSwitched.value
                },
            )

            val secondaryTranslationX = getSecondaryTranslationX(
                isCurrentlyVisible = isCurrentlyVisible,
                players = players,
                isSecondaryVisibleWhenNotPlaying = isSecondaryVisibleWhenBtsNotPlaying,
            )

            val shouldHideSecondaryValue by shouldHideSecondary

            val isSecondaryVisible = remember {
                derivedStateOf {
                    !isPrimaryLongPressing.value && !isPrimaryZooming.value && !shouldHideSecondaryValue
                }
            }

            DualViewLayout(
                modifier = modifier,
                dualViewConfig = dualViewConfig,
                primary = { primaryWidth, primaryHeight ->
                    DualPrimaryView(
                        media = mediasToDisplay.primary.media,
                        player = mediasToDisplay.primary.player,
                        modifier = Modifier.matchParentSize(),
                        onPlayerAction = {
                            players.onPlayerAction(it)
                        },
                        imageLoader = imageLoader,
                        imageSize = ImageSize.Fixed(
                            width = primaryWidth.toPx(),
                            height = primaryHeight.toPx(),
                        ),
                        isLongPressingPrimary = {
                            isPrimaryLongPressing.value
                        },
                        shouldFitIntoContainer = data.type.shouldFitIntoContainer(),
                        onLoaded = onPrimaryLoaded,
                    )
                },
                secondary = { primaryWidth, primaryHeight, secondaryWidth, secondaryHeight ->
                    AnimatedVisibility(
                        modifier = Modifier
                            .matchParentSize()
                            .graphicsLayer {
                                translationX = secondaryTranslationX.value
                            },
                        visible = isSecondaryVisible.value,
                        enter = fadeIn(),
                        exit = fadeOut(),
                    ) {
                        DualSecondaryView(
                            dualViewConfig = dualViewConfig,
                            media = mediasToDisplay.secondary.media,
                            player = mediasToDisplay.secondary.player,
                            imageLoader = imageLoader,
                            // set to the primary size to be sure it's the same cache key (when we switch the primary content, it's not loading again)
                            size = ImageSize.Fixed(
                                width = primaryWidth.toPx(),
                                height = primaryHeight.toPx(),
                            ),
                            modifier = Modifier
                                .matchParentSize(),
                        )
                    }
                },
                allowUserInteractions = allowInteractionsTransformed,
                onPrimaryLongPress = {
                    isPrimaryLongPressing.value = it
                    onLongPress(it)
                },
                onTapPrimary = {
                    if (canSwitchMedia.value && canSwitchMediaFromPrimaryClick.value) {
                        isMediaSwitched.value = !isMediaSwitched.value
                    }
                    onSwapImages(isMediaSwitched.value)
                    onTapPrimary()
                },
                onTapSecondary = {
                    if (canSwitchMedia.value) {
                        isMediaSwitched.value = !isMediaSwitched.value
                    }
                    onSwapImages(isMediaSwitched.value)
                    onTapSecondary()
                },
                onDraggingSecondary = onDraggingSecondary,
                onDoubleTapPrimary = onDoubleTapPrimary,
                onZoomPrimary = {
                    isPrimaryZooming.value = it
                    onZoomPrimary(it)
                },
                onSecondaryAnchorChanged = onSecondaryAnchorChanged,
            )
        } else {
            val mediasAndPlayer = getSingleMediaToDisplay(
                primary = data.primary,
                players = players,
            )
            DualViewLayout(
                modifier = modifier,
                dualViewConfig = dualViewConfig,
                primary = { primaryWidth, primaryHeight ->
                    DualPrimaryView(
                        media = mediasAndPlayer.media,
                        player = mediasAndPlayer.player,
                        modifier = Modifier.matchParentSize(),
                        onPlayerAction = {
                            players.onPlayerAction(it)
                        },
                        imageLoader = imageLoader,
                        imageSize = ImageSize.Fixed(
                            width = primaryWidth.toPx(),
                            height = primaryHeight.toPx(),
                        ),
                        isLongPressingPrimary = {
                            isPrimaryLongPressing.value
                        },
                        shouldFitIntoContainer = data.type.shouldFitIntoContainer(),
                        onLoaded = onPrimaryLoaded,
                    )
                },
                secondary = null, // null if we want to skip it
                allowUserInteractions = allowInteractionsTransformed,
                onPrimaryLongPress = {
                    isPrimaryLongPressing.value = it
                    onLongPress(it)
                },
                onTapPrimary = {
                    onTapPrimary()
                },
                onTapSecondary = {},
                onDraggingSecondary = {},
                onDoubleTapPrimary = onDoubleTapPrimary,
                onZoomPrimary = {
                    isPrimaryZooming.value = it
                    onZoomPrimary(it)
                },
                onSecondaryAnchorChanged = {},
            )
        }
    }
}

@Preview(name = "Images", group = "images")
@Composable
private fun DualViewPreview() {
    BeRealTheme {
        DualView(
            data = DualViewData.previewImages(),
            imageLoader = LocalStableImageLoader.current,
            modifier = Modifier
                .aspectRatio(9f / 16f),
            isCurrentlyVisible = rememberUpdatedState(true),
        )
    }
}

@Preview(name = "Dual Videos", group = "video")
@Composable
private fun DualViewPreview_videos() {
    BeRealTheme {
        DualView(
            data = DualViewData.previewVideos(),
            imageLoader = LocalStableImageLoader.current,
            modifier = Modifier
                .aspectRatio(9f / 16f),
            isCurrentlyVisible = rememberUpdatedState(true),
        )
    }
}

@Preview(name = "BTS", group = "video")
@Composable
private fun DualViewPreview_bts() {
    BeRealTheme {
        DualView(
            data = DualViewData.previewBts(),
            imageLoader = LocalStableImageLoader.current,
            modifier = Modifier
                .aspectRatio(9f / 16f),
            isCurrentlyVisible = rememberUpdatedState(true),
        )
    }
}

@Preview(name = "blurred", group = "blur")
@Composable
private fun DualViewPreview_blurred() {
    BeRealTheme {
        DualView(
            data = DualViewData.previewBts(),
            imageLoader = LocalStableImageLoader.current,
            modifier = Modifier
                .aspectRatio(9f / 16f),
            isCurrentlyVisible = rememberUpdatedState(true),
            blurOverlay = true,
        )
    }
}

@Preview(name = "blur", group = "blur")
@Composable
private fun DualViewPreview_blurred_dynamic() {
    BeRealTheme {
        var isBlurred: Boolean by remember {
            mutableStateOf(false)
        }
        Box(contentAlignment = Alignment.Center) {
            DualView(
                data = DualViewData.previewImages(),
                imageLoader = LocalStableImageLoader.current,
                modifier = Modifier
                    .aspectRatio(9f / 16f),
                isCurrentlyVisible = rememberUpdatedState(true),
                blurOverlay = isBlurred,
            )
            BeRealButton(
                buttonSize = ButtonSize.Small,
                buttonStyle = ButtonStyle.Primary,
                text = "toggle blur",
                onClick = { isBlurred = !isBlurred },
            )
        }
    }
}

@Preview(name = "Images", group = "no_secondary")
@Composable
private fun DualViewPreview_noSecondary() {
    BeRealTheme {
        DualView(
            data = DualViewData.previewMediasFromGallery(),
            imageLoader = LocalStableImageLoader.current,
            modifier = Modifier
                .aspectRatio(9f / 16f),
            isCurrentlyVisible = rememberUpdatedState(true),
        )
    }
}
