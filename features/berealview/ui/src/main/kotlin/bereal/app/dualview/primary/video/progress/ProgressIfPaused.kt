package bereal.app.dualview.primary.video.progress

import androidx.compose.animation.Crossfade
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.berealview.video.ProgressBarState
import bereal.app.design.StadiumShape
import bereal.app.design.isInPreview
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerState
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * Progress bar is visible only if we long press the primary, and it becomes paused
 */
@Composable
internal fun getProgressBarState(
    media: DualViewData.Media.Video,
    player: DualViewPlayer,
    isLongPressingPrimary: () -> Boolean,
): ProgressBarState {
    val isLongPressingPrimaryValue = isLongPressingPrimary()
    if (isLongPressingPrimaryValue) {
        val playState: BeRealVideoPlayerState by player.playerState.collectAsStateWithLifecycle()
        return remember(media, playState) {
            when (playState) {
                BeRealVideoPlayerState.Loading,
                BeRealVideoPlayerState.NearStopped,
                BeRealVideoPlayerState.Stopped,
                BeRealVideoPlayerState.Playing,
                -> ProgressBarState.Hidden

                BeRealVideoPlayerState.Paused -> ProgressBarState.Visible
            }
        }
    } else {
        return ProgressBarState.Hidden
    }
}

@Composable
internal fun ProgressIfPaused(
    progressBar: ProgressBarState,
    player: DualViewPlayer,
    modifier: Modifier = Modifier,
) {
    Crossfade(
        targetState = progressBar,
        modifier = modifier,
        animationSpec = tween(200, easing = LinearEasing),
        label = "progressBar",
    ) { progressBar ->
        when (progressBar) {
            ProgressBarState.Hidden -> {
                // no op
            }

            ProgressBarState.Visible -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = remember {
                                Brush.verticalGradient(
                                    colors = persistentListOf(
                                        Color.Black.copy(alpha = 0f),
                                        Color.Black.copy(alpha = 0.3f),
                                    ),
                                )
                            },
                        ),
                ) {
                    PlayerProgressBar(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .padding(all = 30.dp)
                            .height(8.dp),
                        player = player,
                    )
                }
            }
        }
    }
}

@Composable
fun PlayerProgressBar(
    modifier: Modifier = Modifier,
    player: DualViewPlayer,
) {
    val percent = playerCurrentProgress(player)

    PlayerProgressBar(
        modifier = modifier,
        percent = {
            val percentValue by percent
            percentValue
        },
    )
}

@Composable
private fun playerCurrentProgress(player: DualViewPlayer): State<Float> {
    val percent = remember {
        mutableFloatStateOf(0f)
    }
    if (isInPreview) {
        LaunchedEffect(player) {
            percent.floatValue = 0.3f
        }
    } else {
        LaunchedEffect(key1 = player) {
            val p = player.exoplayer.stable
            try {
                while (isActive) {
                    percent.floatValue =
                        ((p.currentPosition.toFloat() / p.duration.toFloat())).coerceIn(0f, 1f)
                    delay(300)
                }
            } catch (t: Throwable) {
                t.printStackTrace()
            }
        }
    }
    return percent
}

@Composable
private fun PlayerProgressBar(
    modifier: Modifier = Modifier,
    percent: @Composable () -> Float,
) {
    val percentValue = percent()
    Box(
        modifier = modifier
            .clip(StadiumShape)
            .background(Color.White.copy(alpha = 0.2f))
            .drawWithCache {
                onDrawWithContent {
                    val newWidth = this.size.width * percentValue
                    val newHeight = this.size.height
                    drawContent()
                    drawRect(
                        color = Color.White,
                        style = Fill,
                        topLeft = Offset.Zero,
                        size = Size(newWidth, newHeight),
                    )
                }
            },
    )
}

@Composable
@Preview
private fun PlayerProgressBarPreview() {
    BeRealTheme {
        PlayerProgressBar(
            percent = { 0.2f },
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = 30.dp)
                .height(8.dp),
        )
    }
}
