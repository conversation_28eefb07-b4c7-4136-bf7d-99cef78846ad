package bereal.app.berealview.state.secondary

import androidx.compose.ui.geometry.Offset
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel

interface SecondaryEventsCollector {
    fun onStartSecondaryDrag()
    fun onEndSecondaryDrag()
    fun onDragSecondaryCancel()
    fun onDragSecondary(dragAmount: Offset)
    fun onSecondaryClicked(
        containerData: BeRealViewContainerDataUiModel,
    )
}
