package bereal.app.dualview.player

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import bereal.app.design.isInPreview
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.noop.DualViewPlayersNoOp
import bereal.app.dualview.player.provider.DualViewPlayersProviderNoOp
import bereal.app.dualview.player.provider.LocalDualViewPlayersProvider

@Composable
fun rememberDualViewPlayers(
    data: DualViewData,
): DualViewPlayers {
    val provider = if (isInPreview) {
        DualViewPlayersProviderNoOp()
    } else {
        LocalDualViewPlayersProvider.current
    }

    val players = remember(provider, data) {
        provider.providePlayer(data)
    }

    if (provider.isBoundToComposeLifecycle && players !is DualViewPlayersNoOp) {
        DisposableEffect(key1 = players) {
            onDispose {
                players.dispose()
            }
        }
        LaunchedEffect(players, data) {
            players.preload(data)
        }
    }

    return players
}
