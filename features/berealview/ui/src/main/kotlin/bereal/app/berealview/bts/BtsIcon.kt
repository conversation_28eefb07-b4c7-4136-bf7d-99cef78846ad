package bereal.app.berealview.bts

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.expandHorizontally
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkHorizontally
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Lock
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import bereal.app.design.R
import bereal.app.design.theme.BeRealTheme
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun BtsIcon(
    modifier: Modifier = Modifier,
    provideIsExpanded: () -> Boolean,
    provideIsIconLocked: () -> Boolean,
    backgroundColor: Color = BeRealTheme.colors.background.copy(alpha = 0.33f),
    animateIcon: Boolean,
) {
    val extraExpandedDp by animateDpAsState(
        targetValue = if (provideIsExpanded()) 6.dp else 0.dp,
        label = "horizontalPadding",
        animationSpec = TweenSpec(),
    )

    Row(
        modifier = modifier
            .background(backgroundColor.copy(alpha = 0.5f), RoundedCornerShape(percent = 50))
            .padding(
                horizontal = 2.dp + extraExpandedDp * if (provideIsExpanded()) 2 else 0,
                vertical = 2.dp + extraExpandedDp,
            ),
        verticalAlignment = CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        if (provideIsIconLocked()) {
            val painter = rememberVectorPainter(image = Icons.Default.Lock)
            Icon(
                modifier = Modifier.size(if (provideIsExpanded()) 18.dp else 12.dp),
                tint = BeRealTheme.colors.onBackground,
                painter = painter,
                contentDescription = null,
            )
        } else {
            AnimatedIcon(
                animated = animateIcon,
            )
        }

        AnimatedVisibility(
            visible = provideIsExpanded(),
            enter = fadeIn() + expandHorizontally() + expandVertically(),
            exit = fadeOut() + shrinkHorizontally() + shrinkVertically(),
        ) {
            Text(
                modifier = Modifier.padding(start = BeRealTheme.spacing.xs),
                text = "BTS", // TODO: Lokalize?
                style = BeRealTheme.typography.footnote.bold,
                textAlign = TextAlign.Center,
                color = BeRealTheme.colors.onSurface,
            )
        }
    }
}

@Composable
private fun AnimatedIcon(
    modifier: Modifier = Modifier,
    animated: Boolean,
) {
    val lottieFile by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.lottie_bts_on))
    val lottieAnimation by animateLottieCompositionAsState(
        composition = lottieFile,
        iterations = ANIMATION_ITERATIONS,
        isPlaying = animated,
    )

    LottieAnimation(
        composition = lottieFile,
        progress = { lottieAnimation },
        contentScale = ContentScale.FillWidth,
        modifier = modifier.size(16.dp),
    )
}

private const val ANIMATION_ITERATIONS = 1
