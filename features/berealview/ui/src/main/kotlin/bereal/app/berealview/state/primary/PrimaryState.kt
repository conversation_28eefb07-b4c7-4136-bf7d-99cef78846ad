package bereal.app.berealview.state.primary

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.State
import bereal.app.design.berealimageview.model.LongPressInteractionStateEventUiModel
import kotlinx.coroutines.flow.Flow

@Immutable
interface PrimaryState {
    val isUserInteracting: State<Boolean>
    val onClicked: Flow<Unit>
    val onDoubleClick: Flow<Unit>
    val isZooming: State<Boolean>
    val longPressingState: State<LongPressInteractionStateEventUiModel>
    val isImageLoaded: State<Boolean>
    val isPrimaryImageTransparent: State<Boolean>
    val shouldFitPrimaryIntoContainer: State<Boolean>
}
