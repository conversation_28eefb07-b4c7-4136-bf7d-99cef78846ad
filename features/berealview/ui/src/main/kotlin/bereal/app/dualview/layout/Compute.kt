package bereal.app.dualview.layout

import androidx.compose.ui.geometry.Offset
import bereal.app.dualview.layout.model.SecondaryDraggableConfig
import bereal.app.dualview.model.SecondaryAnchor

internal fun computeResetAnchor(
    secondaryDragOffset: Offset,
    config: SecondaryDraggableConfig,
): SecondaryAnchor {
    val currentSecondaryRightCorner = secondaryDragOffset.x + (config.secondaryWidthPX / 2f)
    return if (currentSecondaryRightCorner > config.draggableZoneWidth / 2f) {
        SecondaryAnchor.TopRight
    } else {
        SecondaryAnchor.TopLeft
    }
}

internal fun computeNewOffset(
    currentOffset: Offset,
    dragAmount: Offset,
    config: SecondaryDraggableConfig,
): Offset {
    val newValue = currentOffset + dragAmount

    return newValue.copy(
        x = newValue.x.coerceIn(
            minimumValue = 0f,
            maximumValue = config.maxDragX,
        ),
        y = newValue.y.coerceIn(
            minimumValue = 0f,
            maximumValue = config.maxDragY,
        ),
    )
}
