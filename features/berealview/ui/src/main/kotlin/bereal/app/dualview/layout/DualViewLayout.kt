package bereal.app.dualview.layout

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.commonandroid.thenIf
import bereal.app.design.Zoomable
import bereal.app.design.noRippleDebounceClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.layout.anim.AnimateResetSecondaryToAnchor
import bereal.app.dualview.layout.model.SecondaryDraggableConfig
import bereal.app.dualview.model.DualViewConfig
import bereal.app.dualview.model.DualViewInteractions
import bereal.app.dualview.model.SecondaryAnchor
import bereal.app.dualview.model.whiteBorderDualViewConfig

@Composable
fun DualViewLayout(
    primary: @Composable BoxScope.(primaryWidth: Dp, primaryHeight: Dp) -> Unit,
    secondary: (@Composable BoxScope.(primaryWidth: Dp, primaryHeight: Dp, secondaryWidth: Dp, secondaryHeight: Dp) -> Unit)?,
    allowUserInteractions: DualViewInteractions,
    onPrimaryLongPress: (isLongPressing: Boolean) -> Unit,
    onTapPrimary: () -> Unit,
    onTapSecondary: () -> Unit,
    onDraggingSecondary: (Boolean) -> Unit,
    onZoomPrimary: (isZooming: Boolean) -> Unit,
    onDoubleTapPrimary: () -> Unit,
    modifier: Modifier = Modifier,
    onSecondaryAnchorChanged: (anchor: SecondaryAnchor) -> Unit,
    dualViewConfig: DualViewConfig,
) {
    val isPressingPrimary = remember {
        mutableStateOf(false)
    }

    val canDrag = remember(allowUserInteractions) {
        allowUserInteractions.drag
    }

    ListenLongPress(
        onPrimaryLongPress = onPrimaryLongPress,
        isPressingPrimary = {
            isPressingPrimary.value
        },
    )

    BoxWithConstraints(modifier = modifier) {
        val containerMaxWidth = maxWidth
        val containerMaxHeight = maxHeight

        val primaryWidth = containerMaxWidth.coerceAtLeast(0.dp)
        val primaryHeight = containerMaxHeight.coerceAtLeast(0.dp)

        Zoomable(
            onZoom = onZoomPrimary,
            modifier = Modifier
                .matchParentSize()
                .pointerInput(allowUserInteractions) {
                    if (allowUserInteractions.hasPressGesture) {
                        detectTapGestures(
                            onLongPress = allowUserInteractions.longPressPrimary.takeIf { it }?.let {
                                {
                                    isPressingPrimary.value = true
                                }
                            },
                            onPress = allowUserInteractions.longPressPrimary.takeIf { it }?.let {
                                {
                                    tryAwaitRelease()
                                    isPressingPrimary.value = false
                                }
                            } ?: {},
                            onTap = allowUserInteractions.tap.takeIf { it }?.let {
                                {
                                    onTapPrimary()
                                }
                            },
                            onDoubleTap = allowUserInteractions.doubleTap.takeIf { it }?.let {
                                {
                                    onDoubleTapPrimary()
                                }
                            },
                        )
                    }
                },
            onEvent = {
            },
            isZoomable = allowUserInteractions.zoom,
        ) {
            primary(primaryWidth, primaryHeight)
        }

        if (secondary != null) {
            val secondaryWidth = remember(containerMaxWidth, dualViewConfig) {
                (containerMaxWidth * dualViewConfig.secondaryWidthPercent).coerceAtLeast(0.dp)
            }
            val secondaryHeight = remember(secondaryWidth) {
                (secondaryWidth * 4f / 3f).coerceAtLeast(0.dp)
            }

            BoxWithConstraints(
                // the draggable area
                modifier = Modifier
                    .fillMaxSize()
                    .padding(dualViewConfig.secondaryPadding),
            ) {
                val config = secondaryDraggableConfig(
                    secondaryWidth = secondaryWidth,
                    secondaryHeight = secondaryHeight,
                )

                // current secondary offset
                val secondaryDragOffset = remember {
                    mutableStateOf(Offset.Zero)
                }

                // if we want to reset the secondary, set this to SecondaryAnchor.TopLeft or TopRight
                val resetToBound = remember {
                    mutableStateOf<SecondaryAnchor?>(null)
                }

                if (canDrag) {
                    ListenSecondaryAnchor(
                        secondaryDragOffset = {
                            secondaryDragOffset.value
                        },
                        config = config,
                        onSecondaryAnchorChanged = onSecondaryAnchorChanged,
                    )

                    AnimateResetSecondaryToAnchor(
                        config = config,
                        resetToBound = {
                            resetToBound.value
                        },
                        onFinished = {
                            resetToBound.value = null
                        },
                        secondaryDragOffset = {
                            secondaryDragOffset.value
                        },
                        updateSecondaryDragOffset = {
                            secondaryDragOffset.value = it
                        },
                    )
                }

                Box(
                    modifier = Modifier
                        .size(secondaryWidth, secondaryHeight)
                        .graphicsLayer {
                            val offset = secondaryDragOffset.value
                            translationX = offset.x
                            translationY = offset.y
                        }
                        .noRippleDebounceClickable(
                            onClick = onTapSecondary,
                        )
                        .thenIf(canDrag) {
                            Modifier
                                .pointerInput(canDrag) {
                                    detectDragGestures(
                                        onDragStart = {
                                            onDraggingSecondary(true)
                                        },
                                        onDragEnd = {
                                            onDraggingSecondary(false)
                                            resetToBound.value =
                                                computeResetAnchor(
                                                    secondaryDragOffset.value,
                                                    config,
                                                )
                                        },
                                        onDragCancel = {
                                            onDraggingSecondary(false)
                                            resetToBound.value =
                                                computeResetAnchor(
                                                    secondaryDragOffset.value,
                                                    config,
                                                )
                                        },
                                    ) { change, dragAmount ->
                                        change.consume()

                                        secondaryDragOffset.value = computeNewOffset(
                                            currentOffset = secondaryDragOffset.value,
                                            dragAmount = dragAmount,
                                            config = config,
                                        )
                                    }
                                }
                        },
                ) {
                    secondary(primaryWidth, primaryHeight, secondaryWidth, secondaryHeight)
                }
            }
        }
    }
}

@Composable
private fun ListenLongPress(
    onPrimaryLongPress: (isLongPressing: Boolean) -> Unit,
    isPressingPrimary: () -> Boolean,
) {
    val onLongPressingCallback by rememberUpdatedState(onPrimaryLongPress)
    val isPressingPrimaryValue = isPressingPrimary()
    LaunchedEffect(isPressingPrimaryValue) {
        onLongPressingCallback(isPressingPrimaryValue)
    }
}

@Composable
private fun ListenSecondaryAnchor(
    secondaryDragOffset: () -> Offset,
    config: SecondaryDraggableConfig,
    onSecondaryAnchorChanged: (anchor: SecondaryAnchor) -> Unit,
) {
    val currentSecondaryDragOffset = secondaryDragOffset()
    val onSecondaryAnchorChangedCallback by rememberUpdatedState(onSecondaryAnchorChanged)

    LaunchedEffect(currentSecondaryDragOffset, config) {
        onSecondaryAnchorChangedCallback(
            computeResetAnchor(currentSecondaryDragOffset, config),
        )
    }
}

@Preview
@Composable
private fun DualViewLayoutPreview() {
    BeRealTheme {
        DualViewLayout(
            primary = { _, _ ->
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .background(Color.Red),
                )
            },
            secondary = { primaryWidth, primaryHeight, secondaryWidth, secondaryHeight ->
                val shape = RoundedCornerShape(20.dp)
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .shadow(
                            shape = shape,
                            elevation = 10.dp,
                        )
                        .clip(shape)
                        .border(
                            width = 1.dp,
                            color = BeRealTheme.colors.grayScale.realWhite.copy(alpha = 0.2f),
                            shape = shape,
                        )
                        .background(Color.Blue), // fake content
                )
            },
            allowUserInteractions = DualViewInteractions.AllInteractions,
            onPrimaryLongPress = {},
            onTapPrimary = {},
            onTapSecondary = {},
            onDraggingSecondary = {},
            onZoomPrimary = {},
            onDoubleTapPrimary = {},
            modifier = Modifier.fillMaxSize(),
            onSecondaryAnchorChanged = {},
            dualViewConfig = whiteBorderDualViewConfig,
        )
    }
}

@Preview
@Composable
private fun DualViewLayoutPreview_noSecondary() {
    BeRealTheme {
        DualViewLayout(
            primary = { _, _ ->
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .background(Color.Red),
                )
            },
            secondary = null,
            allowUserInteractions = DualViewInteractions.AllInteractions,
            onPrimaryLongPress = {},
            onTapPrimary = {},
            onTapSecondary = {},
            onDraggingSecondary = {},
            onZoomPrimary = {},
            onDoubleTapPrimary = {},
            modifier = Modifier.fillMaxSize(),
            onSecondaryAnchorChanged = {},
            dualViewConfig = whiteBorderDualViewConfig,
        )
    }
}
