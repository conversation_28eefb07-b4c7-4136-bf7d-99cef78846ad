package bereal.app.dualview.player.provider

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.DualViewPlayers
import org.koin.compose.koinInject
import org.koin.core.annotation.Factory

@Composable
fun rememberDefaultDualViewPlayersProvider(): DualViewPlayersProvider {
    val provider: DualViewPlayersProviderDefault = koinInject()
    return provider
}

@Factory
@Immutable
class DualViewPlayersProviderDefault(
    private val factory: DualViewPlayerFactory,
) : DualViewPlayersProvider {

    override val isBoundToComposeLifecycle: Boolean = true

    override fun providePlayer(data: DualViewData): DualViewPlayers {
        return factory.createPlayer(data)
    }
}
