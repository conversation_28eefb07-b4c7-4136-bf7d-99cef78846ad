package bereal.app.berealview.video

import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import bereal.app.video.ui.VideoView
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer

@Composable
internal fun BoxScope.PlayingOrPausedState(
    player: StablePlayer,
    playerState: BeRealVideoPlayerState,
    overrideOtherPlayer: Boolean,
    containerDataId: String,
    muteButtonState: MuteButtonState,
    muteButtonPadding: PaddingValues,
    playButtonState: PlayButtonState,
    onEvent: ((Boolean) -> Unit)?,
    onVideoClick: (() -> Unit)?,
) {
    VideoView(
        playerVideo = player,
        onClick = onVideoClick,
        tag = containerDataId,
        overrideOtherPlayer = overrideOtherPlayer,
    )

    // TODO VIDEO: Move onEvent invoke in BeRealViewState?
    when {
        playerState is BeRealVideoPlayerState.Paused && playButtonState is PlayButtonState.Visible -> {
            PlayButton(state = playButtonState)
            onEvent?.invoke(false)
        }
        playerState is BeRealVideoPlayerState.NearStopped -> {
            // NearStopped state to show in advance the secondary content, as right when the video ends.
            onEvent?.invoke(false)
        }
        else -> {
        }
    }

    if (muteButtonState is MuteButtonState.Visible) {
        MuteButton(
            modifier = Modifier.align(Alignment.TopEnd).padding(muteButtonPadding).padding(12.dp),
            state = muteButtonState,
        )
    }
}
