package bereal.app.dualview.player.provider

import bereal.app.bts.ui.player.SimpleBTSVideoPlayer
import bereal.app.bts.ui.player.SimpleBeRealVideoPlayer
import bereal.app.bts.ui.player.SimpleSingleVideoPlayer
import bereal.app.dualview.bts.players.DualViewPlayersBts
import bereal.app.dualview.dualvideo.players.DualViewPlayersTwoVideos
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.video
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.noop.DualViewPlayersNoOp
import bereal.app.dualview.singlevideo.players.DualViewPlayersSingleVideo
import org.koin.core.annotation.Single
import org.koin.core.component.KoinComponent

/**
 * Responsible of players creation
 */
@Single
class DualViewPlayerFactory : KoinComponent {
    fun createPlayer(data: DualViewData): DualViewPlayers {
        return when (data.type) {
            is DualViewData.Type.Images -> {
                DualViewPlayersNoOp()
            }

            is DualViewData.Type.DualVideo -> {
                val simpleBeRealVideoPlayer = getKoin().get<SimpleBeRealVideoPlayer>()
                DualViewPlayersTwoVideos(
                    player = simpleBeRealVideoPlayer,
                )
            }

            is DualViewData.Type.BTS -> {
                val simpleBTSVideoPlayer = getKoin().get<SimpleBTSVideoPlayer>()
                DualViewPlayersBts(
                    player = simpleBTSVideoPlayer,
                )
            }

            is DualViewData.Type.SingleVideo -> {
                val simpleSingleVideoPlayer = getKoin().get<SimpleSingleVideoPlayer>()
                DualViewPlayersSingleVideo(
                    player = simpleSingleVideoPlayer,
                )
            }

            is DualViewData.Type.MediaFromGallery -> {
                if (data.primary.video() != null) {
                    val simpleSingleVideoPlayer = getKoin().get<SimpleSingleVideoPlayer>()
                    DualViewPlayersSingleVideo(
                        player = simpleSingleVideoPlayer,
                    )
                } else {
                    DualViewPlayersNoOp()
                }
            }
        }
    }
}
