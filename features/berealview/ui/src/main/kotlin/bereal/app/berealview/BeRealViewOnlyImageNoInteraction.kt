package bereal.app.berealview

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.media3.common.util.UnstableApi
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.image.BeRealImageType
import bereal.app.berealview.primary.PrimaryView
import bereal.app.berealview.secondary.SecondaryView
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent
import bereal.app.image.core.imageloader.StableImageLoader

@UnstableApi
@Composable
fun BeRealViewOnlyImageNoInteraction(
    modifier: Modifier = Modifier,
    state: BeRealViewUiState,
    primaryImage: BeRealViewDataUiModel,
    secondaryImage: BeRealViewDataUiModel?,
    shouldBlurImage: Boolean = false,
    allowHardware: Boolean = true, // for taking screenshot of the view https://github.com/coil-kt/coil/issues/159
    imageLoader: StableImageLoader,
    imageProperties: BeRealImageProperties,
) {
    val visibleLargeImage by state.visibleLargeContent

    val beRealImageProperties by remember(imageProperties) {
        mutableStateOf(
            imageProperties.copy(
                type = BeRealImageType.Regular,
            ),
        )
    }

    Box(
        modifier,
    ) {
        BeRealStackViewOnlyImageNoInteraction(
            primaryViewData = primaryImage,
            secondaryViewData = secondaryImage,
            shouldBlurImage = shouldBlurImage,
            allowHardware = allowHardware,
            imageLoader = imageLoader,
            imageProperties = beRealImageProperties,
            visibleLargeImage = visibleLargeImage,
            state = state,
        )
    }
}

@UnstableApi
@Composable
private fun BeRealStackViewOnlyImageNoInteraction(
    state: BeRealViewUiState,
    primaryViewData: BeRealViewDataUiModel,
    secondaryViewData: BeRealViewDataUiModel?,
    shouldBlurImage: Boolean,
    allowHardware: Boolean, // for taking screenshot of the view https://github.com/coil-kt/coil/issues/159
    imageLoader: StableImageLoader,
    imageProperties: BeRealImageProperties,
    visibleLargeImage: VisibleBerealViewLargeContent,
) {
    val isPrimaryImageTransparent by remember {
        state.primary.isPrimaryImageTransparent
    }

    PrimaryView(
        primaryData = when (visibleLargeImage) {
            VisibleBerealViewLargeContent.Primary -> primaryViewData
            VisibleBerealViewLargeContent.Secondary ->
                secondaryViewData
                    ?: primaryViewData // fallback to primaryImage if secondary is null
        },
        secondaryData = when (visibleLargeImage) {
            VisibleBerealViewLargeContent.Primary ->
                secondaryViewData
                    ?: primaryViewData // fallback to primaryImage if secondary is null
            VisibleBerealViewLargeContent.Secondary -> primaryViewData
        },
        shouldBlurImage = shouldBlurImage,
        isPrimaryImageTransparent = isPrimaryImageTransparent,
        allowHardware = allowHardware,
        imageLoader = imageLoader,
        state = state,
        imageProperties = imageProperties,
        withLoading = false,
        muteButtonPadding = PaddingValues(),
        withPrimaryBorder = { false },
    )

    secondaryViewData?.let { ensuredSecondaryImage ->
        SecondaryView(
            data = when (visibleLargeImage) {
                VisibleBerealViewLargeContent.Primary -> ensuredSecondaryImage
                VisibleBerealViewLargeContent.Secondary -> primaryViewData
            },
            state = state,
            shouldBlurImage = shouldBlurImage,
            allowHardware = allowHardware,
            imageLoader = imageLoader,
            imageProperties = imageProperties,
        )
    }
}
