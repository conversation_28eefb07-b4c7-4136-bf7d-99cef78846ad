package bereal.app.dualview.player

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import bereal.app.dualview.bts.players.DualViewPlayersBts

@Composable
internal fun ResetIsMediaSwitchedOnVisibilityChangedForBTS(
    setIsMediaSwitched: (isMediaSwitched: Boolean) -> Unit,
    isCurrentlyVisible: State<Boolean>,
    players: DualViewPlayers,
) {
    when (players) {
        is DualViewPlayersBts -> {
            val setIsMediaSwitchedCallback by rememberUpdatedState(newValue = setIsMediaSwitched)
            val isCurrentlyVisibleValue by isCurrentlyVisible
            LaunchedEffect(isCurrentlyVisibleValue) {
                if (!isCurrentlyVisibleValue) { // everytime the dual view becomes non visible, I revert the medias position
                    setIsMediaSwitchedCallback(false)
                }
            }
        }
        else -> {
            // no op
        }
    }
}
