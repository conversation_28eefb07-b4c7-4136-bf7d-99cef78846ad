package bereal.app.dualview.player

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.lifecycle.compose.collectAsStateWithLifecycle

@Immutable
sealed interface MuteButtonState {
    @Immutable
    data object Hidden : MuteButtonState

    @Immutable
    data class Visible(
        val isMuted: <PERSON>olean,
    ) : MuteButtonState
}

val DualViewPlayers.muteState: MuteButtonState
    @Composable
    get() {
        return if (this.primary == null) {
            MuteButtonState.Hidden
        } else {
            val playerMuteState by this.playerMuteState.collectAsStateWithLifecycle()
            remember(playerMuteState) {
                MuteButtonState.Visible(playerMuteState.isMuted)
            }
        }
    }
