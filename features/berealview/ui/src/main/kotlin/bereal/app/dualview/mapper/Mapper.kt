package bereal.app.dualview.mapper

import bereal.app.analytics.model.AnalyticsView
import bereal.app.bts.ui.model.VideoAnalyticsParams
import bereal.app.design.avatar.isOfficialAccount
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.dualview.model.DualViewData
import bereal.app.entities.CorePost
import bereal.app.entities.PostMedia
import bereal.app.entities.PostType
import bereal.app.entities.isMediaFromGallery
import bereal.app.video.ui.model.SimpleVideoUiModel
import bereal.app.video.ui.model.VideoSourceTypeUiModel

fun PostMedia.toDualViewData(videoSourceTypeUiModel: VideoSourceTypeUiModel = VideoSourceTypeUiModel.USER_VIDEO): DualViewData.Media {
    return when (this) {
        is PostMedia.Bts -> DualViewData.Media.Video(
            video = SimpleVideoUiModel(
                uri = btsMedia.uri,
                sourceType = videoSourceTypeUiModel,
            ),
            thumbnail = DualViewData.Media.Image(ImageDataModel.Url(picture.uri)),
        )

        is PostMedia.Picture -> DualViewData.Media.Image(image = ImageDataModel.Url(this.picture.uri))
        is PostMedia.Video -> DualViewData.Media.Video(
            SimpleVideoUiModel(uri = video.uri, sourceType = videoSourceTypeUiModel),
            thumbnail = DualViewData.Media.Image(ImageDataModel.Url(picture.uri)),
        )
    }
}

fun CorePost.toDualViewType(analyticsView: AnalyticsView): DualViewData.Type {
    return when (this.type) {
        PostType.Regular -> DualViewData.Type.Images
        PostType.FreeRide -> DualViewData.Type.DualVideo(
            videoAnalyticsParams = this.toVideoAnalyticsParams(
                analyticsView = analyticsView,
            ),
        )

        PostType.Bts -> DualViewData.Type.BTS(
            videoAnalyticsParams = this.toVideoAnalyticsParams(
                analyticsView = analyticsView,
            ),
        )

        PostType.DualVideo -> DualViewData.Type.DualVideo(
            videoAnalyticsParams = this.toVideoAnalyticsParams(
                analyticsView = analyticsView,
            ),
        )

        PostType.MediaFromGallery -> DualViewData.Type.MediaFromGallery
        PostType.MediaFromGalleryVideo, PostType.SingleVideo -> DualViewData.Type.SingleVideo
        PostType.SinglePhoto -> DualViewData.Type.Images
    }
}

fun CorePost.toVideoAnalyticsParams(analyticsView: AnalyticsView): VideoAnalyticsParams {
    return VideoAnalyticsParams(
        postId = this.id.postId,
        momentId = this.moment?.id ?: "",
        userId = this.owner.uid,
        isMain = false,
        isLate = this.isLate,
        view = analyticsView,
        // isMediaTypeVideo = this.primary.videoUrl() != null,
        // hasBtsContent = this.type == PostType.Bts,
        isOfficialUser = this.owner.isOfficialAccount,
        isReshare = this.parentPostInfo != null,
    )
}

fun CorePost.dualViewData(analyticsView: AnalyticsView): DualViewData {
    return DualViewData(
        primary = primary.toDualViewData(),
        secondary = if (isMediaFromGallery()) null else secondary?.toDualViewData(),
        type = toDualViewType(analyticsView = analyticsView),
    )
}
