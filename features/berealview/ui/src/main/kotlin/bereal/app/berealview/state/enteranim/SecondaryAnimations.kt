package bereal.app.berealview.state.enteranim

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.tween
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.geometry.Offset
import bereal.app.berealview.state.secondary.SecondaryStateImpl
import bereal.app.design.animation.BeRealAnimationSpecs
import bereal.app.design.animation.BeRealCurve
import bereal.app.design.berealimageview.model.SecondaryAnimationType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

class SecondaryAnimations(
    private val scope: CoroutineScope,
    private val secondary: SecondaryStateImpl,
    private val secondaryPhotoEnterInitialAnimation: SecondaryAnimationType,
) {

    init {
        // To avoid flickering, if the animation is "none", values should be forced from start
        if (secondaryPhotoEnterInitialAnimation == SecondaryAnimationType.None) {
            runBlocking {
                secondary.secondaryAlpha.snapTo(1f)
                secondary.secondaryOffset.snapTo(Offset.Zero)
            }
        }
    }

    suspend fun animateEnter(secondaryPhotoEnterAnimation: SecondaryAnimationType = secondaryPhotoEnterInitialAnimation) {
        coroutineScope {
            when (secondaryPhotoEnterAnimation) {
                SecondaryAnimationType.Fade -> {
                    secondary.secondaryAlpha.animateTo(1f)
                }
                SecondaryAnimationType.EnterFromStart -> {
                    joinAll(
                        launch {
                            secondary.secondaryOffset.snapTo(
                                Offset(
                                    x = -500f,
                                    y = 0f,
                                ),
                            )
                            secondary.secondaryOffset.animateTo(
                                Offset.Zero,
                                tween(durationMillis = 400, easing = FastOutLinearInEasing),
                            )
                        },
                        launch {
                            secondary.secondaryAlpha.animateTo(
                                1f,
                                tween(durationMillis = 400, easing = FastOutLinearInEasing),
                            )
                        },
                    )
                }
                SecondaryAnimationType.None -> Unit
            }
        }
    }

    fun bindSecondaryAlpha() {
        scope.launch {
            snapshotFlow { secondary.isVisible.value }
                .collectLatest { isVisible ->
                    if (isVisible) {
                        secondary.secondaryAlpha.animateTo(
                            1f,
                            tween(
                                durationMillis = BeRealAnimationSpecs.DEFAULT_DISPLAY_DURATION,
                                easing = BeRealCurve.easeInOutCubic,
                            ),
                        )
                    } else {
                        secondary.secondaryAlpha.animateTo(
                            0f,
                            tween(
                                durationMillis = BeRealAnimationSpecs.DEFAULT_HIDE_DURATION,
                                easing = BeRealCurve.easeInOutCubic,
                            ),
                        )
                    }
                }
        }
    }
}
