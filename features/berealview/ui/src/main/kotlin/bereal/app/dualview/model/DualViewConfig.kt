package bereal.app.dualview.model

import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Immutable
data class DualViewConfig(
    val secondaryWidthPercent: Float,
    val secondaryPadding: Dp,
    val secondaryCornerRadius: Dp,
    val secondaryElevation: Dp,
    val secondaryBorderWidth: Dp,
    val secondaryBorderColor: Color,
)

val whiteBorderDualViewConfig = DualViewConfig(
    secondaryWidthPercent = 0.35f,
    secondaryPadding = 20.dp,
    secondaryCornerRadius = 20.dp,
    secondaryElevation = 10.dp,
    secondaryBorderWidth = 1.dp,
    secondaryBorderColor = Color.White.copy(alpha = 0.2f),
)

val chatDualViewConfig = DualViewConfig(
    secondaryWidthPercent = 0.35f,
    secondaryPadding = 10.dp,
    secondaryCornerRadius = 8.4.dp,
    secondaryElevation = 10.dp,
    secondaryBorderWidth = 1.dp,
    secondaryBorderColor = Color.White.copy(alpha = 0.2f),
)

val blackBorderDualViewConfig = DualViewConfig(
    secondaryWidthPercent = 0.31f,
    secondaryPadding = 16.dp,
    secondaryCornerRadius = 14.dp,
    secondaryElevation = 10.dp,
    secondaryBorderWidth = 2.dp,
    secondaryBorderColor = Color.Black,
)
