package bereal.app.berealview.video

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import bereal.app.design.R
import bereal.app.design.theme.BeRealTheme

@Composable
fun MuteButton(
    modifier: Modifier = Modifier,
    backgroundColor: Color = BeRealTheme.colors.background.copy(alpha = 0.33f),
    state: MuteButtonState.Visible,
) {
    Box(
        modifier = modifier
            .size(32.dp)
            .background(backgroundColor.copy(alpha = 0.5f), RoundedCornerShape(percent = 50))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(bounded = false),
            ) {
                state.onClick()
            },
        contentAlignment = Alignment.Center,
    ) {
        val drawableResId = if (state.isMuted) {
            R.drawable.baseline_volume_off_24
        } else {
            R.drawable.baseline_volume_24
        }

        Image(
            modifier = Modifier.size(18.dp),
            painter = painterResource(id = drawableResId),
            contentDescription = "",
        )
    }
}

@Immutable
sealed interface MuteButtonState {
    @Immutable
    data object Hidden : MuteButtonState

    @Immutable
    data class Visible(
        val isMuted: Boolean,
        val onClick: () -> Unit,
    ) : MuteButtonState
}
