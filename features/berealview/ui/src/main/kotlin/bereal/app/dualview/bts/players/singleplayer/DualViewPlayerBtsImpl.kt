package bereal.app.dualview.bts.players.singleplayer

import android.annotation.SuppressLint
import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.VideoDurationUiModel
import bereal.app.bts.ui.model.toUi
import bereal.app.bts.ui.player.SimpleBTSVideoPlayer
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map

@SuppressLint("UnsafeOptInUsageError")
@Immutable
internal data class DualViewPlayerBtsImpl(
    private val simpleBTSVideoPlayer: SimpleBTSVideoPlayer,
) : DualViewPlayer {
    override fun toggleMuted() {
        simpleBTSVideoPlayer.toggleMute()
    }

    override val playerType: DualViewPlayers.PlayerType = DualViewPlayers.PlayerType.BTS

    override val longPressToPause: Boolean = false

    override val playerMuteState: StateFlow<BeRealVideoPlayerMuteState> = simpleBTSVideoPlayer.playerMuteState

    override val exoplayer: StablePlayer = StablePlayer(simpleBTSVideoPlayer.player)

    override val playerState: StateFlow<BeRealVideoPlayerState> = simpleBTSVideoPlayer.playerState

    override val primaryPlayerDuration: Flow<VideoDurationUiModel> = simpleBTSVideoPlayer.duration.map {
        toUi(it)
    }
}
