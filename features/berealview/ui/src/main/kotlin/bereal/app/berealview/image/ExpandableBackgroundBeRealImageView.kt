package bereal.app.berealview.image

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.toImageOrThumbnailUrl
import bereal.app.design.image.Gradient
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.CrossFade
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.BeRealImageLoaders

@Composable
fun ExpandableBackgroundBeRealImageView(
    primaryImage: BeRealViewDataUiModel,
    backgroundAlpha: Float = 1f,
    imageLoaders: BeRealImageLoaders,
    maxHeight: Dp,
) {
    val gradientColor = BeRealTheme.colors.background
    val gradient = remember {
        Gradient.smoothGradient(
            to = gradientColor,
            from = gradientColor.copy(alpha = 0f),
            direction = Gradient.Direction.BottomTop,
        )
    }

    val primaryImageData = remember(primaryImage) {
        ImageDataModel.Url(url = primaryImage.toImageOrThumbnailUrl() ?: "")
    }

    Box(
        modifier = Modifier.graphicsLayer {
            alpha = backgroundAlpha
        },
    ) {
        RemoteImage(
            modifier = Modifier
                .fillMaxSize()
                .heightIn(0.dp, maxHeight),
            data = primaryImageData,
            imageLoader = imageLoaders.memories,
            options = ImageOptions(
                crossfade = CrossFade.Enabled(),
                hardware = false,
                size = ImageSize.Original,
                blurred = Blur(),
                maskColor = BeRealTheme.colors.background.copy(alpha = 0.7f),
            ),
            contentDescription = null,
            contentScale = ContentScale.Crop,
        )

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(86.dp)
                .background(gradient)
                .align(Alignment.BottomCenter),
        )
    }
}
