package bereal.app.dualview.bts.players

import android.annotation.SuppressLint
import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.DownloadState
import bereal.app.bts.ui.player.SimpleBTSVideoPlayer
import bereal.app.dualview.bts.players.singleplayer.DualViewPlayerBtsImpl
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.video
import bereal.app.dualview.model.videoAnalyticsParams
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.PlayerAction
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update

@SuppressLint("UnsafeOptInUsageError")
@Immutable
internal data class DualViewPlayersBts(
    private val player: SimpleBTSVideoPlayer,
) : DualViewPlayers {

    override val playerType = DualViewPlayers.PlayerType.BTS

    override val downloadState: StateFlow<DownloadState> = player.isPrimaryDownloaded

    override val primary: DualViewPlayer = DualViewPlayerBtsImpl(
        simpleBTSVideoPlayer = player,
    )

    // we increment this each time we play, so we can re-init the BTS video
    val playCounter = MutableStateFlow<Int>(0)

    override val secondary: DualViewPlayer? = null

    override val playerMuteState: StateFlow<BeRealVideoPlayerMuteState> = player.playerMuteState

    override fun toggleMuted() {
        player.toggleMute()
    }

    override fun play() {
        playCounter.update { it + 1 }
        player.play()
    }

    override fun resume() {
        player.resume()
    }

    override fun pause() {
        player.pause()
    }

    override fun dispose() {
        player.release()
    }

    override suspend fun preload(data: DualViewData) {
        data.primary.video()?.let { player.prepare(it, data.videoAnalyticsParams()) }
    }

    override fun onPlayerAction(action: PlayerAction) {
        when (action) {
            is PlayerAction.Play -> play()
            is PlayerAction.Pause -> pause()
        }
    }

    private var pausedOnLifecycle = false

    override fun onViewPaused() {
        when (player.playerState.value) {
            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Stopped,
            -> {
                // no op, only if playing
            }

            BeRealVideoPlayerState.NearStopped,
            BeRealVideoPlayerState.Loading,
            BeRealVideoPlayerState.Playing,
            -> {
                pausedOnLifecycle = true
                pause()
            }
        }
        pausedOnLifecycle = true
        pause()
    }

    override fun onViewResumed() {
        if (pausedOnLifecycle) {
            pausedOnLifecycle = false
            player.play()
        }
    }
}
