package bereal.app.dualview.blur

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.noRippleDebounceClickable
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.primary.blur.DualPrimaryViewBlurred
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun DualViewBlurredOverlay(
    data: DualViewData,
    blurOverlay: Boolean,
    imageLoader: StableImageLoader,
    modifier: Modifier = Modifier,
    imageSize: ImageSize = ImageSize.Original,
) {
    if (blurOverlay) {
        Box(
            modifier = modifier
                .background(Color.Black) // prevents seeing the video or images during the blur process
                .noRippleDebounceClickable {
                    // prevent all clicks behind & the pause long press
                },
        ) {
            DualPrimaryViewBlurred(
                media = data.primary,
                imageLoader = imageLoader,
                modifier = Modifier.matchParentSize(),
                imageSize = imageSize,
            )
        }
    }
}
