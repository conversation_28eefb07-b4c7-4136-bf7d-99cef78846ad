package bereal.app.berealview.state.primary

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.mutableStateOf
import bereal.app.design.berealimageview.model.LongPressInteractionStateEventUiModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.map

@Immutable
class PrimaryStateImpl : PrimaryState {
    internal val onPrimaryClicked = MutableSharedFlow<Long>()
    internal val onPrimaryDoubleClick = MutableSharedFlow<Long>()

    override val isUserInteracting = mutableStateOf(false)
    override val onClicked = onPrimaryClicked.map {}
    override val onDoubleClick = onPrimaryDoubleClick.map { }
    override val isZooming = mutableStateOf(false)
    override val longPressingState =
        mutableStateOf<LongPressInteractionStateEventUiModel>(LongPressInteractionStateEventUiModel.Released)
    override val isImageLoaded = mutableStateOf(false)
    override val isPrimaryImageTransparent = mutableStateOf(false)
    override val shouldFitPrimaryIntoContainer = mutableStateOf(false)
}
