package bereal.app.berealview.image

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import bereal.app.commonandroid.thenIf
import bereal.app.design.berealimageview.model.BeRealViewConstants.BLUR_RADIUS
import bereal.app.design.image.SubcomposeRemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.CrossFade
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.noRippleClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
fun SecondaryImageContent(
    image: ImageDataModel,
    aspectRatio: Float,
    cacheSize: ImageSize,
    shouldBlurImage: Boolean,
    allowHardware: Boolean,
    imageProperties: BeRealImageProperties,
    imageLoader: StableImageLoader,
    imagePlaceholder: String? = null,
    canClick: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var isLoaded by remember { mutableStateOf(false) }
    val isClickable by remember(isLoaded, shouldBlurImage, canClick) {
        derivedStateOf {
            isLoaded && !shouldBlurImage && canClick
        }
    }

    SubcomposeRemoteImage(
        modifier = modifier,
        data = image,
        imageLoader = imageLoader,
        options = ImageOptions(
            crossfade = if (imagePlaceholder == null) CrossFade.Enabled(200) else CrossFade.Disabled,
            hardware = allowHardware,
            size = cacheSize,
            blurred = if (shouldBlurImage) Blur(
                radius = BLUR_RADIUS,
                sampling = 10f,
            ) else null,
        ),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        success = {
            DrawWithSamePainter(
                modifier = Modifier
                    .fillMaxSize()
                    .thenIf(isClickable) {
                        Modifier.noRippleClickable(onClick = onClick)
                    },
            )
        },
        loading = {
            if (imagePlaceholder != null) {
                DrawWithSamePainter(
                    data = ImageDataModel.Url(imagePlaceholder),
                    modifier = Modifier
                        .width(imageProperties.secondaryWidthDp.dp)
                        .aspectRatio(aspectRatio)
                        .clip(RoundedCornerShape(imageProperties.secondaryRadiusDp.dp))
                        .border(
                            width = imageProperties.secondaryBorderDp.dp,
                            color = BeRealTheme.colors.background,
                            shape = RoundedCornerShape(imageProperties.secondaryRadiusDp.dp),
                        ),
                )
            }
        },
        onLoadedSuccess = {
            isLoaded = true
        },
    )
}
