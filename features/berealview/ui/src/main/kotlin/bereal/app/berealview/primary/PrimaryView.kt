package bereal.app.berealview.primary

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import androidx.media3.common.util.UnstableApi
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.commonandroid.UpdateViewConfiguration
import bereal.app.commonandroid.thenIf
import bereal.app.design.Zoomable
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.StableImageLoader

@UnstableApi
@Composable
internal fun PrimaryView(
    modifier: Modifier = Modifier,
    primaryData: BeRealViewDataUiModel,
    secondaryData: BeRealViewDataUiModel?,
    state: BeRealViewUiState,
    isPrimaryImageTransparent: Boolean,
    shouldBlurImage: Boolean,
    allowHardware: Boolean,
    imageLoader: StableImageLoader,
    muteButtonPadding: PaddingValues,
    imageProperties: BeRealImageProperties,
    imagePlaceholder: String? = null,
    withLoading: Boolean,
    withPrimaryBorder: () -> Boolean,
) {
    val stateEvents = remember { state.eventsCollectors.primary }
    val allowUserInteractions by state.allowUserInteractions
    val borderColor = BeRealTheme.colors.onBackground

    val visibleLargeContent by remember { state.visibleLargeContent }

    Crossfade(targetState = primaryData, label = "") {
        UpdateViewConfiguration(
            doubleTapTimeoutMillis = 120L,
            longPressTimeoutMillis = 200L,
            touchSlop = 120f, // Default value, depending of the phone, is around 20.
        ) {
            Zoomable(
                onZoom = stateEvents::onPrimaryZooming,
                modifier = modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(imageProperties.primaryRadiusDp.dp))
                    .thenIf(withPrimaryBorder()) {
                        Modifier.border(
                            2.dp,
                            borderColor,
                            RoundedCornerShape(imageProperties.primaryRadiusDp.dp),
                        )
                    }
                    .pointerInput(allowUserInteractions) {
                        when (allowUserInteractions) {
                            AllowInteractions.Disabled -> {
                                /* no-op */
                            }

                            AllowInteractions.OnlyTap -> {
                                detectTapGestures(
                                    onTap = { stateEvents.onPrimaryClicked() },
                                )
                            }

                            AllowInteractions.AllInteractions -> {
                                detectTapGestures(
                                    onLongPress = {
                                        stateEvents.onLongPress(
                                            containerData = primaryData.containerData,
                                            visibleLargeContent = visibleLargeContent,
                                        )
                                    },
                                    onPress = {
                                        // Allow to detekt if the image is not pressed anymore.
                                        // So we can be sure user is not interacting anymore.
                                        tryAwaitRelease()
                                        stateEvents.onEndLongPressPrimary()
                                    },
                                    onTap = { stateEvents.onPrimaryClicked() },
                                    onDoubleTap = { stateEvents.onPrimaryDoubleClick() },
                                )
                            }
                        }
                    },
                onEvent = stateEvents::onUserInteractingWithPrimaryImage,
                isZoomable = !shouldBlurImage && allowUserInteractions == AllowInteractions.AllInteractions,
            ) {
                PrimaryContent(
                    primaryData = it,
                    secondaryData = secondaryData,
                    isPrimaryImageTransparent = isPrimaryImageTransparent,
                    shouldBlurImage = shouldBlurImage,
                    allowHardware = allowHardware,
                    imageLoader = imageLoader,
                    imageProperties = imageProperties,
                    imagePlaceholder = imagePlaceholder,
                    onLoaded = stateEvents::onPrimaryLoaded,
                    withLoading = withLoading,
                    state = state,
                    onEvent = stateEvents::onUserInteractingWithPrimaryImage,
                    muteButtonPadding = muteButtonPadding,
                )
            }
        }
    }
}
