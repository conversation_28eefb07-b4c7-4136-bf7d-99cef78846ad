package bereal.app.dualview.secondary.image

import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.design.berealimageview.model.BeRealViewConstants.BLUR_RADIUS
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.model.DualViewData
import bereal.app.image.core.BeRealImageSizes
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun DualViewSecondaryImage(
    modifier: Modifier = Modifier,
    imageLoader: StableImageLoader,
    media: DualViewData.Media.Image,
    onLoaded: () -> Unit,
    size: ImageSize,
    isBlurred: Boolean = false,
) {
    RemoteImage(
        data = media.image,
        modifier = modifier,
        imageLoader = imageLoader,
        contentDescription = null,
        contentScale = ContentScale.Crop,
        placeHolder = null,
        onLoaded = onLoaded,
        onError = {},
        options = ImageOptions(
            size = size,
            hardware = true,
            blurred = if (isBlurred) Blur(
                radius = BLUR_RADIUS,
                sampling = 10f,
            ) else null,
        ),
    )
}

@Composable
@Preview
private fun DualViewSecondaryImagePreview() {
    BeRealTheme {
        DualViewSecondaryImage(
            modifier = Modifier.aspectRatio(BeRealImageSizes.NewDesign.ASPECT_RATIO_WIDTH_ON_HEIGHT),
            media = DualViewData.primaryImagePreview(),
            imageLoader = LocalStableImageLoader.current,
            onLoaded = {},
            size = ImageSize.Original,
        )
    }
}

@Composable
@Preview
private fun DualViewSecondaryImagePreview_blurred() {
    BeRealTheme {
        DualViewSecondaryImage(
            modifier = Modifier.aspectRatio(BeRealImageSizes.NewDesign.ASPECT_RATIO_WIDTH_ON_HEIGHT),
            media = DualViewData.primaryImagePreview(),
            imageLoader = LocalStableImageLoader.current,
            isBlurred = true,
            onLoaded = {},
            size = ImageSize.Original,
        )
    }
}
