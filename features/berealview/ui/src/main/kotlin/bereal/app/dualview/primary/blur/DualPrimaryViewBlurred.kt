package bereal.app.dualview.primary.blur

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import bereal.app.design.image.model.options.ImageSize
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.primary.image.DualViewPrimaryImage
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun DualPrimaryViewBlurred(
    modifier: Modifier = Modifier,
    imageLoader: StableImageLoader,
    media: DualViewData.Media,
    imageSize: ImageSize,
) {
    when (media) {
        is DualViewData.Media.Image -> {
            DualViewPrimaryImage(
                modifier = modifier,
                media = media,
                isBlurred = true,
                imageLoader = imageLoader,
                imageSize = imageSize,
            )
        }

        is DualViewData.Media.Video -> {
            if (media.thumbnail != null) {
                DualViewPrimaryImage(
                    modifier = modifier,
                    imageLoader = imageLoader,
                    media = media.thumbnail,
                    isBlurred = true,
                    imageSize = imageSize,
                )
            }
        }
    }
}
