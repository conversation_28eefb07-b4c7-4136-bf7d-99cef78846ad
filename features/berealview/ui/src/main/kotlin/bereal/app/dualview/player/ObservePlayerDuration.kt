package bereal.app.dualview.player

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import bereal.app.bts.ui.model.VideoDurationUiModel
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import kotlin.time.Duration

@Composable
fun DualViewPlayer?.observeDurationWithDefault(default: Duration): Duration {
    if (this == null)
        return default
    return observeDuration() ?: default
}

@Composable
fun DualViewPlayer?.observeDuration(): Duration? {
    if (this == null)
        return null

    val duration = remember(this) { mutableStateOf<Duration?>(null) }
    LaunchedEffect(this) {
        <EMAIL> {
            duration.value = when (it) {
                is VideoDurationUiModel.Fetched -> it.duration
                is VideoDurationUiModel.Unknown -> null
            }
        }
    }

    return duration.value
}
