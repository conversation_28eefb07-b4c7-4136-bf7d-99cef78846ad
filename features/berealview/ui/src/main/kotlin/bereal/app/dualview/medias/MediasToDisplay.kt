package bereal.app.dualview.medias

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.singleplayer.DualViewPlayer

@Immutable
internal data class MediasToDisplay(
    val primary: MediaAndPlayer,
    val secondary: MediaAndPlayer,
)

@Immutable
internal data class MediaAndPlayer(
    val media: DualViewData.Media,
    val player: DualViewPlayer?,
)

@Composable
internal fun getMediasToDisplay(
    primary: DualViewData.Media,
    secondary: DualViewData.Media,
    players: DualViewPlayers,
    isMediaSwitched: () -> Boolean,
): MediasToDisplay {
    var switchToSecondaryCount by remember {
        mutableIntStateOf(0)
    }
    val isMediaSwitchedValue = isMediaSwitched()
    return remember(primary, secondary, players, isMediaSwitchedValue, switchToSecondaryCount) {
        val normalPrimary = MediaAndPlayer(
            media = primary,
            player = players.primary,
        )
        val normalSecondary = MediaAndPlayer(
            media = secondary,
            player = players.secondary,
        )
        if (isMediaSwitchedValue) {
            switchToSecondaryCount++
            MediasToDisplay(
                primary = normalSecondary,
                secondary = normalPrimary,
            )
        } else {
            MediasToDisplay(
                primary = normalPrimary,
                secondary = normalSecondary,
            ).also {
                if (switchToSecondaryCount > 0) {
                    // everytime we switched from secondary an go back to primary, if it's a bts, we play it again
                    if (players.playerType == DualViewPlayers.PlayerType.BTS) {
                        players.play()
                    }
                }
            }
        }
    }
}

@Composable
internal fun getSingleMediaToDisplay(
    primary: DualViewData.Media,
    players: DualViewPlayers,
): MediaAndPlayer {
    return remember(primary, players) {
        MediaAndPlayer(
            media = primary,
            player = players.primary,
        )
    }
}
