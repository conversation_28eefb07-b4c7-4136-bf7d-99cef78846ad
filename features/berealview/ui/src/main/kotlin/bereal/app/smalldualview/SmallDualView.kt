package bereal.app.smalldualview

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.commonandroid.thenIf
import bereal.app.commonandroid.toPx
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.isInPreview
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.model.DualViewConfig
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.DualViewData.Companion.primaryImagePreview
import bereal.app.dualview.primary.image.DualViewPrimaryImage
import bereal.app.dualview.secondary.image.DualViewSecondaryImage
import bereal.app.image.core.BeRealImageSizes
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.smalldualview.model.SmallDualViewData
import bereal.app.smalldualview.model.previewSmallDualViewData

val smallDualViewConfig = DualViewConfig(
    secondaryCornerRadius = (6.5).dp,
    secondaryBorderWidth = (0.5).dp,
    secondaryPadding = (8).dp,
    secondaryElevation = (10).dp,
    secondaryWidthPercent = 0.28125f,
    secondaryBorderColor = Color.White.copy(alpha = 0.2f),
)

val blackBorderDualViewConfig = DualViewConfig(
    secondaryCornerRadius = (6.5).dp,
    secondaryBorderWidth = 1.dp,
    secondaryPadding = (8).dp,
    secondaryElevation = 0.dp,
    secondaryWidthPercent = 0.30f,
    secondaryBorderColor = Color.Black,
)

@Composable
fun SmallDualView(
    data: SmallDualViewData,
    isBlurred: Boolean = false,
    imageLoader: StableImageLoader,
    modifier: Modifier = Modifier,
    dualViewConfig: DualViewConfig = smallDualViewConfig,
) {
    val secondaryShape = remember(dualViewConfig) {
        RoundedCornerShape(dualViewConfig.secondaryCornerRadius)
    }

    BoxWithConstraints(modifier = modifier) {
        val secondaryWidth = remember(maxWidth, dualViewConfig) {
            maxWidth * dualViewConfig.secondaryWidthPercent
        }

        if (data.type is DualViewData.Type.MediaFromGallery) {
            DualViewPrimaryImage(
                media = data.primary,
                modifier = Modifier.matchParentSize(),
                imageLoader = imageLoader,
                isBlurred = true,
                imageSize = ImageSize.Fixed(
                    width = maxWidth.toPx(),
                    height = maxHeight.toPx(),
                ),
            )
        }

        DualViewPrimaryImage(
            media = data.primary,
            modifier = Modifier.matchParentSize(),
            imageLoader = imageLoader,
            isBlurred = isBlurred,
            imageSize = ImageSize.Fixed(
                width = maxWidth.toPx(),
                height = maxHeight.toPx(),
            ), // allow to display downscaled image
            shouldFitImageIntoContainer = data.type is DualViewData.Type.MediaFromGallery,
        )

        if (!isBlurred && data.secondary != null && data.type !is DualViewData.Type.MediaFromGallery) { // don't display the secondary if blurred
            var isSecondaryLoaded by remember(data.secondary) {
                mutableStateOf(false)
            }
            val context = LocalContext.current
            val secondarySize = remember(secondaryWidth, context) {
                ImageSize.Fixed(
                    width = secondaryWidth.toPx(context),
                    height = (secondaryWidth * BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT).toPx(
                        context,
                    ),
                )
            }
            DualViewSecondaryImage(
                media = data.secondary,
                modifier = Modifier
                    .padding(dualViewConfig.secondaryPadding)
                    .width(secondaryWidth)
                    .aspectRatio(BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT)
                    .clip(secondaryShape)
                    .thenIf(isSecondaryLoaded || isInPreview) {
                        Modifier
                            .thenIf(dualViewConfig.secondaryElevation.value > 0f) {
                                Modifier.shadow(
                                    shape = secondaryShape,
                                    elevation = dualViewConfig.secondaryElevation,
                                )
                            }
                            .border(
                                width = dualViewConfig.secondaryBorderWidth,
                                color = dualViewConfig.secondaryBorderColor,
                                shape = secondaryShape,
                            )
                    },
                imageLoader = imageLoader,
                size = secondarySize,
                onLoaded = {
                    isSecondaryLoaded = true
                },
            )
        }
    }
}

@Composable
@Preview
private fun SmallDualViewPreview() {
    BeRealTheme {
        SmallDualView(
            data = previewSmallDualViewData(),
            imageLoader = LocalStableImageLoader.current,
            modifier = Modifier
                .width(128.dp)
                .aspectRatio(BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT),
        )
    }
}

@Composable
@Preview
private fun SmallDualView_blackBorder_Preview() {
    BeRealTheme {
        SmallDualView(
            data = previewSmallDualViewData(),
            imageLoader = LocalStableImageLoader.current,
            dualViewConfig = blackBorderDualViewConfig,
            modifier = Modifier
                .width(128.dp)
                .aspectRatio(BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT),
        )
    }
}

@Composable
@Preview
private fun SmallDualView_blurred_Preview() {
    BeRealTheme {
        SmallDualView(
            data = previewSmallDualViewData(),
            imageLoader = LocalStableImageLoader.current,
            isBlurred = true,
            modifier = Modifier
                .width(128.dp)
                .aspectRatio(BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT),
        )
    }
}

@Composable
@Preview
private fun SmallDualView_mediaFromGallery_Preview() {
    BeRealTheme {
        SmallDualView(
            data = SmallDualViewData(
                primary = primaryImagePreview(),
                secondary = null,
                type = DualViewData.Type.MediaFromGallery,
            ),
            imageLoader = LocalStableImageLoader.current,
            isBlurred = false,
            modifier = Modifier
                .width(128.dp)
                .aspectRatio(BeRealImageSizes.BEREAL_RATIO_WIDTH_ON_HEIGHT),
        )
    }
}
