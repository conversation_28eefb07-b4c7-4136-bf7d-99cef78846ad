package bereal.app.berealview.state

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.State
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.berealview.state.primary.PrimaryEventsCollector
import bereal.app.berealview.state.primary.PrimaryState
import bereal.app.berealview.state.secondary.SecondaryEventsCollector
import bereal.app.berealview.state.secondary.SecondaryState
import bereal.app.bts.ui.model.BtsState
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent

@Immutable
interface EventsCollectors {
    val primary: PrimaryEventsCollector
    val secondary: SecondaryEventsCollector

    fun onImagePropertiesChanged(imageProperties: BeRealImageProperties, aspectRatio: Float)
}

@Immutable
interface BeRealViewUiState {
    val allowUserInteractions: State<AllowInteractions>
    val visibleLargeContent: State<VisibleBerealViewLargeContent>

    val primary: PrimaryState
    val secondary: SecondaryState
    val btsState: BtsState?

    val eventsCollectors: EventsCollectors
}
