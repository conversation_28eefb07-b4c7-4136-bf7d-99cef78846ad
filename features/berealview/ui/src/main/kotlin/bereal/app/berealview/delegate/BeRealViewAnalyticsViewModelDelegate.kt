package bereal.app.berealview.delegate

import bereal.app.analytics.AnalyticsManager
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent
import org.koin.core.annotation.Factory

interface BeRealViewAnalyticsViewModelDelegate {
    fun onPrimaryLongPress(
        containerData: BeRealViewContainerDataUiModel,
        visibleLargeContent: VisibleBerealViewLargeContent,
    )

    fun onContentSwapped(
        containerData: BeRealViewContainerDataUiModel,
        visibleLargeContent: VisibleBerealViewLargeContent,
    )
}

/**
 * This class should definitely not be part of design module
 * This is due to fact that [BeRealViewContainerDataUiModel] is tight to the BeRealView
 * and that BeRealView is in design
 * A refactor to extract teh BeRealView to it is own module should be done !
 */
@Factory
class BeRealViewAnalyticsViewModelDelegateImpl(
    val analyticsManager: AnalyticsManager,
) : BeRealViewAnalyticsViewModelDelegate {
    override fun onPrimaryLongPress(
        containerData: BeRealViewContainerDataUiModel,
        visibleLargeContent: VisibleBerealViewLargeContent,
    ) {
        when (containerData) {
            is BeRealViewContainerDataUiModel.PostContainer -> {
                val event = BeRealViewEventAnalyticsDomainModel.PrimaryLongPress(
                    momentId = containerData.momentId,
                    userId = containerData.userId,
                    postId = containerData.postId,
                    isMain = containerData.isMain,
                    isLate = containerData.isLate,
                    isMediaTypeVideo = containerData.isMediaTypeVideo,
                    mainContent = visibleLargeContent,
                    view = containerData.view,
                    hasBts = containerData.hasBtsContent,
                )
                analyticsManager.logEvent(event)
            }

            else -> {
                // no op
            }
        }
    }

    override fun onContentSwapped(
        containerData: BeRealViewContainerDataUiModel,
        visibleLargeContent: VisibleBerealViewLargeContent,
    ) {
        when (containerData) {
            is BeRealViewContainerDataUiModel.PostContainer -> {
                val event = BeRealViewEventAnalyticsDomainModel.SecondaryClick(
                    momentId = containerData.momentId,
                    userId = containerData.userId,
                    postId = containerData.postId,
                    isMain = containerData.isMain,
                    isLate = containerData.isLate,
                    isMediaTypeVideo = containerData.isMediaTypeVideo,
                    mainContent = visibleLargeContent,
                    view = containerData.view,
                    hasBts = containerData.hasBtsContent,
                )
                analyticsManager.logEvent(event)
            }

            else -> {
                // no op
            }
        }
    }
}
