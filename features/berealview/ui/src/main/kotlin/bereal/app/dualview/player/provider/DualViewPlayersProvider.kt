package bereal.app.dualview.player.provider

import androidx.compose.runtime.Immutable
import androidx.compose.runtime.staticCompositionLocalOf
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.DualViewPlayers

val LocalDualViewPlayersProvider = staticCompositionLocalOf<DualViewPlayersProvider> {
    DualViewPlayersProviderNoOp()
}

/**
 * Gives the good player to the dual view
 * Depending on the implementation
 * - it can add another source that stores player (eg: in stories)
 * - it can just create 1 new player each time we call `providePlayer` (it used DualViewPlayerFactory)
 */
@Immutable
interface DualViewPlayersProvider {
    val isBoundToComposeLifecycle: Boolean
    fun providePlayer(data: DualViewData): DualViewPlayers
}
