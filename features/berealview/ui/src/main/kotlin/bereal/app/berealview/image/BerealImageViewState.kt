package bereal.app.berealview.image

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import bereal.app.berealview.BeRealViewDesign
import bereal.app.berealview.model.BeRealViewSecondaryAnchor
import bereal.app.berealview.state.BeRealViewUiState
import bereal.app.berealview.state.EventsCollectors
import bereal.app.berealview.state.enteranim.SecondaryAnimations
import bereal.app.berealview.state.primary.PrimaryEventsCollector
import bereal.app.berealview.state.primary.PrimaryEventsCollectorImpl
import bereal.app.berealview.state.primary.PrimaryStateImpl
import bereal.app.berealview.state.secondary.SecondaryEventsCollector
import bereal.app.berealview.state.secondary.SecondaryEventsCollectorImpl
import bereal.app.berealview.state.secondary.SecondaryStateImpl
import bereal.app.bts.ui.LocalBTSVideoPlayer
import bereal.app.bts.ui.model.BtsState
import bereal.app.bts.ui.player.BTSVideoPlayer
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import bereal.app.design.berealimageview.model.LongPressInteractionStateEventUiModel
import bereal.app.design.berealimageview.model.SecondaryAnimationType
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent
import bereal.app.haptic.BeRealHaptics
import bereal.app.haptic.LocalBeRealHaptics
import bereal.app.haptic.model.HapticType
import bereal.app.video.ui.LocalVideoPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.player.BeRealVideoPlayer
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.sample
import kotlinx.coroutines.launch

@OptIn(FlowPreview::class)
@Composable
fun rememberBeRealViewState(
    canChangeSecondaryAnchor: Boolean = true,
    secondaryPhotoAnimation: SecondaryAnimationType = SecondaryAnimationType.Fade,
    allowUserInteractions: AllowInteractions = AllowInteractions.AllInteractions,
    onTap: () -> Unit = {},
    onSecondaryClicked: (BeRealViewContainerDataUiModel, VisibleBerealViewLargeContent) -> Unit = { _, _ -> },
    onSwapImages: () -> Unit = { },
    onSecondaryDragging: (dragging: Boolean) -> Unit = { },
    onSecondaryAnchorChanged: (anchor: BeRealViewSecondaryAnchor) -> Unit = { },
    onDoubleTap: () -> Unit = {},
    onFocusCaptionTextField: () -> Unit = {},
    onPrimaryImageLongPress: (LongPressInteractionStateEventUiModel) -> Unit = {},
    postId: String? = null,
    uploadStateKey: String? = null,
    tapDebounceMs: Long? = null,
    onZoom: () -> Unit = {},
    hideSecondaryOnInteraction: Boolean = true,
    btsContainerId: String? = null,
    isBtsPlayable: Boolean = false,
    isBtsLocked: Boolean = false,
    design: BeRealViewDesign = BeRealViewDesign.V1,
    isCaptionTextEnabled: Boolean = false,
): BeRealViewUiState {
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val btsPlayer = btsContainerId?.let {
        LocalBTSVideoPlayer.current
    }

    // TODO VIDEO: Make it null when no video (Also this shouldnot be need if the player is outside the UI, i.e in the viewmodel (or even data layer)
    val beRealVideoPlayer: BeRealVideoPlayer = LocalVideoPlayer.current.stableValue

    val beRealHaptics: BeRealHaptics = LocalBeRealHaptics.current

    val state = remember {
        BeRealViewUiStateImpl(
            scope = scope,
            density = density,
            initialCanChangeSecondaryAnchor = canChangeSecondaryAnchor,
            initialAllowUserInteractions = allowUserInteractions,
            secondaryPhotoEnterAnimation = secondaryPhotoAnimation,
            beRealHaptics = beRealHaptics,
            onSecondaryClicked = onSecondaryClicked,
            hideSecondaryOnInteraction = hideSecondaryOnInteraction,
            btsContainerId = btsContainerId,
            isBtsPlayable = isBtsPlayable,
            isBtsLocked = isBtsLocked,
            btsPlayer = btsPlayer,
            beRealVideoPlayer = beRealVideoPlayer,
            postId = postId,
            design = design,
            onSwapImages = onSwapImages,
            onFocusCaptionTextField = onFocusCaptionTextField,
            isCaptionTextEnabled = isCaptionTextEnabled,
        )
    }

    LaunchedEffect(isBtsPlayable) {
        state.updateBtsData(btsContainerId, isBtsPlayable, btsPlayer, isCaptionTextEnabled)
    }

    LaunchedEffect(allowUserInteractions, postId, uploadStateKey) {
        state.updateAllowUserInteractions(allowUserInteractions)
    }

    LaunchedEffect(state, postId, uploadStateKey, onTap) {
        state.primary.onClicked
            .sample(tapDebounceMs ?: 1L)
            .collect {
                onTap()
            }
    }

    LaunchedEffect(state, postId, uploadStateKey, onDoubleTap) {
        state.primary.onDoubleClick.collect {
            onDoubleTap()
        }
    }

    val longPressingState by state.primary.longPressingState
    val isZooming by state.primary.isZooming
    LaunchedEffect(longPressingState, postId, uploadStateKey, onPrimaryImageLongPress) {
        val stateWithZooming = if (isZooming) {
            LongPressInteractionStateEventUiModel.Released
        } else {
            longPressingState
        }
        onPrimaryImageLongPress(stateWithZooming)
    }

    LaunchedEffect(isZooming, onZoom) {
        if (isZooming) {
            onZoom()
        }
    }

    val onSecondaryDraggingCallback by rememberUpdatedState(newValue = onSecondaryDragging)
    val isDragging by state.secondary.isDragging
    LaunchedEffect(isDragging) {
        onSecondaryDraggingCallback(isDragging)
    }

    val anchor by remember {
        derivedStateOf {
            val offsetX = state.secondary.secondaryOffset.value.x
            val minAnchor = state.secondary.secondaryMinOffsetXAnchor
            if (offsetX > minAnchor) {
                BeRealViewSecondaryAnchor.End
            } else {
                BeRealViewSecondaryAnchor.Start
            }
        }
    }

    val onSecondaryAnchorChangedCallback by rememberUpdatedState(newValue = onSecondaryAnchorChanged)
    LaunchedEffect(anchor) {
        onSecondaryAnchorChangedCallback(anchor)
    }

    btsContainerId?.let {
        LaunchedEffect(longPressingState) {
            val play = when (longPressingState) {
                LongPressInteractionStateEventUiModel.Released -> false
                is LongPressInteractionStateEventUiModel.Started -> true
            }
            state.btsState?.triggerPlay(play)
        }
    }

    return state
}

@OptIn(ExperimentalCoroutinesApi::class)
@Immutable
private class BeRealViewUiStateImpl(
    private val scope: CoroutineScope,
    private val postId: String?,
    initialCanChangeSecondaryAnchor: Boolean,
    secondaryPhotoEnterAnimation: SecondaryAnimationType,
    density: Density,
    private val beRealHaptics: BeRealHaptics,
    initialAllowUserInteractions: AllowInteractions,
    private val onSecondaryClicked: (BeRealViewContainerDataUiModel, VisibleBerealViewLargeContent) -> Unit,
    private val onSwapImages: () -> Unit,
    private val onFocusCaptionTextField: () -> Unit,
    private val hideSecondaryOnInteraction: Boolean,
    btsContainerId: String?,
    isBtsPlayable: Boolean,
    isBtsLocked: Boolean,
    btsPlayer: BTSVideoPlayer?,
    private val beRealVideoPlayer: BeRealVideoPlayer?,
    private val design: BeRealViewDesign,
    isCaptionTextEnabled: Boolean,
) : BeRealViewUiState {

    override val allowUserInteractions = mutableStateOf(initialAllowUserInteractions)
    fun updateAllowUserInteractions(newValue: AllowInteractions) {
        allowUserInteractions.value = newValue
    }

    private val canChangeSecondaryAnchor = mutableStateOf(initialCanChangeSecondaryAnchor)

    override val visibleLargeContent = mutableStateOf(VisibleBerealViewLargeContent.Primary)

    //region primary
    override val primary = PrimaryStateImpl()
    val primaryEventsCollector = PrimaryEventsCollectorImpl(
        allowUserInteractions = allowUserInteractions,
        scope = scope,
        primary = primary,
    )

    init {
        scope.launch {
            primary.onClicked.collect {
                when (design) {
                    BeRealViewDesign.V1 -> {
                        if (_isCaptionTextEnabled) {
                            onFocusCaptionTextField()
                        }
                    }

                    BeRealViewDesign.V2 -> {
                        if (_isCaptionTextEnabled) {
                            onFocusCaptionTextField()
                        } else {
                            swapContents()
                            onSwapImages()
                        }

                        beRealHaptics.perform(HapticType.Click)
                    }
                }
            }
        }
    }

    //endregion

    //region secondary
    override val secondary = SecondaryStateImpl()
    private val secondaryEventCollector = SecondaryEventsCollectorImpl(
        scope = scope,
        allowUserInteractions = allowUserInteractions,
        canChangeSecondaryAnchor = canChangeSecondaryAnchor,
        secondary = secondary,
        density = density,
        onSwitchImages = { containerData ->
            scope.launch {
                val playerState = beRealVideoPlayer?.observePrimaryPlayerState(containerData.id)?.firstOrNull()
                if (playerState != BeRealVideoPlayerState.Stopped && playerState != BeRealVideoPlayerState.Paused) {
                    beRealVideoPlayer?.switchPlayingVideoContent(containerData.id)
                }
                swapContents()
                onSecondaryClicked(containerData, visibleLargeContent.value)
            }
            beRealHaptics.perform(HapticType.Click)
        },
    )

    private fun swapContents() {
        val currentVisibleLargeContent = visibleLargeContent.value
        visibleLargeContent.value = when (currentVisibleLargeContent) {
            VisibleBerealViewLargeContent.Primary -> VisibleBerealViewLargeContent.Secondary
            VisibleBerealViewLargeContent.Secondary -> VisibleBerealViewLargeContent.Primary
        }
    } //endregion

    private val secondaryAnimations = SecondaryAnimations(
        secondary = secondary,
        scope = scope,
        secondaryPhotoEnterInitialAnimation = secondaryPhotoEnterAnimation,
    )

    private var _btsContainerId: String? = btsContainerId
    private var _isBtsPlayable: Boolean = isBtsPlayable
    private var _btsPlayer: BTSVideoPlayer? = btsPlayer
    private var _isCaptionTextEnabled: Boolean = isCaptionTextEnabled

    fun updateBtsData(containerId: String?, isPlayable: Boolean, player: BTSVideoPlayer?, isCaptionTextEnabled: Boolean) {
        if (_btsContainerId == containerId && _isBtsPlayable == isPlayable && _btsPlayer == player) {
            return
        }
        _isCaptionTextEnabled = isCaptionTextEnabled
        _btsContainerId = containerId
        _isBtsPlayable = isPlayable
        _btsPlayer = player
        btsState?.isPlayable = _isBtsPlayable
        init()
    }

    override val btsState: BtsState? = if (btsContainerId != null && btsPlayer != null) {
        BtsState(
            containerId = btsContainerId,
            isPlayable = isBtsPlayable,
            player = btsPlayer,
            isLocked = isBtsLocked,
        )
    } else {
        null
    }

    init {
        init()
    }

    private fun init() {
        btsState?.let { bindBtsPrimaryVisibility(it) }
        if (hideSecondaryOnInteraction) {
            bindSecondaryVisibility(btsState)
        }
        bindBtsPlayerToSecondaryAnimation(btsState)
        scope.launch {
            if (btsState?.isPlayable == true) {
                secondaryAnimations.animateEnter()
            }
            secondaryAnimations.bindSecondaryAlpha()
        }
    }

    private var bindPrimaryVisibilityJob: Job? = null
    private fun bindBtsPrimaryVisibility(btsState: BtsState) {
        // primary should be transparent when there is BTS data and user is long pressing.
        bindPrimaryVisibilityJob?.cancel()
        bindPrimaryVisibilityJob = scope.launch {
            snapshotFlow { primary.longPressingState.value }.flatMapLatest { longPressState ->
                when (longPressState) {
                    LongPressInteractionStateEventUiModel.Released -> {
                        flowOf(Pair(false, longPressState))
                    }

                    is LongPressInteractionStateEventUiModel.Started -> {
                        val primaryShouldBeTransparent = if (btsState.isLocked || !btsState.isPlayable) {
                            flowOf(false)
                        } else {
                            btsState.playerState.map { playerState ->
                                playerState != BeRealVideoPlayerState.Stopped && playerState != BeRealVideoPlayerState.Loading
                            }
                        }
                        primaryShouldBeTransparent.map {
                            Pair(it, longPressState)
                        }
                    }
                }
            }
                .collectLatest { (primaryShouldBeTransparent, longPressState) ->
                    swapLargeToPrimaryIfRequired(
                        btsState = btsState,
                        primaryShouldBeTransparent = primaryShouldBeTransparent,
                        longPressState = longPressState,
                    )
                    primary.isPrimaryImageTransparent.value = primaryShouldBeTransparent
                }
        }
    }

    private fun swapLargeToPrimaryIfRequired(
        btsState: BtsState,
        primaryShouldBeTransparent: Boolean,
        longPressState: LongPressInteractionStateEventUiModel,
    ) {
        if (btsState.isPlayable && !primaryShouldBeTransparent && longPressState is LongPressInteractionStateEventUiModel.Released) {
            if (visibleLargeContent.value == VisibleBerealViewLargeContent.Secondary) {
                visibleLargeContent.value = VisibleBerealViewLargeContent.Primary
            }
        }
    }

    private var bindBtsPlayerToSecondaryAnimationJob: Job? = null
    private fun bindBtsPlayerToSecondaryAnimation(btsState: BtsState?) {
        if (btsState?.isPlayable == true) {
            bindBtsPlayerToSecondaryAnimationJob?.cancel()
            bindBtsPlayerToSecondaryAnimationJob = scope.launch {
                btsState.playerState
                    .collect { playerState ->
                        if (playerState is BeRealVideoPlayerState.NearStopped) {
                            secondaryAnimations.animateEnter(SecondaryAnimationType.EnterFromStart)
                        }
                    }
            }
        }
    }

    private var bindSecondaryVisibilityJob: Job? = null
    private fun bindSecondaryVisibility(btsState: BtsState?) {
        bindSecondaryVisibilityJob?.cancel()

        // !!! Warning, hell logic below !!!
        bindSecondaryVisibilityJob = scope.launch {
            snapshotFlow { primary.isUserInteracting.value }.flatMapLatest { isInteractingWithPrimary ->
                if (isInteractingWithPrimary && primary.longPressingState.value is LongPressInteractionStateEventUiModel.Started && btsState != null) {
                    // Long pressing with BTS
                    if (!btsState.isLocked && btsState.isPlayable) {
                        // BTS is not locked and it is playable, listen to the playerState
                        btsState.playerState.map { playerState ->
                            playerState == BeRealVideoPlayerState.Stopped ||
                                playerState == BeRealVideoPlayerState.NearStopped
                        }
                    } else {
                        // BTS is locked or not playable
                        flowOf(btsState.isLocked)
                    }
                } else {
                    beRealVideoPlayer?.observeSecondaryPlayerState(postId ?: "")?.map { secondaryPlayerState ->
                        (secondaryPlayerState != BeRealVideoPlayerState.Stopped && secondaryPlayerState != BeRealVideoPlayerState.Loading) ||
                            !isInteractingWithPrimary
                    }
                } ?: run {
                    flowOf(!isInteractingWithPrimary)
                }
            }
                .collectLatest { secondaryShouldBeVisible ->
                    secondary.isVisible.value = secondaryShouldBeVisible
                }
        }
    }

    //endregion
    inner class EventsCollectorsImpl(
        override val primary: PrimaryEventsCollector,
        override val secondary: SecondaryEventsCollector,
    ) : EventsCollectors {
        override fun onImagePropertiesChanged(
            imageProperties: BeRealImageProperties,
            aspectRatio: Float,
        ) {
            secondaryEventCollector.onImagePropertiesChanged(
                imageProperties = imageProperties,
                aspectRatio = aspectRatio,
            )
        }
    }

    override val eventsCollectors = EventsCollectorsImpl(
        primaryEventsCollector,
        secondaryEventCollector,
    )
}
