package bereal.app.berealview.state.primary

import androidx.compose.runtime.Immutable
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import bereal.app.design.berealimageview.model.VisibleBerealViewLargeContent

@Immutable
interface PrimaryEventsCollector {
    fun onPrimaryClicked()

    fun onPrimaryDoubleClick()
    fun onPrimaryZooming(isZooming: Boolean)
    fun onUserInteractingWithPrimaryImage(isInteracting: Boolean)

    fun onPrimaryLoaded()

    fun onStartPressPrimary()
    fun onEndLongPressPrimary()

    fun onLongPress(
        containerData: BeRealViewContainerDataUiModel,
        visibleLargeContent: VisibleBerealViewLargeContent,
    )
}
