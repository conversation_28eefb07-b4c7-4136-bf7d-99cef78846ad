package bereal.app.berealview.bts

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.berealview.primary.PrimaryLoading
import bereal.app.berealview.video.MuteButton
import bereal.app.berealview.video.MuteButtonState
import bereal.app.bts.ui.model.BtsSettingsUiModel
import bereal.app.bts.ui.player.BTSVideoPlayer
import bereal.app.design.AnimatedVisibilityFade
import bereal.app.design.berealimageview.model.VideoSettingsUiModel
import bereal.app.design.theme.BeRealTheme
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.VideoUiModelContainerDataUiModel
import kotlinx.coroutines.launch

/**
 * This component should be in the BTS module.
 * But for that to work we will need to move most of Video UI and state in the video/ui module (e.g Mute button, etc).
 * But for the later to work we will need to depends on design/core which it self depend on video/ui which create a cicrcular dependy.
 * design/core module depend on video/ui because BeReal component is in design/core
 *
 * TODO Alexandre D.:
 * - Extract BeRealView and state in it is own module with a dependency to design/core
 * - Extract Video UI and State in it own module with a dependency to design/core
 * - Extract BTSOverylay to its bts/ui  with a dependency to video/ui
 */

@Composable
fun BtsOverlay(
    modifier: Modifier = Modifier,
    contentMargin: Dp,
    player: BTSVideoPlayer,
    containerData: VideoUiModelContainerDataUiModel,
    provideIsVisible: () -> Boolean,
    videoSettings: VideoSettingsUiModel,
    btsSettingsUiModel: BtsSettingsUiModel,
    isSustainedLookBtsPost: Boolean,
) {
    val playerState by player.observePlayerState(containerData.id).collectAsState(BeRealVideoPlayerState.Stopped)
    val muteState by player.observePlayerMuteState().collectAsState(BeRealVideoPlayerMuteState.Unmuted)

    val coroutineScope = rememberCoroutineScope()
    val muteButtonState = remember(videoSettings, muteState) {
        if (videoSettings.shouldShowMuteButton) {
            MuteButtonState.Visible(
                isMuted = muteState == BeRealVideoPlayerMuteState.Muted,
                onClick = {
                    coroutineScope.launch {
                        player.toggleMute(containerData)
                    }
                },
            )
        } else {
            MuteButtonState.Hidden
        }
    }

    BtsOverlayContent(
        modifier = modifier,
        contentMargin = contentMargin,
        playerState = playerState,
        muteButtonState = muteButtonState,
        provideIsIconExpanded = { btsSettingsUiModel.isIconExpanded },
        provideIsIconLocked = { btsSettingsUiModel.isLocked },
        provideIsVisible = provideIsVisible,
        isIconAnimated = isSustainedLookBtsPost,
    )
}

@Composable
private fun BtsOverlayContent(
    modifier: Modifier = Modifier,
    contentMargin: Dp,
    provideIsIconExpanded: () -> Boolean,
    provideIsVisible: () -> Boolean,
    provideIsIconLocked: () -> Boolean,
    isIconAnimated: Boolean,
    playerState: BeRealVideoPlayerState,
    muteButtonState: MuteButtonState,
) {
    val backgroundColor = BeRealTheme.colors.background.copy(alpha = 0.5f)

    Box(
        modifier = modifier.background(Color.Transparent),
    ) {
        if (playerState == BeRealVideoPlayerState.Loading) {
            PrimaryLoading(
                modifier = Modifier
                    .fillMaxSize()
                    .background(backgroundColor),
            )
        }
        AnimatedVisibility(
            modifier = Modifier
                .fillMaxSize()
                .padding(contentMargin)
                .align(Alignment.TopEnd),
            visible = provideIsVisible(),
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.End,
            ) {
                BtsIcon(
                    provideIsExpanded = provideIsIconExpanded,
                    provideIsIconLocked = provideIsIconLocked,
                    animateIcon = isIconAnimated,
                )
                AnimatedVisibilityFade(target = muteButtonState as? MuteButtonState.Visible) {
                    MuteButton(
                        modifier = Modifier.padding(top = 12.dp),
                        state = it,
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun BtsOverlayContentPreview(
    @PreviewParameter(BooleanStateProvider::class) locked: Boolean,
) {
    BeRealTheme {
        BtsOverlayContent(
            contentMargin = 12.dp,
            provideIsIconExpanded = { true },
            provideIsVisible = { true },
            isIconAnimated = false,
            playerState = BeRealVideoPlayerState.Loading,
            muteButtonState = MuteButtonState.Visible(
                isMuted = false,
                onClick = {},
            ),
            provideIsIconLocked = { locked },
        )
    }
}

private class BooleanStateProvider : PreviewParameterProvider<Boolean> {
    override val values: Sequence<Boolean>
        get() = sequenceOf(
            true,
            false,
        )
}
