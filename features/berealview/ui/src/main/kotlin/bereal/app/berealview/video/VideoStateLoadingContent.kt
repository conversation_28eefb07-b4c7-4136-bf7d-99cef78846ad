package bereal.app.berealview.video

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import bereal.app.berealview.primary.PrimaryLoading
import bereal.app.design.theme.BeRealTheme

@Composable
internal fun BoxScope.LoadingState(
    muteButtonState: MuteButtonState,
    muteButtonPadding: PaddingValues,
) {
    if (muteButtonState is MuteButtonState.Visible) {
        MuteButton(
            modifier = Modifier.align(Alignment.TopEnd).padding(muteButtonPadding).padding(12.dp),
            state = muteButtonState,
        )
    }

    val backgroundColor = BeRealTheme.colors.background.copy(alpha = 0.5f)
    PrimaryLoading(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor),
    )
}
