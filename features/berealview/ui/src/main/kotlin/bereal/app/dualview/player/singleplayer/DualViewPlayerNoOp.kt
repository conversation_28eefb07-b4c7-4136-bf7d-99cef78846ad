package bereal.app.dualview.player.singleplayer

import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.VideoDurationUiModel
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import bereal.app.video.ui.model.toggle
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.update

@Immutable
data class DualViewPlayerNoOp(
    val isMutedValue: Boolean = false,
    val playerStateValue: BeRealVideoPlayerState = BeRealVideoPlayerState.Loading,
) : DualViewPlayer {
    override val playerMuteState = MutableStateFlow(BeRealVideoPlayerMuteState.from(isMutedValue))
    override val playerState = MutableStateFlow(playerStateValue)
    override fun toggleMuted() {
        playerMuteState.update { it.toggle() }
    }

    override val playerType: DualViewPlayers.PlayerType = DualViewPlayers.PlayerType.DualVideo

    override val longPressToPause: Boolean = false

    override val exoplayer: StablePlayer by lazy {
        StablePlayer(
            stable = TODO(),
        )
    }

    override val primaryPlayerDuration: Flow<VideoDurationUiModel> = flowOf(VideoDurationUiModel.Unknown)
}
