package bereal.app.dualview.layout.anim

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.VectorConverter
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.geometry.Offset
import bereal.app.dualview.layout.model.SecondaryDraggableConfig
import bereal.app.dualview.model.SecondaryAnchor

@Composable
internal fun AnimateResetSecondaryToAnchor(
    resetToBound: () -> SecondaryAnchor?,
    onFinished: () -> Unit,
    secondaryDragOffset: () -> Offset,
    updateSecondaryDragOffset: (Offset) -> Unit,
    config: SecondaryDraggableConfig,
) {
    val updateSecondaryDragOffsetCallback by rememberUpdatedState(newValue = updateSecondaryDragOffset)
    val onFinishedCallback by rememberUpdatedState(newValue = onFinished)

    val resetToBoundValue = resetToBound()
    if (resetToBoundValue != null) {
        val resetAnim = remember(resetToBoundValue) {
            Animatable(secondaryDragOffset(), Offset.VectorConverter)
        }
        LaunchedEffect(resetAnim, resetToBoundValue) {
            resetAnim.animateTo(
                when (resetToBoundValue) {
                    SecondaryAnchor.TopRight -> Offset(x = config.maxDragX, y = 0f)
                    SecondaryAnchor.TopLeft -> Offset.Zero
                },
            )
            onFinishedCallback()
        }
        val value = resetAnim.value
        LaunchedEffect(value) {
            updateSecondaryDragOffsetCallback(value)
        }
    }
}
