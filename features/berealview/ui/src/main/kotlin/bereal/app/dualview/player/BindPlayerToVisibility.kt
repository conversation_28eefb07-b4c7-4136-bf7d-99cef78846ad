package bereal.app.dualview.player

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import bereal.app.design.lifecycle.BindWithLifecycle
import bereal.app.design.lifecycle.lifecycleStateIsResumed
import bereal.app.dualview.player.noop.DualViewPlayersNoOp

@Composable
internal fun BindPlayerToVisibility(
    players: DualViewPlayers,
    isDataBlurred: Boolean,
    isCurrentlyVisible: State<Boolean>,
) {
    if (players is DualViewPlayersNoOp) {
        return
    }

    val isCurrentlyVisibleValue by isCurrentlyVisible
    var isResumed = lifecycleStateIsResumed()
    BindWithLifecycle(
        onResume = {
            isResumed = true
        },
        onPause = {
            isResumed = false
        },
    )
    LaunchedEffect(players, isCurrentlyVisibleValue, isResumed, isDataBlurred) {
        if (isCurrentlyVisibleValue && isResumed && !isDataBlurred) {
            players.play()
        } else {
            players.pause()
        }
    }
}
