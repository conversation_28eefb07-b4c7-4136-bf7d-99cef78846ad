package bereal.app.dualview.dualvideo.players

import android.annotation.SuppressLint
import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.player.SimpleBeRealVideoPlayer
import bereal.app.dualview.dualvideo.players.singleplayer.DualViewPlayerTwoVideosImpl
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.video
import bereal.app.dualview.model.videoAnalyticsParams
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.PlayerAction
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel

@SuppressLint("UnsafeOptInUsageError")
@Immutable
internal data class DualViewPlayersTwoVideos(
    private val player: SimpleBeRealVideoPlayer,
) : DualViewPlayers {

    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    override val playerType: DualViewPlayers.PlayerType = DualViewPlayers.PlayerType.DualVideo

    override val downloadState = player.downloadState

    override val primary: DualViewPlayer = DualViewPlayerTwoVideosImpl(
        simpleBeRealVideoPlayer = player,
        scope = scope,
        isPrimary = true,
    )

    override val secondary: DualViewPlayer = DualViewPlayerTwoVideosImpl(
        simpleBeRealVideoPlayer = player,
        scope = scope,
        isPrimary = false,
    )

    override val playerMuteState = primary.playerMuteState

    override fun toggleMuted() {
        player.toggleMute()
    }

    override fun play() {
        player.play()
    }

    override fun resume() {
        player.resume()
    }

    override fun pause() {
        player.pause()
    }

    override fun dispose() {
        player.release()
        scope.cancel()
    }

    override suspend fun preload(data: DualViewData) {
        val primaryVideo = data.primary.video()
        val secondaryVideo = data.secondary?.video()
        if (primaryVideo != null && secondaryVideo != null) {
            player.prepare(
                primary = primaryVideo,
                secondary = secondaryVideo,
                videoAnalyticsParams = data.videoAnalyticsParams(),
            )
        }
    }

    override fun onPlayerAction(action: PlayerAction) {
        when (action) {
            is PlayerAction.Play -> play()
            is PlayerAction.Pause -> pause()
        }
    }

    private var pausedOnLifecycle = false

    override fun onViewPaused() {
        pausedOnLifecycle = true
        pause()
    }

    override fun onViewResumed() {
        if (pausedOnLifecycle) {
            pausedOnLifecycle = false
            player.resume()
        }
    }
}
