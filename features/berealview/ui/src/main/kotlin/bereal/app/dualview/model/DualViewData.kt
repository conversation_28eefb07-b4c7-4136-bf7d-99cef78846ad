package bereal.app.dualview.model

import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.VideoAnalyticsParams
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.imageUrl
import bereal.app.video.ui.model.SimpleVideoUiModel
import bereal.app.video.ui.model.VideoSourceTypeUiModel

@Immutable
data class DualViewData(
    val primary: Media,
    val secondary: Media?,
    val type: Type,
) {

    @Immutable
    sealed interface Type {
        @Immutable
        data object Images : Type

        @Immutable
        data class DualVideo(val videoAnalyticsParams: VideoAnalyticsParams?) : Type

        @Immutable
        data object SingleVideo : Type

        @Immutable
        data class BTS(val videoAnalyticsParams: VideoAnalyticsParams?) : Type

        @Immutable
        data object MediaFromGallery : Type
    }

    companion object {

        fun previewImages() = DualViewData(
            primary = primaryImagePreview(),
            secondary = secondaryImagePreview(),
            type = Type.Images,
        )
        fun previewVideos() = DualViewData(
            primary = primaryVideoPreview(),
            secondary = Media.Video(video = SimpleVideoUiModel("", VideoSourceTypeUiModel.USER_VIDEO), thumbnail = secondaryImagePreview()),
            type = Type.DualVideo(
                videoAnalyticsParams = VideoAnalyticsParams.preview(),
            ),
        )
        fun previewBts() = DualViewData(
            primary = primaryVideoPreview(),
            secondary = secondaryImagePreview(),
            type = Type.BTS(
                videoAnalyticsParams = VideoAnalyticsParams.preview(),
            ),
        )
        fun previewMediasFromGallery() = DualViewData(
            primary = primaryImagePreview(),
            secondary = null,
            type = Type.MediaFromGallery,
        )

        fun primaryImagePreview(): Media.Image {
            return Media.Image(image = ImageDataModel.DrawableRes(bereal.app.design.R.drawable.ia_generated_city))
        }

        fun primaryVideoPreview(): Media.Video {
            return Media.Video(video = SimpleVideoUiModel("", VideoSourceTypeUiModel.USER_VIDEO), thumbnail = primaryImagePreview())
        }

        fun secondaryImagePreview(): Media.Image {
            return Media.Image(image = ImageDataModel.DrawableRes(bereal.app.design.R.drawable.ia_generated_selfie))
        }
    }

    @Immutable
    sealed interface Media {

        @Immutable
        data class Image(
            val image: ImageDataModel,
        ) : Media

        @Immutable
        data class Video(
            val video: SimpleVideoUiModel,
            val thumbnail: DualViewData.Media.Image?,
        ) : Media
    }
}

fun DualViewData.Media.imageUrl(): String? {
    return when (this) {
        is DualViewData.Media.Image -> this.image.imageUrl()
        is DualViewData.Media.Video -> this.thumbnail?.imageUrl()
    }
}

fun DualViewData.Media.video(): SimpleVideoUiModel? {
    return when (this) {
        is DualViewData.Media.Image -> null
        is DualViewData.Media.Video -> this.video
    }
}

fun DualViewData.videoAnalyticsParams(): VideoAnalyticsParams? {
    return when (val t = this.type) {
        is DualViewData.Type.BTS -> t.videoAnalyticsParams
        is DualViewData.Type.DualVideo -> t.videoAnalyticsParams
        is DualViewData.Type.SingleVideo -> null
        is DualViewData.Type.Images -> null
        is DualViewData.Type.MediaFromGallery -> null
    }
}

fun DualViewData.Type.shouldFitIntoContainer(): Boolean {
    return when (this) {
        is DualViewData.Type.Images -> false
        is DualViewData.Type.DualVideo -> false
        is DualViewData.Type.SingleVideo -> true
        is DualViewData.Type.BTS -> false
        is DualViewData.Type.MediaFromGallery -> true
    }
}
