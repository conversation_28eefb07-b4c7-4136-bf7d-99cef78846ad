package bereal.app.berealview.state.secondary

import androidx.compose.runtime.State
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import bereal.app.berealview.image.BeRealImageProperties
import bereal.app.commonandroid.toPx
import bereal.app.design.berealimageview.model.AllowInteractions
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class SecondaryEventsCollectorImpl(
    val scope: CoroutineScope,
    val allowUserInteractions: State<AllowInteractions>,
    val canChangeSecondaryAnchor: State<Boolean>,
    val secondary: SecondaryStateImpl,
    val density: Density,
    val onSwitchImages: (BeRealViewContainerDataUiModel) -> Unit,
) : SecondaryEventsCollector {
    override fun onStartSecondaryDrag() {
        secondary.updateIsDraggingSecondary(true)
    }

    override fun onEndSecondaryDrag() {
        secondary.updateIsDraggingSecondary(false)
        scope.launch {
            val offsetX =
                if (secondary.secondaryOffset.value.x > secondary.secondaryMinOffsetXAnchor && canChangeSecondaryAnchor.value) {
                    secondary.secondaryOffsetMax.value.x
                } else {
                    0f
                }
            secondary.secondaryOffset.animateTo(Offset(offsetX, 0f))
        }
    }

    override fun onDragSecondaryCancel() {
        secondary.updateIsDraggingSecondary(false)
        scope.launch {
            secondary.secondaryOffset.animateTo(Offset.Zero)
        }
    }

    override fun onDragSecondary(dragAmount: Offset) {
        scope.launch {
            val newValue = secondary.secondaryOffset.value + dragAmount

            val limited = newValue.copy(
                x = newValue.x.coerceIn(
                    minimumValue = 0f,
                    maximumValue = secondary.secondaryOffsetMax.value.x,
                ),
                y = newValue.y.coerceIn(
                    minimumValue = 0f,
                    maximumValue = secondary.secondaryOffsetMax.value.y,
                ),
            )

            secondary.secondaryOffset.snapTo(limited)
        }
    }

    override fun onSecondaryClicked(
        containerData: BeRealViewContainerDataUiModel,
    ) {
        if (allowUserInteractions.value != AllowInteractions.Disabled) {
            scope.launch {
                secondary.onSecondaryClicked.emit(System.currentTimeMillis())
            }
            onSwitchImages(containerData)
        }
    }

    fun onImagePropertiesChanged(imageProperties: BeRealImageProperties, aspectRatio: Float) {
        val secondaryOffsetXMax = imageProperties.dragOffsetMaxDp.dp.toPx(density)
        val secondaryOffsetYMax = secondaryOffsetXMax / aspectRatio
        val margin = imageProperties.secondaryMarginDp.dp.toPx(density) * 2

        secondary.secondaryOffsetMax.value = Offset(
            secondaryOffsetXMax - margin,
            secondaryOffsetYMax - margin,
        )
        secondary.secondaryMinOffsetXAnchor = imageProperties.secondaryWidthDp.dp.toPx(density)

        resetPosition()
    }

    private fun resetPosition() {
        scope.launch {
            secondary.secondaryOffset.snapTo(Offset.Zero)
        }
    }
}
