package bereal.app.berealview.image

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.berealview.bts.BtsIcon
import bereal.app.berealview.video.PlayButton
import bereal.app.berealview.video.PlayButtonState
import bereal.app.commonandroid.toPx
import bereal.app.design.berealimageview.model.BeRealViewConstants.BLUR_RADIUS
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.isMediaFromGallery
import bereal.app.design.berealimageview.model.toImageOrThumbnailUrl
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.CrossFade
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
fun SmallBerealImageView(
    modifier: Modifier = Modifier,
    primaryViewData: BeRealViewDataUiModel,
    secondaryViewData: BeRealViewDataUiModel?,
    secondaryPadding: PaddingValues = PaddingValues(4.dp),
    imageLoader: StableImageLoader,
    primaryRadius: Dp = 8.dp,
    secondaryRadius: Dp = 6.dp,
    areImagesBlurred: Boolean,
    provideHasBtsContent: () -> Boolean,
) {
    val primaryImageData = remember(primaryViewData) {
        ImageDataModel.Url(url = primaryViewData.toImageOrThumbnailUrl() ?: "")
    }

    val secondaryImageData = remember(secondaryViewData) {
        ImageDataModel.Url(secondaryViewData?.toImageOrThumbnailUrl() ?: "")
    }

    val shouldShowPlayButton = remember(primaryViewData) {
        when (primaryViewData) {
            is BeRealViewDataUiModel.Video -> true
            is BeRealViewDataUiModel.Image -> false
        }
    }

    Box(
        modifier = modifier
            .aspectRatio(primaryViewData.aspectRatio, matchHeightConstraintsFirst = false),
    ) {
        primaryImageData?.let {
            RemoteImage(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(primaryRadius)),
                imageLoader = imageLoader,
                contentScale = ContentScale.Crop,
                data = it,
                options = ImageOptions(
                    crossfade = CrossFade.Enabled(),
                    size = ImageSize.Original,
                    hardware = false,
                    blurred = if (areImagesBlurred) {
                        Blur(
                            radius = BLUR_RADIUS,
                            sampling = 10f,
                        )
                    } else null,
                ),
                contentDescription = null,
            )
        }
        if (secondaryViewData != null && secondaryImageData != null && !primaryViewData.containerData.isMediaFromGallery()) {
            RemoteImage(
                modifier = Modifier
                    .padding(secondaryPadding)
                    .fillMaxWidth(fraction = 30.0f / 96.0f)
                    .aspectRatio(secondaryViewData.aspectRatio, matchHeightConstraintsFirst = false)
                    .clip(RoundedCornerShape(secondaryRadius))
                    .border(
                        width = 1.dp,
                        color = BeRealTheme.colors.background,
                        shape = RoundedCornerShape(secondaryRadius),
                    ),
                imageLoader = imageLoader,
                data = secondaryImageData,
                contentScale = ContentScale.Crop,
                options = ImageOptions(
                    crossfade = CrossFade.Enabled(),
                    size = ImageSize.Original,
                    hardware = false,
                    blurred = if (areImagesBlurred) {
                        Blur(
                            radius = BLUR_RADIUS,
                            sampling = 10f,
                        )
                    } else null,
                ),
                contentDescription = null,
            )
        }
        if (shouldShowPlayButton) {
            PlayButton(
                modifier = Modifier.align(Alignment.Center),
                radius = 18.dp.toPx(),
                state = PlayButtonState.Visible.NotClickable,
            )
        }
        if (provideHasBtsContent()) {
            BtsIcon(
                modifier = Modifier
                    .padding(BeRealTheme.spacing.xs)
                    .align(Alignment.TopEnd),
                provideIsExpanded = { false },
                animateIcon = false,
                provideIsIconLocked = { false },
            )
        }
    }
}
