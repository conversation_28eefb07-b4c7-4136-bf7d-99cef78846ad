package bereal.app.dualview.player

import androidx.compose.runtime.Immutable
import bereal.app.bts.ui.model.DownloadState
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.singleplayer.DualViewPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import kotlinx.coroutines.flow.StateFlow

/**
 * this video player can be either : BTS or TwoVideos
 * it contains 1 or 2 player, and can perform actions on them
 */
@Immutable
interface DualViewPlayers {
    val primary: DualViewPlayer?
    val secondary: DualViewPlayer?

    val downloadState: StateFlow<DownloadState>

    val playerType: PlayerType

    val playerMuteState: StateFlow<BeRealVideoPlayerMuteState>
    fun toggleMuted()

    fun play()
    fun resume()
    fun pause()
    fun onPlayerAction(action: PlayerAction)
    suspend fun preload(data: DualViewData)
    fun dispose()

    fun onViewPaused()
    fun onViewResumed()

    @Immutable
    enum class PlayerType {
        DualVideo,
        SingleVideo,
        BTS,
    }
}
