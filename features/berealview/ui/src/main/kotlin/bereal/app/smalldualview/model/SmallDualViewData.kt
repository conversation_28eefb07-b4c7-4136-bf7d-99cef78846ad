package bereal.app.smalldualview.model

import androidx.compose.runtime.Immutable
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.model.DualViewData.Companion.primaryImagePreview
import bereal.app.dualview.model.DualViewData.Companion.secondaryImagePreview
import bereal.app.dualview.model.DualViewData.Type

@Immutable
class SmallDualViewData(
    val primary: DualViewData.Media.Image,
    val secondary: DualViewData.Media.Image?,
    val type: Type,
)

fun previewSmallDualViewData() = SmallDualViewData(
    primary = primaryImagePreview(),
    secondary = secondaryImagePreview(),
    type = Type.Images,
)
