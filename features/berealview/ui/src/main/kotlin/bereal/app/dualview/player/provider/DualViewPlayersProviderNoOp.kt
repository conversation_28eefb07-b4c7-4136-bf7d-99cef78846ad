package bereal.app.dualview.player.provider

import androidx.compose.runtime.Immutable
import bereal.app.dualview.model.DualViewData
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.dualview.player.noop.DualViewPlayersNoOp
import bereal.app.dualview.player.singleplayer.DualViewPlayerNoOp

@Immutable
class DualViewPlayersProviderNoOp : DualViewPlayersProvider {
    override val isBoundToComposeLifecycle: Boolean = true

    override fun providePlayer(data: DualViewData): DualViewPlayers {
        return when (data.type) {
            is DualViewData.Type.BTS -> DualViewPlayersNoOp(
                primary = DualViewPlayerNoOp(),
            )
            is DualViewData.Type.DualVideo -> DualViewPlayersNoOp(
                primary = DualViewPlayerNoOp(),
                secondary = DualViewPlayerNoOp(),
            )
            is DualViewData.Type.Images -> DualViewPlayersNoOp()
            is DualViewData.Type.MediaFromGallery,
            is DualViewData.Type.SingleVideo,
            -> DualViewPlayersNoOp(
                primary = DualViewPlayerNoOp(),
            )
        }
    }
}
