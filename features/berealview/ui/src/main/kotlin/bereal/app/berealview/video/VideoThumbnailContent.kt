package bereal.app.berealview.video

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import bereal.app.design.berealimageview.model.BeRealViewConstants.BLUR_RADIUS
import bereal.app.design.berealimageview.model.BeRealViewDataUiModel
import bereal.app.design.berealimageview.model.toImageData
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun VideoThumbnail(
    modifier: Modifier = Modifier,
    thumbnailData: BeRealViewDataUiModel?,
    shouldBlur: Boolean,
    imageLoader: StableImageLoader,
) {
    if (thumbnailData != null && thumbnailData is BeRealViewDataUiModel.Image) {
        RemoteImage(
            data = thumbnailData.toImageData(),
            contentDescription = null,
            imageLoader = imageLoader,
            contentScale = ContentScale.Crop,
            modifier = modifier
                .fillMaxSize(),
            options = ImageOptions(
                blurred = if (shouldBlur) Blur(
                    radius = BLUR_RADIUS,
                    sampling = 10f,
                ) else null,
                size = ImageSize.Optimized,
                hardware = true,
            ),
        )
    }
}
