package bereal.app.dualview.layout

import androidx.compose.foundation.layout.BoxWithConstraintsScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.Dp
import bereal.app.commonandroid.toPx
import bereal.app.dualview.layout.model.SecondaryDraggableConfig

@Composable
internal fun BoxWithConstraintsScope.secondaryDraggableConfig(
    secondaryWidth: Dp,
    secondaryHeight: Dp,
): SecondaryDraggableConfig {
    val draggableZoneWidth = maxWidth.toPx()
    val secondaryWidthPX = secondaryWidth.toPx()

    val draggableZoneHeight = maxHeight.toPx()
    val secondaryHeightPX = secondaryHeight.toPx()

    return remember(
        secondaryWidthPX,
        draggableZoneWidth,
        draggableZoneHeight,
        secondaryHeightPX,
    ) {
        SecondaryDraggableConfig(
            secondaryWidthPX = secondaryWidthPX,
            draggableZoneWidth = draggableZoneWidth,
            maxDragX = draggableZoneWidth - secondaryWidthPX,
            maxDragY = draggableZoneHeight - secondaryHeightPX,
        )
    }
}
