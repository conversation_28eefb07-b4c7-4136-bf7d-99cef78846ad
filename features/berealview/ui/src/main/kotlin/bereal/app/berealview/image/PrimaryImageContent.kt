package bereal.app.berealview.image

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import bereal.app.berealview.primary.PrimaryLoading
import bereal.app.commonandroid.thenIf
import bereal.app.design.berealimageview.model.BeRealViewConstants.BLUR_RADIUS
import bereal.app.design.image.SubcomposeRemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.CrossFade
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.image.core.imageloader.StableImageLoader

@Composable
internal fun PrimaryImageContent(
    imageData: ImageDataModel,
    cacheSize: ImageSize,
    shouldBlurImage: Boolean,
    isTransparent: Boolean,
    allowHardware: Boolean,
    imageLoader: StableImageLoader,
    imageProperties: BeRealImageProperties,
    imagePlaceholder: String? = null,
    onLoaded: (() -> Unit)? = null,
    withLoading: Boolean,
    maskColor: Color? = null,
    imageTransformation: ImageTransformation? = null,
    shouldFitIntoContainer: Boolean,
) {
    val alpha = remember(isTransparent) {
        if (isTransparent) {
            0f
        } else {
            1f
        }
    }

    SubcomposeRemoteImage(
        imageLoader = imageLoader,
        data = imageData,
        contentScale = if (shouldFitIntoContainer) ContentScale.Fit else ContentScale.Crop,
        options = ImageOptions(
            hardware = allowHardware,
            crossfade = if (imagePlaceholder == null) CrossFade.Enabled(200) else CrossFade.Disabled,
            size = cacheSize,
            maskColor = maskColor,
            blurred = if (shouldBlurImage) Blur(
                radius = BLUR_RADIUS,
                sampling = 10f,
            ) else null,
        ),
        onLoadedSuccess = onLoaded,
        modifier = Modifier.fillMaxSize()
            .alpha(alpha)
            .thenIf(imageTransformation != null) {
                Modifier
                    .rotate(imageTransformation?.rotationAngle ?: 0f)
                    .graphicsLayer {
                        rotationZ = imageTransformation?.flipAngle ?: 0f
                    }
            },
        contentDescription = null,
        loading = {
            when {
                imagePlaceholder != null -> {
                    DrawWithSamePainter(
                        data = ImageDataModel.Url(imagePlaceholder),
                    )
                }

                withLoading && !shouldBlurImage -> PrimaryLoading()
            }
        },
        success = {
            DrawWithSamePainter(
                // So we also have the rounded corner when we move the image with gestures
                modifier = Modifier
                    // .aspectRatio(aspectRatio)
                    .clip(RoundedCornerShape(imageProperties.primaryRadiusDp.dp)),
            )
        },
    )
}
