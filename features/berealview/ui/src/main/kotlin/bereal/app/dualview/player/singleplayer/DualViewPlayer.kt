@file:OptIn(UnstableApi::class)

package bereal.app.dualview.player.singleplayer

import androidx.annotation.OptIn
import androidx.compose.runtime.Immutable
import androidx.media3.common.util.UnstableApi
import bereal.app.bts.ui.model.VideoDurationUiModel
import bereal.app.dualview.player.DualViewPlayers
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.StablePlayer
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * Video player scoped to one and unique video (one instance of exo player)
 */
@Immutable
interface DualViewPlayer {
    fun toggleMuted()

    val playerType: DualViewPlayers.PlayerType

    val longPressToPause: Boolean

    val playerMuteState: StateFlow<BeRealVideoPlayerMuteState>

    val exoplayer: StablePlayer
    val playerState: StateFlow<BeRealVideoPlayerState>

    val primaryPlayerDuration: Flow<VideoDurationUiModel>
}
