plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.ui.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "bereal.app.features.camera.ui"
}

dependencies {
    implementation(projects.platform.common)
    implementation(projects.platform.data.post.post)
    implementation(projects.platform.entities)
    implementation(projects.platform.domain)
    implementation(projects.platform.image.coreUi)
    implementation(projects.platform.music.domain)
    implementation(projects.platform.music.core)
    implementation(projects.platform.music.ui)

    implementation(projects.features.myUser.domain)
    implementation(projects.features.mypost.domain)
    implementation(projects.features.post.domain)
    implementation(projects.features.moment.domain)

    implementation(projects.features.cameraOrganisee.domain)
    implementation(projects.features.berealview.ui)
    implementation(projects.features.video.domain)

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.settings.domain)
    implementation(projects.platform.navigation)
    implementation(projects.platform.time)
    implementation(projects.platform.store.filestore)

    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.camera.core)
    implementation(libs.androidx.camera.compose)
    implementation(libs.androidx.camera.lifecycle)
    implementation(libs.androidx.camera.camera2)
    implementation(libs.androidx.camera.video)
    implementation(libs.accompanist.permissions)
    implementation(libs.lottie)
    implementation(libs.guava)
    implementation(libs.kotlinx.serialization.json)

    remoteImage()
    implementation(libs.androidx.constraintLayout)
}
