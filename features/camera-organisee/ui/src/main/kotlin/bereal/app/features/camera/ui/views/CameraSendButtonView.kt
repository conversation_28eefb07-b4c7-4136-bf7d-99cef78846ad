package bereal.app.features.camera.ui.views

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraSendContentState

@Composable
fun CameraSendButtonView(
    modifier: Modifier = Modifier,
    sendButtonState: CameraSendContentState.SendButtonState,
    isCaptureCompleted: Boolean,
    contentPadding: PaddingValues,
    onClick: () -> Unit,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        AnimatedVisibility(
            visible = isCaptureCompleted,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            SingleTapSendButton(
                modifier = Modifier.fillMaxWidth(),
                isEnabled = sendButtonState.isEnabled,
                contentPadding = contentPadding,
                onSend = onClick,
                sendText = sendButtonState.sendText,
                remainingBeRealText = sendButtonState.remainingBeRealText,
                withIcon = sendButtonState.withSendIcon,
            )
        }
    }
}

@Preview(widthDp = 360, heightDp = 140)
@Composable
private fun CameraSendButtonViewPreview() {
    BeRealTheme {
        CameraSendButtonView(
            sendButtonState = CameraSendContentState.SendButtonState(
                isEnabled = true,
                sendText = "Send",
                remainingBeRealText = "1 BeReal",
                withSendIcon = true,
            ),
            isCaptureCompleted = true,
            contentPadding = PaddingValues(0.dp),
            onClick = {},
        )
    }
}
