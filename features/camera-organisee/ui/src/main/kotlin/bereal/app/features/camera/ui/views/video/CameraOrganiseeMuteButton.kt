package bereal.app.features.camera.ui.views.video

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.common.design.BeRealDrawables
import bereal.app.design.noRippleDebounceClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.player.MuteButtonState

@Composable
internal fun CameraOrganiseeMuteButton(
    modifier: Modifier = Modifier,
    onClicked: () -> Unit,
    state: MuteButtonState,
) {
    when (state) {
        is MuteButtonState.Hidden -> {
            return
        }

        is MuteButtonState.Visible -> {
            Box(
                modifier = modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .background(BeRealTheme.colors.background.copy(alpha = 0.4f))
                    .noRippleDebounceClickable { onClicked() },
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    modifier = Modifier.size(20.dp),
                    painter = painterResource(
                        id = if (state.isMuted) {
                            BeRealDrawables.speaker_muted
                        } else {
                            BeRealDrawables.speaker
                        },
                    ),
                    contentDescription = "",
                )
            }
        }
    }
}

@Preview
@Composable
private fun CameraOrganiseeMuteButtonPreview() {
    BeRealTheme {
        CameraOrganiseeMuteButton(
            state = MuteButtonState.Visible(isMuted = true),
            onClicked = {},
        )
    }
}
