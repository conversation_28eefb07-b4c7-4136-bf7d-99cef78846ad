package bereal.app.features.camera.ui.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.commonandroid.thenIf
import bereal.app.design.R
import bereal.app.design.common.CircleInnerShadow
import bereal.app.design.noRippleClickable
import bereal.app.design.theme.BeRealTheme

@Composable
fun CameraRollIconView(
    modifier: Modifier = Modifier,
    launchMediaGalleryPicker: () -> Unit,
    canClick: <PERSON><PERSON>an,
) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(BeRealTheme.colors.grayScale.gray700)
            .thenIf(canClick) {
                Modifier.noRippleClickable(launchMediaGalleryPicker)
            },
        contentAlignment = Alignment.Center,
    ) {
        CircleInnerShadow(modifier = Modifier.matchParentSize())

        Icon(
            modifier = Modifier
                .size(36.dp)
                .padding(8.dp),
            painter = painterResource(R.drawable.medias),
            contentDescription = null,
            tint = BeRealTheme.colors.onBackground,
        )
    }
}

@Preview
@Composable
private fun CameraRollIconViewPreview() {
    BeRealTheme {
        CameraRollIconView(
            launchMediaGalleryPicker = {},
            canClick = true,
        )
    }
}
