package bereal.app.features.camera.ui.views.bottomsheet

import androidx.compose.runtime.Composable
import bereal.app.design.sheet.BeRealBottomSheetRow
import bereal.app.features.camera.ui.models.CameraLocationOption
import kotlinx.collections.immutable.PersistentList

@Composable
internal fun LocationBottomSheetContent(
    locationOptions: PersistentList<CameraLocationOption>,
    selectedLocation: CameraLocationOption,
    onLocationSelected: (CameraLocationOption) -> Unit,
) {
    locationOptions.forEach { item ->
        BeRealBottomSheetRow(
            icon = item.icon,
            title = item.title,
            description = item.description,
            selected = item.type == selectedLocation.type,
            onClick = {
                onLocationSelected(item)
            },
        )
    }
}
