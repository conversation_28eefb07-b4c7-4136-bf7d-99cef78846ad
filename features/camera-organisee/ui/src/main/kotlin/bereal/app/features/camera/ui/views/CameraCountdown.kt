package bereal.app.features.camera.ui.views

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import bereal.app.design.theme.BeRealTheme

private const val TRANSITION_DURATION_IN_MS = 300

@Composable
internal fun CameraCountdown(
    count: Int,
    modifier: Modifier = Modifier,
) {
    var oldCount by remember {
        mutableIntStateOf(count)
    }
    SideEffect {
        oldCount = count
    }

    val target = if (count == -1) "" else count.toString() // Use empty string to also animate first and last numbers
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        AnimatedContent(
            targetState = target,
            transitionSpec = {
                (slideInVertically(tween(TRANSITION_DURATION_IN_MS)) { -it } + fadeIn(tween(TRANSITION_DURATION_IN_MS))) togetherWith
                    (slideOutVertically(tween(TRANSITION_DURATION_IN_MS)) { it } + fadeOut(tween(TRANSITION_DURATION_IN_MS))) using
                    SizeTransform(clip = false)
            },
            contentAlignment = Alignment.Center,
        ) { text ->
            Text(
                text = text,
                style = BeRealTheme.typography.largeTitle.black.copy(fontSize = 46.sp),
                color = BeRealTheme.colors.onBackground,
            )
        }
    }
}

@Composable
@Preview
private fun CameraCountdownPreview() {
    BeRealTheme {
        CameraCountdown(count = 2, modifier = Modifier.fillMaxSize())
    }
}
