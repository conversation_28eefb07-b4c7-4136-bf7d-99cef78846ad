package bereal.app.features.camera.ui.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.design.berealimageview.model.BeRealViewConstants
import bereal.app.design.berealimageview.model.BeRealViewConstants.PRIMARY_ROUNDED_CORNER
import bereal.app.design.theme.BeRealTheme

@Composable
fun CameraPlaceholderView(
    modifier: Modifier = Modifier,
    topPadding: Dp,
) {
    Box(
        modifier = modifier.padding(top = topPadding)
            .clip(RoundedCornerShape(PRIMARY_ROUNDED_CORNER))
            .background(BeRealTheme.colors.grayScale.gray300),
    )
}

@Preview
@Composable
private fun CameraPlaceholderViewPreview() {
    CameraPlaceholderView(
        modifier = Modifier
            .aspectRatio(BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT)
            .fillMaxWidth(),
        topPadding = 16.dp,
    )
}
