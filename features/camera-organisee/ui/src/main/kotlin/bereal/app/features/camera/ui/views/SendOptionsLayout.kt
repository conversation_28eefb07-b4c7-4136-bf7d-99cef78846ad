package bereal.app.features.camera.ui.views

import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.dp
import bereal.app.design.theme.BeRealTheme

@Composable
fun SendOptionsLayout(content: @Composable () -> Unit) {
    SubcomposeLayout(modifier = Modifier.height(36.dp)) { constraints ->
        val itemSpacing = BeRealTheme.spacing.s.toPx().toInt()
        val placeables = subcompose(slotId = 0, content = content).map {
            it.measure(Constraints.fixedHeight(constraints.maxHeight))
        }
        var audienceChip = placeables[0]
        var locationChip = placeables[1]
        val musicChip = placeables.getOrNull(2)
        val musicChipWidthWithSpacing = if (musicChip != null) musicChip.width + itemSpacing else 0
        var remainingSpace =
            constraints.maxWidth - (audienceChip.width + itemSpacing + locationChip.width + musicChipWidthWithSpacing)

        if (remainingSpace < 0) {
            // We don't have enough space, remeasure audience and location
            val measurables = subcompose(slotId = 1, content = content)
            val spaceForAudienceAndLocation =
                constraints.maxWidth - itemSpacing - musicChipWidthWithSpacing

            when {
                locationChip.width < spaceForAudienceAndLocation / 2 -> {
                    audienceChip = measurables[0].measure(
                        Constraints.fixed(
                            height = constraints.maxHeight,
                            width = spaceForAudienceAndLocation - locationChip.width,
                        ),
                    )
                }

                audienceChip.width < spaceForAudienceAndLocation / 2 -> {
                    locationChip = measurables[1].measure(
                        Constraints.fixed(
                            height = constraints.maxHeight,
                            width = spaceForAudienceAndLocation - audienceChip.width,
                        ),
                    )
                }

                else -> {
                    audienceChip = measurables[0].measure(
                        Constraints.fixed(
                            height = constraints.maxHeight,
                            width = spaceForAudienceAndLocation / 2,
                        ),
                    )
                    locationChip = measurables[1].measure(
                        Constraints.fixed(
                            height = constraints.maxHeight,
                            width = spaceForAudienceAndLocation / 2,
                        ),
                    )
                }
            }

            remainingSpace = 0
        }

        layout(constraints.maxWidth, constraints.maxHeight) {
            var currentX = remainingSpace / 2
            if (audienceChip.width > 0) {
                audienceChip.place(x = currentX, y = 0)
                currentX += audienceChip.width + itemSpacing
            }
            if (locationChip.width > 0) {
                locationChip.place(x = currentX, y = 0)
                currentX += locationChip.width + itemSpacing
            }
            musicChip?.place(x = currentX, y = 0)
        }
    }
}
