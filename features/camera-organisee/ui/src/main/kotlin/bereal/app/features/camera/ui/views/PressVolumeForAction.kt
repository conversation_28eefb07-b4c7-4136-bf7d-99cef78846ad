package bereal.app.features.camera.ui.views

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.input.key.type
import bereal.app.commonandroid.thenIf

@Composable
fun PressVolumeForAction(
    modifier: Modifier = Modifier,
    onVolumePressed: () -> Unit,
    onBackPressed: () -> Unit,
    isVolumePressEnabled: Boolean,
    content: @Composable BoxScope.() -> Unit,
) {
    // onKeyEvent only works with a non-empty view, focusable & focused (or children is focused)
    val focusRequesterToHandleKeyEvents = remember { FocusRequester() }

    Box(
        modifier = modifier
            .thenIf(isVolumePressEnabled) {
                Modifier.onKeyEvent { keyEvent ->
                    if (keyEvent.type == KeyEventType.KeyDown) {
                        if (keyEvent.key == Key.VolumeUp || keyEvent.key == Key.VolumeDown)
                            onVolumePressed()
                        if (keyEvent.key == Key.Back) {
                            onBackPressed()
                        }
                        return@onKeyEvent true
                    }
                    false
                }
            }
            .focusRequester(focusRequesterToHandleKeyEvents)
            .focusable(),
        content = content,
    )

    BackHandler(!isVolumePressEnabled) {
        onBackPressed()
    }

    LaunchedEffect(Unit) {
        try {
            focusRequesterToHandleKeyEvents.requestFocus()
        } catch (t: Throwable) {
            t.printStackTrace()
        }
    }
}
