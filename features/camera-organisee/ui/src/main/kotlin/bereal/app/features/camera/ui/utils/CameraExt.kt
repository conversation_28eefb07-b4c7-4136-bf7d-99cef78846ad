package bereal.app.features.camera.ui.utils

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.os.Build
import androidx.annotation.OptIn
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.CameraInfo
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.ImageProxy
import bereal.app.commonandroid.StableBitmap
import bereal.app.entities.BeRealMedia
import bereal.app.features.camera.ui.models.CameraImageBitmapUiModel
import bereal.app.features.camera.ui.models.CameraMode
import bereal.app.features.camera.ui.models.Facing
import bereal.app.features.camera.ui.models.MediaCaptured
import bereal.app.features.camera.ui.models.MediaGalleryInfoUiModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.asExecutor
import java.nio.ByteBuffer
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

data class TakePictureResult(
    val image: CameraImageBitmapUiModel,
    val debugInfos: Map<String, String>,
)

suspend fun ImageCapture.takePicture(
    cameraSelector: CameraSelector,
    mode: CameraMode = CameraMode.Picture3By4,
    dispatcher: CoroutineDispatcher,
    preTakePicture: () -> Unit,
    postTakePicture: () -> Unit,
): TakePictureResult =
    suspendCoroutine { const ->
        val threadExecutor = dispatcher.asExecutor()
        val captureCallback = object : ImageCapture.OnImageCapturedCallback() {
            override fun onCaptureStarted() {
                super.onCaptureStarted()
                preTakePicture()
            }

            override fun onCaptureSuccess(image: ImageProxy) {
                try {
                    image.use {
                        val buffer: ByteBuffer = image.planes[0].buffer
                        val bytes = ByteArray(buffer.remaining())
                        buffer.get(bytes)

                        val options = BitmapFactory.Options().apply {
                            inScaled = false
                        }
                        val bitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size, options)

                        val rotatedBitmap = bitmap.rotate(
                            angle = image.imageInfo.rotationDegrees.toFloat(),
                            flipHorizontally = cameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA,
                        )
                        val (scaledBitmap, debugInfos) = centerCropBitmap(
                            rotatedBitmap,
                            mode.width,
                            mode.height,
                        )

                        val uiModel = CameraImageBitmapUiModel(
                            imageBitmap = StableBitmap(scaledBitmap),
                            facing = cameraSelector.toFacing(),
                            height = scaledBitmap.height,
                            width = scaledBitmap.width,
                        )
                        postTakePicture()
                        const.resume(
                            TakePictureResult(
                                image = uiModel,
                                debugInfos = debugInfos,
                            ),
                        )
                    }
                } catch (exception: Exception) {
                    const.resumeWithException(exception)
                }
            }

            override fun onError(exception: ImageCaptureException) {
                const.resumeWithException(exception)
            }
        }
        takePicture(threadExecutor, captureCallback)
    }

private const val CROPPED = "cropped"
private const val CROP_TYPE = "cropType"
private const val SRC_ASPECT = "srcAspect"
private const val TARGET_ASPECT = "targetAspect"
private const val RESIZED = "resized"
private const val ERROR = "error"

private fun centerCropBitmap(
    source: Bitmap,
    targetWidth: Int,
    targetHeight: Int,
): Pair<Bitmap, Map<String, String>> {
    val srcWidth = source.width
    val srcHeight = source.height

    val debugInfos = mutableMapOf<String, String>()

    debugInfos.put("srcWidth", srcWidth.toString())
    debugInfos.put("targetWidth", targetWidth.toString())
    debugInfos.put("srcHeight", srcHeight.toString())
    debugInfos.put("targetHeight", targetHeight.toString())

    // Vérification des dimensions sources et cibles
    if (srcWidth == 0 || srcHeight == 0 || targetWidth == 0 || targetHeight == 0) {
        // Option : retourner la source ou une Bitmap vide
        debugInfos.put(SRC_ASPECT, "0")
        debugInfos.put(TARGET_ASPECT, "0")

        debugInfos.put(
            ERROR,
            "srcWidth == 0 || srcHeight == 0 || targetWidth == 0 || targetHeight == 0",
        )
        return source to debugInfos
    }

    val srcAspect = srcWidth.toFloat() / srcHeight.toFloat()
    val targetAspect = targetWidth.toFloat() / targetHeight.toFloat()

    debugInfos.put(SRC_ASPECT, srcAspect.toString())
    debugInfos.put(TARGET_ASPECT, targetAspect.toString())

    if (targetAspect == srcAspect) {
        debugInfos.put(CROPPED, "false")
        // Même ratio, juste resize si nécessaire
        return if (srcWidth != targetWidth || srcHeight != targetHeight) {
            debugInfos.put(RESIZED, "true")
            debugInfos.put(
                "resizeType",
                resizeType(
                    srcWidth = srcWidth,
                    targetWidth = targetWidth,
                    srcHeight = srcHeight,
                    targetHeight = targetHeight,
                ),
            )
            val scaledBitmap = Bitmap.createScaledBitmap(source, targetWidth, targetHeight, true)
            scaledBitmap to debugInfos
        } else {
            debugInfos.put(RESIZED, "false")
            source to debugInfos
        }
    }

    debugInfos.put(CROPPED, "true")

    var cropWidth = srcWidth
    var cropHeight = srcHeight

    if (srcAspect > targetAspect) {
        debugInfos.put(CROP_TYPE, "srcAspect > targetAspect")
        cropWidth = (srcHeight * targetAspect).toInt()
    } else {
        debugInfos.put(CROP_TYPE, "srcAspect <= targetAspect")
        cropHeight = (srcWidth / targetAspect).toInt()
    }

    debugInfos.put("cropWidth", cropWidth.toString())
    debugInfos.put("cropHeight", cropHeight.toString())

    val xOffset = (srcWidth - cropWidth) / 2
    val yOffset = (srcHeight - cropHeight) / 2

    val croppedBitmap = try {
        val cropped = Bitmap.createBitmap(source, xOffset, yOffset, cropWidth, cropHeight)
        Bitmap.createScaledBitmap(cropped, targetWidth, targetHeight, true)
    } catch (e: IllegalArgumentException) {
        // En cas de taille invalide
        debugInfos.put(ERROR, "crop issue : ${e.message}")
        source
    }

    return croppedBitmap to debugInfos
}

private fun resizeType(srcWidth: Int, targetWidth: Int, srcHeight: Int, targetHeight: Int): String {
    return if (srcWidth < targetWidth || srcHeight < targetHeight) {
        "upscale"
    } else {
        "downscale"
    }
}

fun CameraSelector.toFacing() = if (this == CameraSelector.DEFAULT_FRONT_CAMERA) {
    Facing.Front
} else {
    Facing.Back
}

fun Bitmap.rotate(angle: Float, flipHorizontally: Boolean): Bitmap {
    val matrix = Matrix()

    if (flipHorizontally) {
        // flip horizontally needs to be done first, no pixel interpolation
        matrix.postScale(-1f, 1f)
        matrix.postRotate(360 - angle)
    } else {
        matrix.postRotate(angle)
    }

    return Bitmap.createBitmap(
        this, 0, 0, width, height,
        matrix, true,
    )
}

fun MediaCaptured.toBeRealMedia(): BeRealMedia = when (this) {
    is MediaCaptured.Image -> BeRealMedia(
        uri = data.uri.toString(),
        height = data.image.height,
        width = data.image.width,
        mediaType = BeRealMedia.MediaType.IMAGE,
    )

    is MediaCaptured.Video -> BeRealMedia(
        uri = video.uri,
        height = video.height,
        width = video.width,
        mediaType = BeRealMedia.MediaType.VIDEO,
    )

    is MediaCaptured.MediaGallery -> {
        val width = when (val type = media.mediaState) {
            is MediaGalleryInfoUiModel.MediaState.Image -> type.image.width
            is MediaGalleryInfoUiModel.MediaState.Video -> type.video.width
        }

        val height = when (val type = media.mediaState) {
            is MediaGalleryInfoUiModel.MediaState.Image -> type.image.height
            is MediaGalleryInfoUiModel.MediaState.Video -> type.video.height
        }
        BeRealMedia(
            uri = media.uri,
            height = height,
            width = width,
            mediaType = when (media.mediaState) {
                is MediaGalleryInfoUiModel.MediaState.Image -> BeRealMedia.MediaType.IMAGE
                is MediaGalleryInfoUiModel.MediaState.Video -> BeRealMedia.MediaType.VIDEO
            },
        )
    }
}

@OptIn(ExperimentalCamera2Interop::class)
internal fun getCameraDebugsInfos(cameraManager: CameraManager, cameraInfo: CameraInfo) =
    runCatching {
        buildMap {
            val cameraId = Camera2CameraInfo.from(cameraInfo).cameraId

            // region basic infos

            put("has_flash_unit", cameraInfo.hasFlashUnit().toString())
            put(
                "lens_facing",
                when (cameraInfo.lensFacing) {
                    CameraSelector.LENS_FACING_BACK -> "back"
                    CameraSelector.LENS_FACING_FRONT -> "front"
                    else -> "unknown"
                },
            )
            put("intrinsic_zoom_ratio", cameraInfo.intrinsicZoomRatio.toString())
            put("camera_state", cameraInfo.cameraState.value?.toString().orEmpty())
            put("zoom_state", cameraInfo.zoomState.value?.toString().orEmpty())
            put("torch_state", cameraInfo.torchState.value?.toString().orEmpty())
            put("sensor_rotation_degrees", cameraInfo.sensorRotationDegrees.toString())

            //endregion

            // region characteristics
            runCatching { cameraManager.getCameraCharacteristics(cameraId) }
                .getOrNull()
                ?.let { characteristics ->
                    val af = characteristics
                        .get(CameraCharacteristics.CONTROL_AF_AVAILABLE_MODES)
                        ?: intArrayOf()

                    put(
                        "control_af_mode_off",
                        af.contains(CameraCharacteristics.CONTROL_AF_MODE_OFF).toString(),
                    )
                    put(
                        "control_af_mode_auto",
                        af.contains(CameraCharacteristics.CONTROL_AF_MODE_AUTO).toString(),
                    )
                    put(
                        "control_af_mode_macro",
                        af.contains(CameraCharacteristics.CONTROL_AF_MODE_MACRO).toString(),
                    )
                    put(
                        "control_af_mode_continuous_video",
                        af.contains(CameraCharacteristics.CONTROL_AF_MODE_CONTINUOUS_VIDEO)
                            .toString(),
                    )
                    put(
                        "control_af_mode_continuous_picture",
                        af.contains(CameraCharacteristics.CONTROL_AF_MODE_CONTINUOUS_PICTURE)
                            .toString(),
                    )
                    put(
                        "control_af_mode_edof",
                        af.contains(CameraCharacteristics.CONTROL_AF_MODE_EDOF).toString(),
                    )

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        characteristics.get(CameraCharacteristics.CONTROL_ZOOM_RATIO_RANGE)
                            ?.let { range ->
                                put("min_zoom", range.lower.toFloat().toString())
                                put("max_zoom", range.upper.toFloat().toString())
                            }
                    }
                    val flash =
                        characteristics.get(CameraCharacteristics.FLASH_INFO_AVAILABLE) ?: false
                    put("flash_available", flash.toString())

                    val focalLengths =
                        characteristics.get(CameraCharacteristics.LENS_INFO_AVAILABLE_FOCAL_LENGTHS)
                            ?.toList() ?: emptyList()
                    put("focal_lengths", focalLengths.joinToString(","))

                    val lensInfoMinFocusDistance =
                        characteristics.get(CameraCharacteristics.LENS_INFO_MINIMUM_FOCUS_DISTANCE)
                            ?: 0f
                    put("lens_info_minimum_focus_distance", lensInfoMinFocusDistance.toString())

                    // endregion
                }
        }
    }
        .getOrNull()
        ?: emptyMap()
