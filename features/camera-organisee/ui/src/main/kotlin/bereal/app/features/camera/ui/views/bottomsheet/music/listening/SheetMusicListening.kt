package bereal.app.features.camera.ui.views.bottomsheet.music.listening

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import bereal.app.design.image.Gradient
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.Blur
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.sheet.BeRealBottomSheetRow
import bereal.app.design.sheet.BeRealBottomSheetTopBar
import bereal.app.design.theme.BeRealTheme
import bereal.app.entities.PostMusicVisibility
import bereal.app.features.camera.ui.models.MusicUiState
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.music.model.MusicService
import bereal.app.music.model.bottomSheetLogo
import kotlinx.collections.immutable.persistentListOf
import org.koin.compose.koinInject
import bereal.app.translations.R.string as translations

@Preview
@Composable
internal fun SheetMusicListeningPreview_shared() {
    BeRealTheme {
        Column {
            SheetMusicListening(
                musicState = MusicUiState.Logged.Listening(
                    id = "1",
                    isrc = "1",
                    musicService = MusicService.Spotify,
                    track = "Atlas",
                    artist = "Bicep",
                    artwork = "",
                    postMusicVisibility = PostMusicVisibility.Shared,
                    previewUrl = "",
                    musicLink = "",
                ),
                onMusicVisibilitySelected = {},
                imageLoader = koinInject(),
            )
        }
    }
}

@Preview
@Composable
internal fun SheetMusicListeningPreview_private() {
    BeRealTheme {
        Column {
            SheetMusicListening(
                musicState = MusicUiState.Logged.Listening(
                    id = "1",
                    isrc = "1",
                    musicService = MusicService.Spotify,
                    track = "Atlas",
                    artist = "Bicep",
                    artwork = "",
                    postMusicVisibility = PostMusicVisibility.Private,
                    previewUrl = "",
                    musicLink = "",
                ),
                onMusicVisibilitySelected = {},
                imageLoader = koinInject(),
            )
        }
    }
}

@Preview
@Composable
internal fun SheetMusicListeningPreview_no_music() {
    BeRealTheme {
        Column {
            SheetMusicListening(
                musicState = MusicUiState.Logged.Listening(
                    id = "1",
                    isrc = "1",
                    musicService = MusicService.Spotify,
                    track = "Atlas",
                    artist = "Bicep",
                    artwork = "",
                    postMusicVisibility = PostMusicVisibility.NoMusic,
                    previewUrl = "",
                    musicLink = "",
                ),
                onMusicVisibilitySelected = {},
                imageLoader = koinInject(),
            )
        }
    }
}

@Composable
internal fun SheetMusicListening(
    musicState: MusicUiState.Logged.Listening,
    imageLoader: StableImageLoader,
    onMusicVisibilitySelected: (PostMusicVisibility) -> Unit,
) {
    Box(modifier = Modifier.fillMaxWidth()) {
        SheetMusicListeningBlurredBackground(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            artwork = musicState.artwork,
            imageLoader = imageLoader,
        )
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            BeRealBottomSheetTopBar(
                sheetTitle = stringResource(id = translations.music_attach_sheet_title),
            )
            Spacer(
                modifier = Modifier.height(
                    BeRealTheme.spacing.s,
                ),
            )

            Box(
                modifier = Modifier
                    .size(120.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .aspectRatio(1f),
                contentAlignment = Alignment.Center,
            ) {
                RemoteImage(
                    data = ImageDataModel.Url(musicState.artwork),
                    contentDescription = null,
                    imageLoader = imageLoader,
                    options = ImageOptions(
                        hardware = true,
                        size = ImageSize.Optimized,
                    ),
                    contentScale = ContentScale.Fit,
                    modifier = Modifier
                        .fillMaxSize()
                        .background(color = BeRealTheme.colors.grayScale.gray900),
                )
            }

            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = BeRealTheme.spacing.xs)
                    .padding(horizontal = BeRealTheme.spacing.xl),
                text = musicState.track,
                style = BeRealTheme.typography.body.semibold,
                color = BeRealTheme.colors.onBackground,
                textAlign = TextAlign.Center,
            )

            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = BeRealTheme.spacing.xl)
                    .padding(bottom = BeRealTheme.spacing.xm),
                text = musicState.artist,
                style = BeRealTheme.typography.footnote.default,
                color = BeRealTheme.colors.onBackground.copy(alpha = 0.5f),
                textAlign = TextAlign.Center,
            )

            val visibilities = remember {
                persistentListOf(
                    PostMusicVisibility.Shared,
                    PostMusicVisibility.Private,
                    PostMusicVisibility.NoMusic,
                )
            }

            visibilities.forEach { visibility ->
                val selected = visibility == musicState.postMusicVisibility
                BeRealBottomSheetRow(
                    iconSlot = { colors ->
                        when (visibility) {
                            PostMusicVisibility.Shared -> {
                                Image(
                                    modifier = Modifier
                                        .size(22.dp),
                                    painter = painterResource(id = bereal.app.design.R.drawable.my_friends),
                                    contentDescription = null,
                                    colorFilter = ColorFilter.tint(colors.mainContentColor),
                                )
                            }

                            PostMusicVisibility.Private -> {
                                Image(
                                    modifier = Modifier
                                        .size(22.dp),
                                    painter = painterResource(id = bereal.app.design.R.drawable.ic_private),
                                    contentDescription = null,
                                    colorFilter = ColorFilter.tint(colors.mainContentColor),
                                )
                            }

                            PostMusicVisibility.NoMusic -> {
                                Box(
                                    modifier = Modifier.size(22.dp),
                                    contentAlignment = Alignment.Center,
                                ) {
                                    Image(
                                        painterResource(id = bereal.app.design.R.drawable.music_off),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .size(17.dp),
                                        colorFilter = ColorFilter.tint(colors.mainContentColor),
                                    )
                                }
                            }
                        }
                    },
                    title = when (visibility) {
                        PostMusicVisibility.Shared -> stringResource(id = translations.music_attach_sheet_visibility_shared_title)
                        PostMusicVisibility.Private -> stringResource(id = translations.music_attach_sheet_visibility_private_title)
                        PostMusicVisibility.NoMusic -> stringResource(id = translations.music_attach_sheet_visibility_none_title)
                    },
                    description = when (visibility) {
                        PostMusicVisibility.Shared -> stringResource(id = translations.music_attach_sheet_visibility_shared_description)
                        PostMusicVisibility.Private -> stringResource(id = translations.music_attach_sheet_visibility_private_description)
                        PostMusicVisibility.NoMusic -> stringResource(id = translations.music_attach_sheet_visibility_none_description)
                    },
                    selected = selected,
                    onClick = {
                        onMusicVisibilitySelected(visibility)
                    },
                )
            }

            Row(
                modifier = Modifier
                    .padding(vertical = BeRealTheme.spacing.m),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    modifier = Modifier,
                    text = stringResource(id = translations.music_playing_on_sheet_footer),
                    style = BeRealTheme.typography.footnote.default,
                    color = BeRealTheme.colors.grayScale.gray300,
                    textAlign = TextAlign.Center,
                    fontSize = 11.sp,
                )
                Image(
                    modifier = Modifier
                        .padding(start = 6.dp)
                        .height(21.dp),
                    painter = painterResource(id = musicState.musicService.bottomSheetLogo()),
                    contentScale = ContentScale.FillHeight,
                    contentDescription = null,
                )
            }
        }
    }
}

@Composable
private fun SheetMusicListeningBlurredBackground(
    artwork: String,
    imageLoader: StableImageLoader,
    modifier: Modifier = Modifier,
) {
    val gradientColor = BeRealTheme.colors.grayScale.gray950
    val topGradient = remember {
        Gradient.smoothGradient(
            from = gradientColor,
            to = gradientColor.copy(0f),
            direction = Gradient.Direction.TopBottom,
        )
    }
    val bottomGradient = remember {
        // use a native gradient here, to respect the design
        Brush.verticalGradient(
            0f to gradientColor.copy(alpha = 0f),
            0.9f to Color.Black.copy(alpha = 0.9f),
            1f to Color.Black.copy(alpha = 1f),
        )
    }

    ConstraintLayout(modifier = modifier) {
        val (image, overlay1, overlay2, gradientTop, gradientBottom) = createRefs()
        RemoteImage(
            data = ImageDataModel.Url(artwork),
            contentDescription = null,
            imageLoader = imageLoader,
            modifier = Modifier
                .constrainAs(image) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    width = Dimension.fillToConstraints
                    height = Dimension.wrapContent
                }
                .aspectRatio(1f),
            contentScale = ContentScale.Fit,
            options = ImageOptions(
                size = ImageSize.Optimized,
                hardware = true,
                blurred = Blur(
                    radius = 25f,
                    sampling = 5f,
                ),
            ),
        )
        // overlay
        Box(
            modifier = Modifier
                .constrainAs(overlay1) {
                    start.linkTo(image.start)
                    end.linkTo(image.end)
                    top.linkTo(image.top)
                    bottom.linkTo(image.bottom)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
                .background(Color.Companion.Black.copy(alpha = 0.6f)),
        )

        Box(
            modifier = Modifier
                .constrainAs(overlay2) {
                    start.linkTo(image.start)
                    end.linkTo(image.end)
                    top.linkTo(image.top)
                    bottom.linkTo(image.bottom)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
                .background(Color.Companion.Black.copy(alpha = 0.42f)),
        )

        Box(
            modifier = Modifier
                .constrainAs(gradientTop) {
                    start.linkTo(image.start)
                    end.linkTo(image.end)
                    top.linkTo(parent.top)
                    width = Dimension.fillToConstraints
                    height = Dimension.value(120.dp)
                }
                .background(topGradient)
                .alpha(0f),
        )
        Box(
            modifier = Modifier
                .constrainAs(gradientBottom) {
                    start.linkTo(image.start)
                    end.linkTo(image.end)
                    top.linkTo(gradientTop.bottom)
                    bottom.linkTo(image.bottom)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
                .background(bottomGradient),
        )
    }
}
