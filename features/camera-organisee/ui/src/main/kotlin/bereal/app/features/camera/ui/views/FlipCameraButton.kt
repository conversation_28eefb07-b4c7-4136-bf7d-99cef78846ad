package bereal.app.features.camera.ui.views

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.design.noRippleClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.R
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieAnimatable
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun FlipCameraButton(
    modifier: Modifier = Modifier,
    visible: Boolean,
    isClickable: <PERSON><PERSON><PERSON>,
    debounceOnClick: () -> Unit,
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.lottie_camera_rotate))
    val lottieAnimatable = rememberLottieAnimatable()

    // set it if you want to trigger the animation
    var playTrigger by remember { mutableStateOf<Long?>(null) }

    LaunchedEffect(playTrigger) {
        if (playTrigger != null) {
            lottieAnimatable.snapTo(
                composition = composition,
                progress = 0f,
            )
            lottieAnimatable.animate(
                composition,
            )
        }
    }

    if (visible && isClickable) {
        LottieAnimation(
            composition = composition,
            progress = { lottieAnimatable.progress },
            modifier = modifier
                .size(36.dp)
                .noRippleClickable({
                    playTrigger = System.currentTimeMillis()
                    debounceOnClick()
                }),
        )
    }
}

@Composable
@Preview
private fun FlipCameraButtonPreview() {
    BeRealTheme {
        FlipCameraButton(
            visible = true,
            isClickable = true,
            debounceOnClick = {},
        )
    }
}
