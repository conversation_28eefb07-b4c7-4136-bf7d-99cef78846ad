package bereal.app.features.camera.ui.views

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import bereal.app.commonandroid.thenIf
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme

@Composable
fun SingleTapSendButton(
    isEnabled: Boolean,
    sendText: String,
    remainingBeRealText: String?,
    withIcon: Boolean,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues,
    onSend: () -> Unit,
) {
    val mutableInteractionSource = remember { MutableInteractionSource() }
    val isPressed by mutableInteractionSource.collectIsPressedAsState()

    val itemsColor by animateColorAsState(
        label = "ButtonColors",
        targetValue = if (isPressed || !isEnabled) {
            BeRealTheme.colors.grayScale.gray500
        } else {
            BeRealTheme.colors.onBackground
        },
        animationSpec = tween(800),
    )

    Box(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(16.dp))
                .thenIf(isEnabled) {
                    Modifier.debounceClickable(
                        interactionSource = mutableInteractionSource,
                        onClick = onSend,
                    )
                }
                .padding(contentPadding)
                .padding(horizontal = BeRealTheme.spacing.l),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
            ) {
                Text(
                    text = sendText,
                    style = BeRealTheme.typography.largeTitle.black.copy(
                        fontSize = 36.sp,
                    ),
                    color = itemsColor,
                    textAlign = TextAlign.Center,
                )

                if (withIcon) {
                    Spacer(Modifier.width(BeRealTheme.spacing.s))

                    Icon(
                        painterResource(bereal.app.design.R.drawable.arrow_send),
                        contentDescription = null,
                        tint = itemsColor,
                        modifier = Modifier.size(28.dp),
                    )
                }
            }

            if (remainingBeRealText != null) {
                Text(
                    text = remainingBeRealText,
                    style = BeRealTheme.typography.footnote.bold,
                    color = itemsColor,
                )
            }
        }
    }
}
