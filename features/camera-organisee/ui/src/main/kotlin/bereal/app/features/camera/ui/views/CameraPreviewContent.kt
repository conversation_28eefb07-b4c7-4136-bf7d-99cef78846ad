package bereal.app.features.camera.ui.views

import androidx.camera.compose.CameraXViewfinder
import androidx.camera.core.SurfaceRequest
import androidx.camera.viewfinder.compose.MutableCoordinateTransformer
import androidx.camera.viewfinder.core.ImplementationMode
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.rememberTransformableState
import androidx.compose.foundation.gestures.transformable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.isSpecified
import androidx.compose.ui.geometry.takeOrElse
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.round
import bereal.app.design.berealimageview.model.BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT
import bereal.app.design.berealimageview.model.BeRealViewConstants.PRIMARY_ROUNDED_CORNER
import bereal.app.design.noRippleDebounceClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraPreviewState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID

@Composable
internal fun CameraPreviewContent(
    ratio: Float,
    previewState: CameraPreviewState,
    onAction: (CameraOrganiseeAction) -> Unit,
    surfaceRequest: SurfaceRequest?,
    topPadding: Dp,
    bottomPadding: Dp,
    isCameraInitialised: Boolean,
) {
    val animatableScale = remember { Animatable(1f) }
    val scope = rememberCoroutineScope()

    val screenWidth = LocalConfiguration.current.screenWidthDp.dp

    val animatedHeight by animateDpAsState(
        targetValue = screenWidth / ratio,
        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing),
    )

    val state = rememberTransformableState { zoomChange, _, _ ->
        val newScale = (animatableScale.value * zoomChange).coerceIn(
            minimumValue = previewState.captureOptions.minZoom,
            maximumValue = previewState.captureOptions.maxZoom,
        )
        scope.launch {
            animatableScale.snapTo(newScale)
        }
    }

    var autofocusRequest by remember { mutableStateOf(UUID.randomUUID() to Offset.Unspecified) }

    val autofocusRequestId = autofocusRequest.first
    val showAutofocusIndicator = autofocusRequest.second.isSpecified
    val autofocusCoords = remember(autofocusRequestId) { autofocusRequest.second }

    if (showAutofocusIndicator) {
        LaunchedEffect(autofocusRequestId) {
            delay(1000)
            autofocusRequest = autofocusRequestId to Offset.Unspecified
        }
    }

    LaunchedEffect(animatableScale.value, onAction) {
        onAction(CameraOrganiseeAction.OnZoomChanged(animatableScale.value))
    }

    surfaceRequest?.let { request ->
        val coordinateTransformer = remember { MutableCoordinateTransformer() }
        Box(
            Modifier
                .fillMaxSize()
                .padding(top = topPadding),
        ) {
            Box(
                Modifier
                    .clip(RoundedCornerShape(PRIMARY_ROUNDED_CORNER))
                    .fillMaxWidth()
                    .height(animatedHeight), // Use animated height here,
            ) {
                CameraXViewfinder(
                    modifier = Modifier
                        .pointerInput(previewState.captureOptions.mandatoryOptions.facing) {
                            detectTapGestures(
                                onTap = {
                                    with(coordinateTransformer) {
                                        val offset = it.transform()
                                        onAction(
                                            CameraOrganiseeAction.TapToFocus(
                                                coordinateX = offset.x,
                                                coordinateY = offset.y,
                                            ),
                                        )
                                    }
                                    autofocusRequest = UUID.randomUUID() to it
                                },
                                onDoubleTap = { onAction(CameraOrganiseeAction.FlipCamera) },
                            )
                        }
                        .transformable(state),
                    implementationMode = ImplementationMode.EMBEDDED,
                    coordinateTransformer = coordinateTransformer,
                    surfaceRequest = request,
                )

                AnimatedVisibility(
                    visible = showAutofocusIndicator,
                    enter = fadeIn(),
                    exit = fadeOut(),
                    modifier = Modifier
                        .offset { autofocusCoords.takeOrElse { Offset.Zero }.round() }
                        .offset((-24).dp, (-24).dp),
                ) {
                    Spacer(
                        Modifier
                            .border(2.dp, BeRealTheme.colors.onBackground, CircleShape)
                            .size(48.dp),
                    )
                }
            }

            CameraLayout(
                modifier = Modifier.padding(bottom = bottomPadding),
                cameraView = {
                    Box(
                        Modifier
                            .fillMaxWidth()
                            .aspectRatio(CAMERA_ASPECT_RATIO_PORTRAIT)
                            .clip(RoundedCornerShape(PRIMARY_ROUNDED_CORNER)),
                    )
                },
                controlsView = {
                    if (previewState.showControls) {
                        CameraOrganiseeControls(
                            modifier = Modifier.fillMaxSize(),
                            previewState = previewState,
                            onAction = onAction,
                            isCameraInitialised = isCameraInitialised,
                        )
                    } else {
                        Box {}
                    }
                },
                zoomView = {
                    if (previewState.showControls) {
                        ZoomIndicator(
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .padding(bottom = BeRealTheme.spacing.s)
                                .noRippleDebounceClickable {
                                    val newScale = if (animatableScale.value == 1f) {
                                        previewState.captureOptions.minZoom
                                    } else {
                                        1f
                                    }
                                    scope.launch {
                                        animatableScale.animateTo(newScale)
                                    }
                                },
                            zoom = previewState.zoomText,
                        )
                    } else {
                        Box {}
                    }
                },
            )
        }
    }
}
