package bereal.app.features.camera.ui.views

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.common.design.BeRealDrawables
import bereal.app.design.debounceClick

@Composable
fun ShutterButton(
    visible: <PERSON><PERSON><PERSON>,
    modifier: Modifier = Modifier,
    isClickable: <PERSON>olean,
    shutterButtonSize: Dp = 80.dp,
    debounceOnClick: () -> Unit,
    onLongClick: (() -> Unit)? = null,
    onLongClickReleased: (() -> Unit)? = null,
) {
    ResponsiveButton(
        modifier = modifier,
        painter = painterResource(id = BeRealDrawables.ic_shutter_80),
        visible = visible,
        iconSize = shutterButtonSize,
        isClickable = isClickable,
        debounceOnClick = debounceClick(debounceOnClick),
        onLongClick = onLongClick,
        onLongClickReleased = onLongClickReleased,
    )
}
