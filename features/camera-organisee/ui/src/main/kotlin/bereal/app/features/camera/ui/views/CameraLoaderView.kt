package bereal.app.features.camera.ui.views

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.design.berealimageview.model.BeRealViewConstants
import bereal.app.design.berealimageview.model.BeRealViewConstants.PRIMARY_ROUNDED_CORNER
import bereal.app.design.circularloader.BeRealCircularLoader
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraLoaderState

@Composable
fun CameraLoaderView(
    state: CameraLoaderState,
    modifier: Modifier = Modifier,
    topPadding: Dp = 0.dp,
    ratio: Float = BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT,
) {
    CameraLoaderContent(
        visible = state.show,
        backgroundVisible = state.showBackground,
        captureInProgressMessage = state.captureInProgressMessage,
        topPadding = topPadding,
        ratio = ratio,
        modifier = modifier,
    )
}

@Composable
private fun CameraLoaderContent(
    visible: Boolean,
    backgroundVisible: Boolean,
    captureInProgressMessage: String?,
    topPadding: Dp,
    ratio: Float,
    modifier: Modifier = Modifier,
) {
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(BeRealTheme.animDuration.medium)),
        exit = fadeOut(animationSpec = tween()),
    ) {
        Column(
            modifier = modifier
                .padding(top = topPadding)
                .fillMaxWidth()
                .aspectRatio(ratio)
                .clip(RoundedCornerShape(PRIMARY_ROUNDED_CORNER)),
        ) {
            BackgroundLoader(backgroundVisible) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.fillMaxSize(),
                ) {
                    BeRealCircularLoader()

                    captureInProgressMessage?.let {
                        Text(
                            text = captureInProgressMessage,
                            modifier = Modifier
                                .padding(top = BeRealTheme.spacing.m)
                                .padding(horizontal = BeRealTheme.spacing.l),
                            color = BeRealTheme.colors.onBackground,
                            textAlign = TextAlign.Center,
                            style = BeRealTheme.typography.headline.default.copy(
                                shadow = Shadow(
                                    color = BeRealTheme.colors.background,
                                    offset = Offset(4f, 4f),
                                    blurRadius = 8f,
                                ),
                            ),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun BackgroundLoader(
    visible: Boolean,
    content: @Composable () -> Unit,
) {
    val alpha: Float by animateFloatAsState(
        targetValue = if (visible) 1f else 0f,
        animationSpec = tween(),
    )

    Surface(color = BeRealTheme.colors.background.copy(alpha), content = content)
}
