package bereal.app.features.camera.ui.views.bottomsheet.music

import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.runtime.Composable
import bereal.app.entities.PostMusicVisibility
import bereal.app.features.camera.ui.models.MusicUiState
import bereal.app.features.camera.ui.views.bottomsheet.music.listening.SheetMusicListening
import bereal.app.features.camera.ui.views.bottomsheet.music.notlistening.SheetMusicNotListening
import bereal.app.features.camera.ui.views.bottomsheet.music.notlogged.SheetMusicNotLogged
import bereal.app.image.core.imageloader.BeRealImageLoaders
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders
import bereal.app.music.model.MusicService

@Composable
fun ColumnScope.SheetMusic(
    musicState: MusicUiState,
    onMusicServiceRequireLogin: (MusicService) -> Unit,
    onMusicVisibilitySelected: (PostMusicVisibility) -> Unit,
) {
    val imageLoaders: BeRealImageLoaders = LocalBeRealImageLoaders.current
    when (musicState) {
        is MusicUiState.NotLogged -> SheetMusicNotLogged(musicState, onMusicServiceRequireLogin)
        is MusicUiState.Logged.Listening -> SheetMusicListening(
            musicState = musicState,
            imageLoader = imageLoaders.music,
            onMusicVisibilitySelected = onMusicVisibilitySelected,
        )
        is MusicUiState.Logged.NotListening -> {
            SheetMusicNotListening()
        }
        is MusicUiState.Loading -> {
            // nothing
        }
        is MusicUiState.Disabled -> {
            // nothing
        }
    }
}
