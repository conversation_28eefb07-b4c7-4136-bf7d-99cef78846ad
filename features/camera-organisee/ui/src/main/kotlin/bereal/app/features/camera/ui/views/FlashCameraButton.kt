package bereal.app.features.camera.ui.views

import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import bereal.app.design.noRippleClickable
import bereal.app.features.camera.ui.R
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieClipSpec
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieAnimatable
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun FlashCameraButton(
    flashEnabled: Boolean,
    visible: Boolean,
    isClickable: Boolean,
    modifier: Modifier = Modifier,
    debounceOnClick: () -> Unit,
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.lottie_flash_camera))
    var isPlaying by remember { mutableStateOf<Boolean?>(null) }
    val lottieAnimatable = rememberLottieAnimatable()
    var isFlashEnabled by remember {
        mutableStateOf(flashEnabled)
    }

    LaunchedEffect(isFlashEnabled) {
        if (isPlaying == null) {
            lottieAnimatable.snapTo(
                composition = composition,
                progress = if (isFlashEnabled) 0f else 0.5f,
            )
        } else {
            lottieAnimatable.animate(
                composition,
                clipSpec = if (isFlashEnabled) LottieClipSpec.Progress(0.5f, 1f) else LottieClipSpec.Progress(0f, 0.5f),
            )
        }
    }

    if (visible && isClickable) {
        LottieAnimation(
            composition = composition, progress = { lottieAnimatable.progress },
            modifier = modifier
                .size(36.dp)
                .noRippleClickable({
                    isPlaying = true
                    isFlashEnabled = !isFlashEnabled
                    debounceOnClick()
                }),
        )
    }
}
