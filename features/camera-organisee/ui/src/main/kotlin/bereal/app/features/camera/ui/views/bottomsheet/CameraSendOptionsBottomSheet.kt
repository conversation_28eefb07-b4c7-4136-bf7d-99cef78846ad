package bereal.app.features.camera.ui.views.bottomsheet

import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraOrganiseeSendOptionsState
import kotlinx.coroutines.launch

@Composable
internal fun CameraSendOptionsBottomSheet(
    sendOptionsState: CameraOrganiseeSendOptionsState,
    navigateUp: () -> Unit,
    onAction: (CameraOrganiseeAction) -> Unit,
    content: @Composable () -> Unit,
) {
    val scope = rememberCoroutineScope()

    val bottomSheetState =
        rememberModalBottomSheetState(
            initialValue = ModalBottomSheetValue.Hidden,
            confirmValueChange = {
                when (it) {
                    ModalBottomSheetValue.Hidden -> {
                        onAction(CameraOrganiseeAction.OnHideBottomSheet)
                    }

                    ModalBottomSheetValue.Expanded,
                    ModalBottomSheetValue.HalfExpanded,
                    -> {
                        /* no op, don't send any message to the viewmodel */
                    }
                }
                true // allow to hide or display
            },
            skipHalfExpanded = false,
        )

    LaunchedEffect(sendOptionsState.bottomSheetTypeSelected) {
        if (sendOptionsState.bottomSheetTypeSelected != null) {
            bottomSheetState.show()
        } else {
            bottomSheetState.hide()
        }
    }

    CameraSendOptionsBottomSheetContent(
        sendOptionsState = sendOptionsState,
        navigateUp = navigateUp,
        onAction = onAction,
        cameraBottomSheetType = sendOptionsState.bottomSheetTypeSelected,
        bottomSheetState = bottomSheetState,
        onHideBottomSheetRequested = {
            scope.launch {
                // called when we decide to hide automatically the bottom sheet (while scrolling down for example)
                // note : it will not fire the `confirmStateChange`, that's why we call `onPreviewAction` also here
                bottomSheetState.hide()
                onAction(CameraOrganiseeAction.OnHideBottomSheet)
            }
        },
    ) {
        content()
    }
}
