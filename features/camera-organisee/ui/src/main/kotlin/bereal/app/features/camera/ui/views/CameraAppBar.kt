package bereal.app.features.camera.ui.views

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.common.design.BeRealDrawables
import bereal.app.design.countdown.CountDown
import bereal.app.design.countdown.CountUp
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraPostInfoState

@Composable
fun CameraAppBar(
    cameraPostInfoState: CameraPostInfoState?,
    onCountDownFinished: () -> Unit,
    onCloseCamera: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .statusBarsPadding()
            .padding(vertical = BeRealTheme.spacing.s),
    ) {
        // only show logo when there is no counter avoid avoid a too big top bar
        if (cameraPostInfoState is CameraPostInfoState.AdditionalPosts || cameraPostInfoState is CameraPostInfoState.Late) {
            Image(
                painter = painterResource(id = BeRealDrawables.logo),
                contentDescription = null,
                modifier = Modifier.align(Alignment.Center),
            )
        }

        Box(
            modifier = Modifier
                .padding(horizontal = BeRealTheme.spacing.m)
                .clip(CircleShape)
                .align(Alignment.CenterStart)
                .debounceClickable { onCloseCamera() },
            contentAlignment = Alignment.Center,
        ) {
            Image(
                modifier = Modifier.size(28.dp),
                painter = painterResource(BeRealDrawables.chevron_down),
                contentDescription = null,
                colorFilter = ColorFilter.tint(color = BeRealTheme.colors.onBackground),
            )
        }

        when (cameraPostInfoState) {
            is CameraPostInfoState.InMoment -> {
                if (cameraPostInfoState.withIncrementalTimer) {
                    CountUp(
                        modifier = Modifier.align(Alignment.Center),
                        countFrom = cameraPostInfoState.startTime,
                    )
                } else {
                    CountDown(
                        countTo = cameraPostInfoState.startTime,
                        modifier = Modifier.align(Alignment.Center),
                        onCountDownFinished = onCountDownFinished,
                        withHapticFeedback = true,
                        withRedUrgency = true,
                    )
                }
            }

            else -> {
                Spacer(modifier = Modifier.size(BeRealTheme.spacing.m))
            }
        }
    }
}

@Preview
@Composable
private fun CameraAppBar_InMoment_Preview() {
    BeRealTheme {
        CameraAppBar(
            cameraPostInfoState = CameraPostInfoState.InMoment(
                startTime = 1000,
                withIncrementalTimer = false,
            ),
            onCountDownFinished = {},
            onCloseCamera = {},
        )
    }
}

@Preview
@Composable
private fun CameraAppBar_Late_Preview() {
    BeRealTheme {
        CameraAppBar(
            cameraPostInfoState = CameraPostInfoState.Late,
            onCountDownFinished = {},
            onCloseCamera = {},
        )
    }
}

@Preview
@Composable
private fun CameraAppBar_UnlimitedPosts_Preview() {
    BeRealTheme {
        CameraAppBar(
            cameraPostInfoState = CameraPostInfoState.AdditionalPosts.WithoutRemainingPostInfo,
            onCountDownFinished = {},
            onCloseCamera = {},
        )
    }
}
