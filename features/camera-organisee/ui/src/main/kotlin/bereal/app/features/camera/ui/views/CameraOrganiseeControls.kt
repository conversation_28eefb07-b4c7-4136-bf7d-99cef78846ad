package bereal.app.features.camera.ui.views

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraPreviewState

@Composable
fun CameraOrganiseeControls(
    modifier: Modifier = Modifier,
    previewState: CameraPreviewState,
    isCameraInitialised: Boolean,
    onAction: (CameraOrganiseeAction) -> Unit,
) {
    val shutterButtonSize by remember { mutableStateOf(80.dp) }

    PressVolumeForAction(
        modifier = modifier,
        onVolumePressed = { onAction(CameraOrganiseeAction.TakePicture) },
        onBackPressed = { onAction(CameraOrganiseeAction.OnBackPressed) },
        isVolumePressEnabled = true,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = BeRealTheme.spacing.m)
                .align(Alignment.Center),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceEvenly,
        ) {
            Box(modifier = Modifier.size(36.dp)) {
                FlashCameraButton(
                    flashEnabled = previewState.captureOptions.isFlashEnabled,
                    visible = previewState.showControls,
                    isClickable = true,
                    debounceOnClick = {
                        onAction(CameraOrganiseeAction.ToggleFlash)
                    },
                )
            }

            ShutterButton(
                visible = true,
                isClickable = isCameraInitialised,
                shutterButtonSize = shutterButtonSize,
                debounceOnClick = { onAction(CameraOrganiseeAction.TakePicture) },
            )

            Box(modifier = Modifier.size(36.dp)) {
                FlipCameraButton(
                    visible = previewState.showControls,
                    isClickable = true,
                    debounceOnClick = {
                        onAction(CameraOrganiseeAction.FlipCamera)
                    },
                )
            }
        }
    }
}
