package bereal.app.features.camera.ui

import android.content.Context
import android.content.Intent
import android.provider.Settings
import androidx.activity.compose.BackHandler
import androidx.annotation.OptIn
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.SurfaceRequest
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.design.AnimatedVisibilityFade
import bereal.app.design.BeRealScaffold
import bereal.app.design.berealimageview.model.BeRealViewConstants
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraOrganiseeEvent
import bereal.app.features.camera.ui.models.CameraOrganiseeUiState
import bereal.app.features.camera.ui.preview.generateCameraPreviewState
import bereal.app.features.camera.ui.views.CameraAppBar
import bereal.app.features.camera.ui.views.CameraLoaderView
import bereal.app.features.camera.ui.views.CameraPlaceholderView
import bereal.app.features.camera.ui.views.CameraPreviewContent
import bereal.app.features.camera.ui.views.SendContentView
import bereal.app.features.camera.ui.views.bottomsheet.CameraSendOptionsBottomSheet

@OptIn(ExperimentalCamera2Interop::class)
@Composable
fun CameraOrganiseeScreen(
    viewModel: CameraOrganiseeViewModel,
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val surfaceRequest by viewModel.surfaceRequest.collectAsStateWithLifecycle()
    val context = LocalContext.current

    BackHandler {
        viewModel.pop()
    }

    BeRealScaffold(
        modifier = Modifier.fillMaxSize(),
        topBar = {
            CameraAppBar(
                cameraPostInfoState = uiState.cameraHeaderState,
                onCountDownFinished = viewModel::onCountDownFinished,
                onCloseCamera = {
                    viewModel.pop(forceExit = true)
                },
            )
        },
    ) { contentPadding ->
        CameraOrganiseeContent(
            modifier = Modifier.fillMaxSize(),
            uiState = uiState,
            surfaceRequest = surfaceRequest,
            contentPadding = contentPadding,
            onAction = viewModel::onAction,
            bindToCamera = viewModel::bindToCamera,
            navigateUp = viewModel::pop,
        )
    }

    LaunchedEffect(viewModel) {
        viewModel.events
            .collect {
                when (it) {
                    CameraOrganiseeEvent.OpenLocationSettings -> context.startActivity(
                        Intent(
                            Settings.ACTION_LOCATION_SOURCE_SETTINGS,
                        ),
                    )
                }
            }
    }
}

@Composable
internal fun CameraOrganiseeContent(
    modifier: Modifier = Modifier,
    uiState: CameraOrganiseeUiState,
    surfaceRequest: SurfaceRequest?,
    contentPadding: PaddingValues,
    navigateUp: () -> Unit,
    onAction: (CameraOrganiseeAction) -> Unit,
    bindToCamera: suspend (Context, LifecycleOwner) -> Unit,
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    when (uiState) {
        is CameraOrganiseeUiState.CameraPermissionGranted -> {
            Box(modifier = modifier) {
                CameraPreviewContent(
                    previewState = uiState.cameraPreviewState,
                    surfaceRequest = surfaceRequest,
                    topPadding = contentPadding.calculateTopPadding(),
                    bottomPadding = contentPadding.calculateBottomPadding(),
                    ratio = uiState.cameraRatio,
                    isCameraInitialised = uiState.isCameraInitialised,
                    onAction = onAction,
                )

                AnimatedVisibilityFade(target = uiState.sendContentState) { sendContentViewState ->
                    CameraSendOptionsBottomSheet(
                        navigateUp = navigateUp,
                        sendOptionsState = sendContentViewState.cameraOptionsState,
                        onAction = onAction,
                    ) {
                        SendContentView(
                            state = sendContentViewState,
                            topPadding = contentPadding.calculateTopPadding(),
                            action = onAction,
                        )
                    }
                }

                CameraLoaderView(
                    state = uiState.loaderState,
                    topPadding = contentPadding.calculateTopPadding(),
                    ratio = uiState.cameraRatio,
                )
            }

            LaunchedEffect(
                lifecycleOwner,
                bindToCamera,
                uiState.cameraPreviewState.captureOptions.mandatoryOptions,
            ) {
                bindToCamera(context.applicationContext, lifecycleOwner)
            }
        }

        is CameraOrganiseeUiState.CameraPermissionNotGranted -> {
            CameraPlaceholderView(
                modifier = Modifier
                    .aspectRatio(BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT)
                    .fillMaxWidth(),
                topPadding = contentPadding.calculateTopPadding(),
            )
        }

        is CameraOrganiseeUiState.Initial -> {
            // No-Op
        }
    }
}

@Preview(heightDp = 650, widthDp = 350)
@Composable
private fun CameraOrganiseeScreenPreview() {
    BeRealTheme {
        CameraOrganiseeContent(
            uiState = generateCameraPreviewState(),
            surfaceRequest = null,
            contentPadding = PaddingValues(),
            onAction = {},
            navigateUp = {},
            bindToCamera = { _, _ -> },
        )
    }
}

@Preview(heightDp = 650, widthDp = 350)
@Composable
private fun CameraOrganisee_CapturingInProgress_Preview() {
    BeRealTheme {
        CameraOrganiseeContent(
            uiState = generateCameraPreviewState(withLoaderView = true, isCaptureCompleted = false),
            surfaceRequest = null,
            contentPadding = PaddingValues(),
            navigateUp = {},
            onAction = {},
            bindToCamera = { _, _ -> },
        )
    }
}

@Preview(heightDp = 650, widthDp = 350)
@Composable
private fun CameraOrganisee_CapturingCountdown_Preview() {
    BeRealTheme {
        CameraOrganiseeContent(
            uiState = generateCameraPreviewState(
                withLoaderView = true,
                isCaptureCompleted = false,
            ),
            surfaceRequest = null,
            contentPadding = PaddingValues(),
            navigateUp = {},
            onAction = {},
            bindToCamera = { _, _ -> },
        )
    }
}
