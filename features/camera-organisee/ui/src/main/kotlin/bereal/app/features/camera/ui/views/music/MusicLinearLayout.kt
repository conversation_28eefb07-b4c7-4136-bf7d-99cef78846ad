package bereal.app.features.camera.ui.views.music

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import bereal.app.commonandroid.thenIf
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme

enum class MusicLayoutOrientation {
    Vertical, Horizontal
}

@Composable
internal fun MusicLinearLayout(
    modifier: Modifier = Modifier,
    orientation: MusicLayoutOrientation,
    backgroundColor: Color,
    debounceOnClick: (() -> Unit)? = null,
    onLongPress: (() -> Unit)? = null,
    content: @Composable () -> Unit,
) {
    val modifier = remember(modifier) {
        modifier
            .clip(RoundedCornerShape(50))
            .background(backgroundColor)
            .thenIf(debounceOnClick != null) {
                debounceOnClick?.let {
                    Modifier.debounceClickable(
                        onClick = it,
                        onLongClick = onLongPress,
                    )
                } ?: Modifier
            }
    }
    when (orientation) {
        MusicLayoutOrientation.Vertical -> Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(BeRealTheme.spacing.xs),
            modifier = modifier
                .padding(horizontal = BeRealTheme.spacing.s, vertical = BeRealTheme.spacing.m),
        ) {
            content()
        }

        MusicLayoutOrientation.Horizontal -> Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(BeRealTheme.spacing.xs),
            modifier = modifier
                .padding(horizontal = BeRealTheme.spacing.xm, vertical = BeRealTheme.spacing.s),
        ) {
            content()
        }
    }
}
