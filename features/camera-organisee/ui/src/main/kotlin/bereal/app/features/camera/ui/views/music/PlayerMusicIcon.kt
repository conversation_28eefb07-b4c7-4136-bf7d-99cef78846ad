package bereal.app.features.camera.ui.views.music

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.commonandroid.thenIf
import bereal.app.design.R
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme
import bereal.app.entities.PostMusicVisibility
import bereal.app.features.camera.ui.models.MusicUiState
import bereal.app.image.core.imageloader.BeRealImageLoaders
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders
import bereal.app.music.MusicPlayerImage
import bereal.app.music.model.MusicService
import bereal.app.music.toIcon
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import kotlinx.collections.immutable.persistentListOf

@Composable
fun PlayerMusicIcon(
    musicState: MusicUiState,
    modifier: Modifier = Modifier,
    orientation: MusicLayoutOrientation = MusicLayoutOrientation.Horizontal,
    imageLoaders: BeRealImageLoaders,
    backgroundColor: Color = BeRealTheme.colors.background.copy(alpha = 0.33f),
    onClick: () -> Unit,
) {
    when (musicState) {
        is MusicUiState.Loading -> {
            CircleContainer(
                modifier = modifier,
                backgroundColor = backgroundColor,
                onClick = null, // not clickable
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = BeRealTheme.colors.onBackground,
                    strokeWidth = 2.dp,
                )
            }
        }

        is MusicUiState.Logged -> {
            when (musicState) {
                is MusicUiState.Logged.Listening -> {
                    when (val musicVisibility = musicState.postMusicVisibility) {
                        PostMusicVisibility.Private,
                        PostMusicVisibility.Shared,
                        -> {
                            MusicLinearLayout(
                                debounceOnClick = onClick,
                                modifier = modifier,
                                backgroundColor = backgroundColor,
                                orientation = orientation,
                            ) {
                                if (musicVisibility == PostMusicVisibility.Private) {
                                    Image(
                                        modifier = Modifier
                                            .height(16.dp),
                                        painter = painterResource(id = R.drawable.ic_private),
                                        contentDescription = null,
                                        contentScale = ContentScale.FillHeight,
                                    )
                                }

                                MusicPlayerImage(
                                    imageUrl = musicState.artwork,
                                    onClick = null,
                                    onLongPress = null,
                                    cornerRadius = 2.dp,
                                    imageLoader = imageLoaders.music,
                                    modifier = Modifier
                                        .size(20.dp),
                                )

                                Image(
                                    modifier = Modifier
                                        .width(14.dp),
                                    painter = painterResource(id = musicState.musicService.toIcon()),
                                    contentDescription = null,
                                )
                            }
                        }

                        PostMusicVisibility.NoMusic -> {
                            CircleContainer(
                                modifier = modifier,
                                onClick = onClick,
                                backgroundColor = backgroundColor,
                            ) {
                                Image(
                                    modifier = Modifier.size(16.dp),
                                    painter = painterResource(id = R.drawable.music_off),
                                    contentDescription = null,
                                    colorFilter = ColorFilter.tint(BeRealTheme.colors.onBackground),
                                )
                            }
                        }
                    }
                }

                is MusicUiState.Logged.NotListening -> {
                    CircleContainer(
                        modifier = modifier,
                        onClick = onClick,
                        backgroundColor = backgroundColor,
                    ) {
                        Image(
                            modifier = Modifier.size(16.dp),
                            painter = painterResource(id = R.drawable.music_off),
                            contentDescription = null,
                            colorFilter = ColorFilter.tint(BeRealTheme.colors.onBackground),
                        )
                    }
                }
            }
        }

        is MusicUiState.NotLogged ->
            if (musicState.withSparkles) {
                CircleContainer(
                    modifier = modifier,
                    onClick = onClick,
                    backgroundColor = backgroundColor,
                ) {
                    val lottieFile by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.lottie_music_sparkle))
                    val lottieAnimation by animateLottieCompositionAsState(
                        composition = lottieFile,
                        iterations = LottieConstants.IterateForever,
                    )

                    LottieAnimation(
                        composition = lottieFile,
                        progress = { lottieAnimation },
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier
                            .size(36.dp),
                    )
                }
            } else {
                CircleContainer(
                    modifier = modifier,
                    onClick = onClick,
                    backgroundColor = backgroundColor,
                ) {
                    Image(
                        modifier = Modifier.size(16.dp),
                        painter = painterResource(id = R.drawable.music_on),
                        contentDescription = null,
                        colorFilter = ColorFilter.tint(BeRealTheme.colors.onBackground),
                    )
                }
            }

        MusicUiState.Disabled -> {
            // nothing
        }
    }
}

@Composable
private fun CircleContainer(
    modifier: Modifier = Modifier,
    backgroundColor: Color,
    onClick: (() -> Unit)?,
    content: @Composable () -> Unit,
) {
    Box(
        modifier = modifier
            .size(36.dp)
            .aspectRatio(1f)
            .clip(CircleShape)
            .background(color = backgroundColor)
            .thenIf(onClick != null) {
                Modifier.debounceClickable {
                    onClick?.invoke()
                }
            },
        contentAlignment = Alignment.Center,
    ) {
        content()
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_Logging() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.Loading,
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_NotLogged_sparkles() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.NotLogged(
                withSparkles = true,
                availableProviders = persistentListOf(),
            ),
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_NotLogged_noSparkle() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.NotLogged(
                withSparkles = false,
                availableProviders = persistentListOf(),
            ),
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_Logged_NotListening() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.Logged.NotListening(MusicService.Spotify),
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_Logged_Listening_NoMusic() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.Logged.Listening(
                id = "1",
                isrc = "1",
                musicService = MusicService.Spotify,
                track = "title",
                artist = "artist",
                artwork = "",
                postMusicVisibility = PostMusicVisibility.NoMusic,
                previewUrl = "",
                musicLink = "",
            ),
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_Logged_Listening_Private() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.Logged.Listening(
                id = "1",
                isrc = "1",
                musicService = MusicService.Spotify,
                track = "title",
                artist = "artist",
                artwork = "",
                postMusicVisibility = PostMusicVisibility.Private,
                previewUrl = "",
                musicLink = "",
            ),
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}

@Preview
@Composable
private fun PlayerMusicIcon_Logged_Listening_Shared() {
    BeRealTheme {
        PlayerMusicIcon(
            musicState = MusicUiState.Logged.Listening(
                id = "1",
                isrc = "1",
                musicService = MusicService.Spotify,
                track = "title",
                artist = "artist",
                artwork = "",
                postMusicVisibility = PostMusicVisibility.Shared,
                previewUrl = "",
                musicLink = "",
            ),
            onClick = {},
            imageLoaders = LocalBeRealImageLoaders.current,
        )
    }
}
