package bereal.app.features.camera.ui.views.bottomsheet.music.notlistening

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.design.theme.BeRealTheme
import bereal.app.translations.R.string as translations

@Preview
@Composable
internal fun SheetMusicNotListening_preview() {
    BeRealTheme {
        Column {
            SheetMusicNotListening()
        }
    }
}

@Composable
internal fun SheetMusicNotListening() {
    Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.height(BeRealTheme.spacing.s))

        Box(
            modifier = Modifier
                .size(120.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(BeRealTheme.colors.grayScale.gray800),
            contentAlignment = Alignment.Center,
        ) {
            Image(
                painter = painterResource(id = bereal.app.design.R.drawable.music_off),
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp),
            )
        }

        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = BeRealTheme.spacing.xm, bottom = BeRealTheme.spacing.l)
                .padding(horizontal = BeRealTheme.spacing.m),
            text = stringResource(id = translations.music_not_listening_sheet_footer),
            style = BeRealTheme.typography.subHeadline.default,
            color = BeRealTheme.colors.grayScale.gray100,
            textAlign = TextAlign.Center,
        )
    }
}
