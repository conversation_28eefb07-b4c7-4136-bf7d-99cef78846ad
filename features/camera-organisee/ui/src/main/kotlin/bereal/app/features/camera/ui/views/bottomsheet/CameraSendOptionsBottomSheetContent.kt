package bereal.app.features.camera.ui.views.bottomsheet

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import bereal.app.design.sheet.BeRealModalBottomSheet
import bereal.app.design.sheet.PostVisibilityBottomSheetContent
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.CameraBottomSheetType
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraOrganiseeSendOptionsState
import bereal.app.features.camera.ui.models.MusicUiState
import bereal.app.features.camera.ui.views.bottomsheet.music.SheetMusic
import bereal.app.translations.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
internal fun CameraSendOptionsBottomSheetContent(
    sendOptionsState: CameraOrganiseeSendOptionsState,
    bottomSheetState: ModalBottomSheetState,
    navigateUp: () -> Unit,
    onAction: (CameraOrganiseeAction) -> Unit,
    cameraBottomSheetType: CameraBottomSheetType?,
    onHideBottomSheetRequested: () -> Unit,
    content: @Composable () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()

    BeRealModalBottomSheet(
        navigateUp = navigateUp,
        bottomSheetState = bottomSheetState,
        sheetTitle = when (cameraBottomSheetType) {
            CameraBottomSheetType.PostVisibility -> R.string.camera_audience_title
            CameraBottomSheetType.Location -> R.string.camera_location_title
            CameraBottomSheetType.Music -> when (sendOptionsState.musicOptionsState) {
                is MusicUiState.Logged.NotListening -> R.string.music_not_listening_sheet_title
                is MusicUiState.Disabled,
                is MusicUiState.Loading,
                is MusicUiState.NotLogged,
                -> R.string.music_connect_sheet_title

                is MusicUiState.Logged.Listening -> null // I want to handle it directly inside the bottom sheet content
            }
            null -> null
        }?.let { stringResource(it) },
        sheetContent = {
            when (cameraBottomSheetType) {
                CameraBottomSheetType.PostVisibility -> {
                    if (sendOptionsState.postVisibilityOptionsState is CameraOrganiseeSendOptionsState.PostVisibilityOptionsState.Enabled) {
                        PostVisibilityBottomSheetContent(
                            selectedPostVisibility = sendOptionsState.optionsCurrentlySelected.postVisibility,
                            availablePostVisibilities = sendOptionsState.postVisibilityOptionsState.visibilitiesOptions,
                            onPostVisibilitySelected = {
                                if (sendOptionsState.postVisibilityOptionsState.isAllowedToChange) {
                                    onAction(CameraOrganiseeAction.OnPostVisibilityChanged(it))
                                } else {
                                    onAction(CameraOrganiseeAction.OnChangePostVisibilityForbidden)
                                }
                                coroutineScope.launch {
                                    delay(300)
                                    onHideBottomSheetRequested()
                                }
                            },
                        )
                    }
                }

                CameraBottomSheetType.Location -> {
                    if (sendOptionsState.locationOptionsState is CameraOrganiseeSendOptionsState.LocationOptionsState.Enabled) {
                        LocationBottomSheetContent(
                            locationOptions = sendOptionsState.locationOptionsState.locationOptions,
                            selectedLocation = sendOptionsState.optionsCurrentlySelected.locationOption,
                            onLocationSelected = {
                                onAction(CameraOrganiseeAction.OnLocationOptionSelected(it))
                                coroutineScope.launch {
                                    delay(300)
                                    onHideBottomSheetRequested()
                                }
                            },
                        )
                    }
                }

                CameraBottomSheetType.Music -> {
                    SheetMusic(
                        musicState = sendOptionsState.musicOptionsState,
                        onMusicServiceRequireLogin = {
                            coroutineScope.launch {
                                onHideBottomSheetRequested()
                                delay(300) // here we want to hide the bottom sheet before start login the user
                                onAction(CameraOrganiseeAction.OnMusicServiceRequireLogin(it))
                            }
                        },
                        onMusicVisibilitySelected = {
                            coroutineScope.launch {
                                onAction(CameraOrganiseeAction.OnMusicVisibilitySelected(it))
                                onHideBottomSheetRequested()
                            }
                        },
                    )
                }
                null -> { Box { } }
            }

            Spacer(modifier = Modifier.height(BeRealTheme.spacing.xl))
        },
    ) {
        content()
    }
}
