package bereal.app.features.camera.ui.utils

import android.content.Context
import android.media.MediaMetadataRetriever
import android.media.MediaPlayer
import android.net.Uri
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.store.filestore.model.FileExtension
import kotlinx.coroutines.withContext

fun getVideoPlaceholderFileUri(fileStore: FileStore, identifier: String): String {
    val placeholder = fileStore.file(
        fileName = "video_placeholder_$identifier",
        fileExtension = FileExtension.WEBP,
        destinationDir = FileCacheDir.BeRealMyUserLocalVideo,
    )
    return Uri.fromFile(placeholder).toString()
}

suspend fun getVideoDuration(
    context: Context,
    retriever: MediaMetadataRetriever,
    uri: Uri,
    dispatcherProvider: DispatcherProvider,
): Int? = withContext(dispatcherProvider.viewmodel) {
    try {
        val duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
            ?.toIntOrNull()
        if (duration != null && duration > 0) {
            return@withContext duration
        }

        // Fallback to MediaPlayer to get the duration
        val mediaPlayer = MediaPlayer()
        mediaPlayer.setDataSource(context, uri)
        mediaPlayer.prepare() // blocking!
        val fallbackDuration = mediaPlayer.duration
        mediaPlayer.release()

        if (fallbackDuration > 0) {
            return@withContext fallbackDuration
        }

        null
    } catch (e: Exception) {
        null
    }
}
