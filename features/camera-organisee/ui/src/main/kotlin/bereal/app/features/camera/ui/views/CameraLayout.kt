package bereal.app.features.camera.ui.views

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.design.berealimageview.model.BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT
import java.lang.Integer.max
import kotlin.math.roundToInt

private val minControlHeight = 112.dp

@Composable
fun CameraLayout(
    modifier: Modifier = Modifier,
    controlsSharedPaddingWithCamera: Dp = 0.dp,
    ratio: Float = CAMERA_ASPECT_RATIO_PORTRAIT,
    cameraView: @Composable () -> Unit,
    controlsView: @Composable () -> Unit,
    zoomView: (@Composable () -> Unit)? = null,
) {
    var errorState by remember {
        mutableStateOf(emptyMap<String, Int>())
    }

    Layout(
        content = {
            cameraView()
            controlsView()
            zoomView?.invoke()
        },
        modifier = modifier,
    ) { measurables, constraints ->

        val camera = measurables[0]
        val controls = measurables[1]
        val zoomButton = measurables.getOrNull(2)

        val controlsMinHeight = minControlHeight.roundToPx()
        val maxHeight = constraints.maxHeight
        val cameraMaxHeight = maxHeight - controlsMinHeight
        val maxWidth = constraints.maxWidth

        val cameraMaxWidth = (cameraMaxHeight * ratio).toInt()
        if (cameraMaxWidth < 0) {
            errorState = mapOf(
                "maxHeight" to maxHeight,
                "controlsMinHeight" to controlsMinHeight,
                "maxWidth" to maxWidth,
            )
        }
        val cameraPlaceable = camera.measure(
            Constraints(
                maxHeight = max(0, cameraMaxHeight),
                maxWidth = Integer.min(
                    max(0, cameraMaxWidth),
                    maxWidth,
                ),
            ),
        )

        val controlsPlaceable = controls.measure(
            Constraints(
                minHeight = max(0, controlsMinHeight),
                maxHeight = max(controlsMinHeight, max(0, maxHeight - cameraPlaceable.height)),
                minWidth = constraints.maxWidth,
                maxWidth = constraints.maxWidth,
            ).let {
                it.copy(
                    minHeight = (it.minHeight + controlsSharedPaddingWithCamera.toPx()).coerceAtMost(it.maxHeight.toFloat()).toInt(),
                )
            },
        )

        val zoomButtonPlaceable = zoomButton?.measure(constraints)

        layout(constraints.maxWidth, constraints.maxHeight) {
            // To place the controls below the camera
            val controlsY =
                (cameraPlaceable.height - controlsSharedPaddingWithCamera.toPx()).roundToInt()

            controlsPlaceable.place(0, controlsY)

            // To center the camera view
            val cameraX = (constraints.maxWidth - cameraPlaceable.width) / 2
            cameraPlaceable.place(cameraX, 0)

            zoomButtonPlaceable?.let {
                val zoomButtonX = (constraints.maxWidth / 2) - (zoomButtonPlaceable.width / 2)
                val zoomButtonY = cameraPlaceable.height - (1.5 * zoomButtonPlaceable.height).toInt()
                zoomButtonPlaceable.place(zoomButtonX, zoomButtonY)
            }
        }
    }
}
