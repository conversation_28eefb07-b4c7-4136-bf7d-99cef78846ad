package bereal.app.features.camera.ui.views.bottomsheet.music.notlogged

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.models.MusicUiState
import bereal.app.music.login.MusicLoginStatus
import bereal.app.music.login.MusicProviderCard
import bereal.app.music.model.MusicService
import kotlinx.collections.immutable.persistentListOf
import bereal.app.translations.R.string as translations

@Preview
@Composable
internal fun SheetMusicNotLoggedPreview() {
    BeRealTheme {
        Column {
            SheetMusicNotLogged(
                musicState = MusicUiState.NotLogged(
                    availableProviders = persistentListOf(MusicService.Spotify),
                    withSparkles = false,
                ),
                onMusicServiceRequireLogin = {},
            )
        }
    }
}

@Preview
@Composable
internal fun SheetMusicNotLoggedPreview_twoProviders() {
    BeRealTheme {
        Column {
            SheetMusicNotLogged(
                musicState = MusicUiState.NotLogged(
                    availableProviders = persistentListOf(
                        MusicService.Spotify,
                        MusicService.Spotify,
                    ),
                    withSparkles = false,
                ),
                onMusicServiceRequireLogin = {},
            )
        }
    }
}

@Composable
internal fun ColumnScope.SheetMusicNotLogged(
    musicState: MusicUiState.NotLogged,
    onMusicServiceRequireLogin: (MusicService) -> Unit,
) {
    Text(
        modifier = Modifier.padding(horizontal = BeRealTheme.spacing.m),
        color = BeRealTheme.colors.grayScale.gray100,
        style = BeRealTheme.typography.subHeadline.default,
        textAlign = TextAlign.Center,
        text = stringResource(id = translations.music_connect_sheet_description),
    )

    Spacer(modifier = Modifier.height(BeRealTheme.spacing.m))

    Column(verticalArrangement = Arrangement.spacedBy(BeRealTheme.spacing.m)) {
        musicState.availableProviders.forEach { service ->
            MusicProviderCard(
                service = service,
                modifier = Modifier.padding(horizontal = BeRealTheme.spacing.m),
                onClicked = {
                    onMusicServiceRequireLogin(service)
                },
                loggedStatus = MusicLoginStatus.NotLogged,
            )
        }
    }

    Text(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                top = BeRealTheme.spacing.m,
                bottom = BeRealTheme.spacing.l,
            )
            .padding(horizontal = BeRealTheme.spacing.l),
        text = stringResource(id = translations.music_connect_sheet_more_soon),
        textAlign = TextAlign.Center,
        style = BeRealTheme.typography.footnote.default,
        color = BeRealTheme.colors.grayScale.gray500,
    )
}
