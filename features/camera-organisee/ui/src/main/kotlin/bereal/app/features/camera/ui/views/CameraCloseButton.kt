package bereal.app.features.camera.ui.views

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import bereal.app.design.R
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme

@Composable
fun CameraCloseButton(
    modifier: Modifier = Modifier,
    debounceOnClick: () -> Unit,
) {
    Box(
        modifier = modifier
            .size(36.dp)
            .clip(CircleShape)
            .background(BeRealTheme.colors.background.copy(alpha = 0.4f))
            .debounceClickable { debounceOnClick() },
        contentAlignment = Alignment.Center,
    ) {
        Image(
            painter = painterResource(R.drawable.close),
            contentDescription = null,
            modifier = Modifier.size(20.dp),
        )
    }
}
