package bereal.app.features.camera.ui.views

import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.design.AnimatedVisibilityFade
import bereal.app.design.button.Chip
import bereal.app.design.sheet.icon
import bereal.app.design.sheet.title
import bereal.app.design.theme.BeRealTheme
import bereal.app.entities.ShareLocation
import bereal.app.features.camera.ui.models.CameraBottomSheetType
import bereal.app.features.camera.ui.models.CameraOrganiseeSendOptionsState
import bereal.app.features.camera.ui.models.MusicUiState
import bereal.app.features.camera.ui.preview.generateSendOptionsState
import bereal.app.features.camera.ui.views.music.PlayerMusicIcon
import bereal.app.image.core.imageloader.BeRealImageLoaders
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders

@Composable
internal fun CameraSendOptionsButtonsView(
    modifier: Modifier = Modifier,
    visible: <PERSON><PERSON><PERSON>,
    cameraSendOptionsState: CameraOrganiseeSendOptionsState,
    imageLoaders: BeRealImageLoaders,
    onClickOption: (CameraBottomSheetType) -> Unit,
) {
    AnimatedVisibilityFade(
        visible = visible,
        modifier = modifier,
    ) {
        SendOptionsLayout {
            if (cameraSendOptionsState.postVisibilityOptionsState is CameraOrganiseeSendOptionsState.PostVisibilityOptionsState.Enabled &&
                cameraSendOptionsState.optionsCurrentlySelected.postVisibility != null
            ) {
                val selectedPostVisibility = cameraSendOptionsState.optionsCurrentlySelected.postVisibility
                Chip(
                    iconRes = selectedPostVisibility.icon(),
                    text = selectedPostVisibility.title(),
                    backgroundColor = BeRealTheme.colors.background.copy(alpha = 0.33f),
                    debounceOnClick = {
                        onClickOption(CameraBottomSheetType.PostVisibility)
                    },
                )
            } else {
                Box(Modifier)
            }

            if (cameraSendOptionsState.locationOptionsState is CameraOrganiseeSendOptionsState.LocationOptionsState.Enabled) {
                val selectedLocation = cameraSendOptionsState.optionsCurrentlySelected.locationOption
                Chip(
                    iconRes = selectedLocation.icon,
                    text = when (selectedLocation.type) {
                        ShareLocation.Off -> selectedLocation.title
                        ShareLocation.Approximate -> selectedLocation.description
                        ShareLocation.Precise -> selectedLocation.description
                    },
                    backgroundColor = BeRealTheme.colors.background.copy(alpha = 0.33f),
                    debounceOnClick = {
                        onClickOption(CameraBottomSheetType.Location)
                    },
                )
            } else {
                Box(Modifier)
            }

            if (cameraSendOptionsState.musicOptionsState !is MusicUiState.Disabled) {
                PlayerMusicIcon(
                    musicState = cameraSendOptionsState.musicOptionsState,
                    onClick = {
                        when (cameraSendOptionsState.musicOptionsState) {
                            is MusicUiState.Loading -> {
                                // not clickable
                            }

                            is MusicUiState.Logged.Listening,
                            is MusicUiState.Logged.NotListening,
                            is MusicUiState.NotLogged,
                            -> {
                                onClickOption(CameraBottomSheetType.Music)
                            }
                            is MusicUiState.Disabled -> {
                                // nothing is displayed so not clickable
                            }
                        }
                    },
                    imageLoaders = imageLoaders,
                )
            }
        }
    }
}

@Preview
@Composable
private fun CameraSendOptionsButtonsViewPreview() {
    BeRealTheme {
        CameraSendOptionsButtonsView(
            visible = true,
            cameraSendOptionsState = generateSendOptionsState(),
            imageLoaders = LocalBeRealImageLoaders.current,
            onClickOption = {},
        )
    }
}
