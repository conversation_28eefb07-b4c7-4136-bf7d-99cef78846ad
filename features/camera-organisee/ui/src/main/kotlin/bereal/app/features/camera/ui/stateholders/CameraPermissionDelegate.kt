package bereal.app.features.camera.ui.stateholders

import bereal.app.features.camera.domain.usecases.permission.BindCameraPermissionsWithTermsUseCase
import bereal.app.features.camera.domain.usecases.permission.IsCameraPermissionEnabledUseCase
import bereal.app.features.camera.domain.usecases.permission.RequestCameraPermissionUseCase
import bereal.app.navigation.NavigationManager
import kotlinx.coroutines.flow.Flow
import org.koin.core.annotation.Factory

@Factory
class CameraPermissionDelegate(
    private val isCameraPermissionEnabledUseCase: IsCameraPermissionEnabledUseCase,
    private val requestCameraPermissionUseCase: RequestCameraPermissionUseCase,
    private val bindCameraPermissionsWithTermsUseCase: BindCameraPermissionsWithTermsUseCase,
    private val navigationManager: NavigationManager,
) {

    fun observeCameraPermissionEnabled(): Flow<Boolean> = isCameraPermissionEnabledUseCase()

    fun bindCameraPermissionsWithTerms() = bindCameraPermissionsWithTermsUseCase()

    suspend fun requestCameraPermission() {
        if (!requestCameraPermissionUseCase()) {
            navigationManager.pop()
        }
    }
}
