package bereal.app.features.camera.ui.stateholders

import android.content.Context
import androidx.camera.core.CameraState
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.lifecycle.LifecycleOwner
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.design.dialog.DialogDisplayer
import bereal.app.design.dialog.alert.model.Cancel
import bereal.app.design.dialog.alert.model.Confirm
import bereal.app.design.dialog.model.Dialog
import bereal.app.entities.error.throwable
import bereal.app.features.camera.domain.models.CameraEventAnalyticsDomainModel
import bereal.app.features.camera.domain.usecases.CameraAnalyticsUseCase
import bereal.app.features.camera.ui.mapper.getCameraErrorMessage
import bereal.app.features.camera.ui.models.CameraOrganiseeError
import bereal.app.translations.R
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory
import timber.log.Timber

@Factory
class CameraErrorDelegate(
    private val dialogDisplayer: DialogDisplayer,
    private val dispatcherProvider: DispatcherProvider,
    private val stringProvider: StringProvider,
    private val analyticsUseCase: CameraAnalyticsUseCase,
) {

    suspend fun handleCriticalError(
        processCameraProvider: ProcessCameraProvider,
        appContext: Context,
        lifecycleOwner: LifecycleOwner,
        bindToCamera: suspend (Context, LifecycleOwner) -> Unit,
    ) = withContext(dispatcherProvider.ui) {
        processCameraProvider.unbindAll()
        bindToCamera(appContext, lifecycleOwner)
    }

    fun handleRecoverableError(
        error: CameraOrganiseeError,
        onErrorRetryClicked: () -> Unit,
        onErrorCancelClicked: () -> Unit,
    ) {
        if (error is CameraOrganiseeError.CameraClosedByUserWhenCapturing) {
            return
        }

        val throwable = when (error) {
            is CameraOrganiseeError.DoNotDisturbEnabled -> error.throwable
            is CameraOrganiseeError.Generic -> error.error.throwable
            is CameraOrganiseeError.TakePhoto -> error.error.throwable
            CameraOrganiseeError.CameraClosedByUserWhenCapturing -> Throwable("Camera closed by user when capturing")
        }
        Timber.e(throwable)
        trackCameraError(throwable.message ?: "Unknown error in camera")
        val errorMessage = getCameraErrorMessage(error = error, stringProvider = stringProvider)
        dialogDisplayer.display(
            Dialog.Alert(
                title = null,
                description = errorMessage,
                confirm = Confirm(
                    customText = stringProvider[R.string.general_error_try_again],
                    isCritical = false,
                    onConfirm = { onErrorRetryClicked() },
                ),
                cancel = Cancel(
                    customText = stringProvider[R.string.general_later],
                    onCancel = { onErrorCancelClicked() },
                    cancelOnTouchOutside = false,
                ),
            ),
        )
    }

    fun trackCameraError(message: String) {
        analyticsUseCase(
            CameraEventAnalyticsDomainModel.CameraError(message = message),
        )
    }

    fun getErrorMessageFromCode(error: CameraState.StateError): String {
        return when (error.code) {
            CameraState.ERROR_DO_NOT_DISTURB_MODE_ENABLED -> {
                stringProvider[R.string.camera_error_do_not_disturb]
            }

            else -> stringProvider[R.string.camera_error_during_bereal]
        }
    }
}
