package bereal.app.features.camera.ui.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.design.textfield.AutoSizeText
import bereal.app.design.theme.BeRealTheme

@Composable
fun ZoomIndicator(
    modifier: Modifier = Modifier,
    zoom: String,
) {
    Box(
        modifier = modifier
            .size(42.dp)
            .clip(CircleShape)
            .background(BeRealTheme.colors.background.copy(alpha = 0.7f)),
        contentAlignment = Alignment.Center,
    ) {
        AutoSizeText(
            text = zoom,
            style = BeRealTheme.typography.footnote.bold,
            color = BeRealTheme.colors.onBackground,
            textAlign = TextAlign.Center,
            maxLines = 1,
        )
    }
}

@Preview(
    widthDp = 100,
    heightDp = 100,
)
@Composable
private fun ZoomIndicatorPreview() {
    BeRealTheme {
        Column(modifier = Modifier.background(BeRealTheme.colors.onBackground)) {
            ZoomIndicator(
                zoom = "-9.99x",
            )
        }
    }
}
