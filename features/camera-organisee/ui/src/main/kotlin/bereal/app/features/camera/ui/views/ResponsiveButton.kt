package bereal.app.features.camera.ui.views

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.design.debounceClick
import bereal.app.design.theme.BeRealTheme

@Composable
fun ResponsiveButton(
    painter: Painter,
    modifier: Modifier = Modifier,
    visible: Boolean = true,
    iconSize: Dp = 36.dp,
    iconTint: Color = BeRealTheme.colors.onBackground,
    isClickable: Boolean,
    debounceOnClick: () -> Unit,
    onLongClick: (() -> Unit)? = null,
    onLongClickReleased: (() -> Unit)? = null,
) {
    val interactionSource = remember {
        MutableInteractionSource()
    }

    val isPressed by interactionSource.collectIsPressedAsState()

    var isLongClickStarted by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(isLongClickStarted, isPressed, onLongClickReleased) {
        if (isLongClickStarted && !isPressed) {
            isLongClickStarted = false
            onLongClickReleased?.invoke()
        }
    }

    val scale = when {
        !visible -> 0f
        isPressed && onLongClick == null && onLongClickReleased == null -> 1.3f
        else -> 1f
    }

    val scaleState: Float by animateFloatAsState(
        targetValue = scale,
        animationSpec = tween(
            durationMillis = BeRealTheme.animDuration.short,
            easing = FastOutSlowInEasing,
        ),
    )

    Box(
        modifier = Modifier
            .size(iconSize)
            .combinedClickable(
                enabled = visible && isClickable,
                interactionSource = interactionSource,
                indication = null,
                onClick = debounceClick(debounceOnClick),
                onLongClick = {
                    if (onLongClick != null) {
                        isLongClickStarted = true
                        onLongClick.invoke()
                    }
                },
            )
            .then(modifier),
    ) {
        Icon(
            painter = painter,
            contentDescription = null,
            tint = if (isPressed) iconTint.copy(alpha = 0.7f) else iconTint,
            modifier = Modifier
                .size(iconSize)
                .scale(scaleState)
                .align(Alignment.Center),
        )
    }
}
