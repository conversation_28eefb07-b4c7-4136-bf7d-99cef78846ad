package bereal.app.features.camera.ui.utils

import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.model.AnalyticsEvent
import bereal.app.analytics.model.AnalyticsParam
import bereal.app.features.camera.ui.models.CameraOrganiseeCaptureState
import bereal.app.features.camera.ui.models.MediaCaptured
import kotlin.collections.component1
import kotlin.collections.component2

internal fun logCameraCaptureDebugInfos(
    analyticsManager: AnalyticsManager,
    captureState: CameraOrganiseeCaptureState.Completed,
) {
    val contentFirstParams = getMediasAnalyticsParams(media = captureState.firstMedia, prefix = "first")
    val contentSecondParams =
        captureState.secondMedia?.let { getMediasAnalyticsParams(media = it, prefix = "second") } ?: emptyList()

    analyticsManager.logEvent(object : AnalyticsEvent {
        override val name = "cameraCapturePicturesDebugInfos"
        override val params = contentFirstParams + contentSecondParams
    })
}

private fun getMediasAnalyticsParams(media: MediaCaptured, prefix: String): List<AnalyticsParam> {
    return buildList {
        val infos = when (media) {
            is MediaCaptured.Image -> {
                media.data.debugInfos
            }
            is MediaCaptured.Video,
            is MediaCaptured.MediaGallery,
            -> {
                emptyMap()
            }
        }
        infos.forEach { (name, value) ->
            add(AnalyticsParam("${prefix}_$name", value))
        }
    }
}
