package bereal.app.features.camera.ui.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import bereal.app.design.berealimageview.model.BeRealViewConstants
import bereal.app.design.berealimageview.model.BeRealViewConstants.PRIMARY_ROUNDED_CORNER
import bereal.app.design.theme.BeRealTheme
import bereal.app.dualview.DualView
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraSendContentState
import bereal.app.features.camera.ui.preview.generateCameraSendContentState
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders

@Composable
fun SendContentView(
    state: CameraSendContentState,
    topPadding: Dp,
    action: (CameraOrganiseeAction) -> Unit,
) {
    val imageLoader = LocalBeRealImageLoaders.current
    val controlsSharedPaddingWithCamera = 30.dp

    var isLongPressingBeReal by remember {
        mutableStateOf(false)
    }

    var isZooming by remember {
        mutableStateOf(false)
    }

    Box(
        modifier = Modifier
            .background(BeRealTheme.colors.background)
            .padding(top = topPadding),
    ) {
        CameraLayout(
            controlsSharedPaddingWithCamera = controlsSharedPaddingWithCamera,
            cameraView = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT)
                        .clip(RoundedCornerShape(PRIMARY_ROUNDED_CORNER)),
                ) {
                    DualView(
                        modifier = Modifier.matchParentSize(),
                        isCurrentlyVisible = rememberUpdatedState(true),
                        imageLoader = imageLoader.camera,
                        data = state.dualViewData,
                        onZoomPrimary = { isZooming = it },
                        onLongPress = { isLongPressingBeReal = it },
                    )

                    CameraCloseButton(
                        modifier = Modifier
                            .padding(BeRealTheme.spacing.m)
                            .align(Alignment.TopEnd),
                        debounceOnClick = {
                            action(CameraOrganiseeAction.DiscardMediasCaptured)
                        },
                    )

                    CameraSendOptionsButtonsView(
                        modifier = Modifier
                            .padding(BeRealTheme.spacing.m)
                            .align(Alignment.BottomCenter),
                        visible = state.isCaptureCompleted,
                        cameraSendOptionsState = state.cameraOptionsState,
                        imageLoaders = imageLoader,
                        onClickOption = { action(CameraOrganiseeAction.OnBottomSheetSelected(it)) },
                    )
                }
            },
            controlsView = {
                CameraSendButtonView(
                    modifier = Modifier.fillMaxWidth(),
                    contentPadding = PaddingValues(top = controlsSharedPaddingWithCamera),
                    isCaptureCompleted = state.isCaptureCompleted,
                    sendButtonState = state.sendButtonState,
                    onClick = {
                        action(CameraOrganiseeAction.SendBeReal)
                    },
                )
            },
        )
    }
}

@Preview(heightDp = 650, widthDp = 350)
@Composable
private fun SendContentPreview() {
    BeRealTheme {
        SendContentView(
            state = generateCameraSendContentState(),
            topPadding = 16.dp,
            action = {},
        )
    }
}

@Preview(heightDp = 650, widthDp = 350)
@Composable
private fun SendContent_WithoutMusic_Preview() {
    BeRealTheme {
        SendContentView(
            state = generateCameraSendContentState(
                withMusic = false,
            ),
            topPadding = 16.dp,
            action = {},
        )
    }
}

@Preview(heightDp = 650, widthDp = 350)
@Composable
private fun SendContent_CaptureInProgress_Preview() {
    BeRealTheme {
        SendContentView(
            state = generateCameraSendContentState(
                isCaptureCompleted = false,
            ),
            topPadding = 16.dp,
            action = {},
        )
    }
}
