package bereal.app.features.camera.ui

import android.content.Context
import android.hardware.camera2.CameraCaptureSession
import android.hardware.camera2.CaptureRequest
import androidx.annotation.OptIn
import androidx.camera.camera2.interop.Camera2Interop
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector.DEFAULT_BACK_CAMERA
import androidx.camera.core.CameraSelector.DEFAULT_FRONT_CAMERA
import androidx.camera.core.FocusMeteringAction
import androidx.camera.core.MeteringPoint
import androidx.camera.core.Preview
import androidx.camera.core.SurfaceOrientedMeteringPointFactory
import androidx.camera.core.SurfaceRequest
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.lifecycle.awaitInstance
import androidx.compose.runtime.Immutable
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import bereal.app.analytics.model.AnalyticsView
import bereal.app.common.combines
import bereal.app.common.deleteChildFilesOlderThan
import bereal.app.common.stateIn
import bereal.app.commonandroid.BeRealStrings
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.design.dialog.DialogDisplayer
import bereal.app.design.dialog.alert.model.Confirm
import bereal.app.design.dialog.model.Dialog
import bereal.app.design.feedback.BeRealFeedbackManager
import bereal.app.design.feedback.snack.SnackBarDatas
import bereal.app.design.theme.BeRealColors
import bereal.app.entities.PostType
import bereal.app.entities.toAnalyticsValue
import bereal.app.features.camera.domain.delegate.CameraTypeDelegate
import bereal.app.features.camera.domain.models.CameraEventAnalyticsDomainModel
import bereal.app.features.camera.domain.models.CameraParams
import bereal.app.features.camera.domain.usecases.CameraAnalyticsUseCase
import bereal.app.features.camera.domain.usecases.ObserveCameraPostInfoUseCase
import bereal.app.features.camera.domain.usecases.ObserveCaptureInProgressMessageTypeUseCase
import bereal.app.features.camera.domain.usecases.PrefetchSignedUrlsUseCase
import bereal.app.features.camera.domain.usecases.ShowProfilePictureAfterBeRealCtaUseCase
import bereal.app.features.camera.ui.mapper.CameraOrganiseeUiMapper
import bereal.app.features.camera.ui.mapper.defaultSendOptionsState
import bereal.app.features.camera.ui.models.CameraBottomSheetType
import bereal.app.features.camera.ui.models.CameraOrganiseeAction
import bereal.app.features.camera.ui.models.CameraOrganiseeCaptureState
import bereal.app.features.camera.ui.models.CameraOrganiseeEvent
import bereal.app.features.camera.ui.models.CameraOrganiseeUiState
import bereal.app.features.camera.ui.models.Facing
import bereal.app.features.camera.ui.models.MediaCaptured
import bereal.app.features.camera.ui.profilepicture.CameraProfilePictureDestination
import bereal.app.features.camera.ui.stateholders.CameraErrorDelegate
import bereal.app.features.camera.ui.stateholders.CameraPermissionDelegate
import bereal.app.features.camera.ui.stateholders.CameraSendOptionsDelegate
import bereal.app.features.camera.ui.stateholders.CheckMusicAfter
import bereal.app.features.camera.ui.stateholders.PostLocationDelegate
import bereal.app.features.camera.ui.stateholders.PostMusicDelegate
import bereal.app.features.camera.ui.stateholders.PostVisibilityDelegate
import bereal.app.features.camera.ui.stateholders.SendBeRealDelegate
import bereal.app.features.camera.ui.stateholders.TakePictureDelegate
import bereal.app.features.camera.ui.utils.toBeRealMedia
import bereal.app.features.mypost.domain.usecases.ObserveCanPostUsingMyStateUseCase
import bereal.app.moment.usecases.GetCurrentMomentUseCase
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.MainScreenDirection
import bereal.app.settings.usecases.DismissNewFormatTooltipUseCase
import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.time.provider.currentTimeInstant
import bereal.app.translations.R
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.awaitCancellation
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam
import org.koin.core.component.KoinComponent
import org.koin.core.parameter.parametersOf
import timber.log.Timber
import java.io.File
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger
import kotlin.time.Duration.Companion.days

@ExperimentalCamera2Interop
@KoinViewModel
class CameraOrganiseeViewModel(
    @InjectedParam private val params: Params,
    private val takePictureDelegate: TakePictureDelegate,
    private val dispatcherProvider: DispatcherProvider,
    private val beRealTimeProvider: BeRealTimeProvider,
    private val cameraOrganiseeUiMapper: CameraOrganiseeUiMapper,
    observeCameraPostInfoUseCase: ObserveCameraPostInfoUseCase,
    observeCaptureInProgressMessageType: ObserveCaptureInProgressMessageTypeUseCase,
    private val dismissedNewFormatTooltipUseCase: DismissNewFormatTooltipUseCase,
    private val stringProvider: StringProvider,
    private val postLocationDelegate: PostLocationDelegate,
    private val feedbackManager: BeRealFeedbackManager,
    private val analyticsUseCase: CameraAnalyticsUseCase,
    private val getCurrentMomentUseCase: GetCurrentMomentUseCase,
    private val prefetchSignedUrls: PrefetchSignedUrlsUseCase,
    private val dialogDisplayer: DialogDisplayer,
    private val cameraPermissionDelegate: CameraPermissionDelegate,
    observeCanPostUsingMyStateUseCase: ObserveCanPostUsingMyStateUseCase,
    private val fileStore: FileStore,
    private val postMusicDelegate: PostMusicDelegate,
    context: Context,
    private val cameraErrorDelegate: CameraErrorDelegate,
    private val sendBeRealDelegate: SendBeRealDelegate,
    private val navigationManager: NavigationManager,
    private val showProfilePictureAfterBeRealCtaUseCase: ShowProfilePictureAfterBeRealCtaUseCase,
) : ViewModel(), KoinComponent {

    private var centeredFocusedPoint: MeteringPoint? = null

    @Immutable
    data class Params(
        val cameraTypeDelegate: CameraTypeDelegate,
        val params: CameraParams?,
    )

    // this will allow to identify the media files that were created and clean them up
    private val cameraSessionIdentifier = UUID.randomUUID().toString()

    //region StateHolders
    private val postVisibilityDelegate =
        getKoin().get<PostVisibilityDelegate> {
            parametersOf(
                params.params?.canSavePostVisibilityPreference == true,
            )
        }.also(::addCloseable)

    private val cameraSendOptionsDelegate =
        getKoin().get<CameraSendOptionsDelegate> {
            parametersOf(
                CameraSendOptionsDelegate.Params(
                    isLocationEnabled = params.params?.locationEnabled == true,
                    isMusicEnabled = params.params?.musicEnabled == true,
                ),
            )
        }
    //endregion

    private val analyticsView = AnalyticsView.CameraSendBereal
    private val countDownFinished = MutableStateFlow<Boolean?>(null)
    private val beRealTakenAt = MutableStateFlow<Instant?>(null)
    private val initCameraTracking = MutableStateFlow(Pair(false, System.currentTimeMillis()))
    private val _surfaceRequest = MutableStateFlow<SurfaceRequest?>(null)
    val surfaceRequest: StateFlow<SurfaceRequest?> = _surfaceRequest
    private var retryBindToCameraCount = AtomicInteger(0)

    private val captureState = MutableStateFlow<CameraOrganiseeCaptureState>(
        CameraOrganiseeCaptureState.Idle(
            openScreenTime = beRealTimeProvider.currentTimeInstant,
        ),
    )

    private val canMyUserPost = observeCanPostUsingMyStateUseCase().map { canPost ->
        canPost || params.params?.ignoreRemainingPost == true
    }
        .flowOn(dispatcherProvider.viewmodel)
        .stateIn(viewModelScope, SharingStarted.Eagerly, true)

    private val _events = MutableSharedFlow<CameraOrganiseeEvent>()
    val events = _events.asSharedFlow()

    @OptIn(ExperimentalCamera2Interop::class)
    private val cameraPreview: Preview = Preview.Builder()
        .apply {
            val captureCallback = object : CameraCaptureSession.CaptureCallback() {
                override fun onCaptureStarted(
                    session: CameraCaptureSession,
                    request: CaptureRequest,
                    timestamp: Long,
                    frameNumber: Long,
                ) {
                    super.onCaptureStarted(session, request, timestamp, frameNumber)
                    if (frameNumber == 5L) { // skip some frames to make sure UI is rendering
                        takePictureDelegate.isCameraInitialised.value = true
                    }
                }
            }
            val previewExtender = Camera2Interop.Extender(this)
            previewExtender.setSessionCaptureCallback(captureCallback)
        }.build().apply {
            setSurfaceProvider { newSurfaceRequest ->
                _surfaceRequest.update { newSurfaceRequest }
                val width = newSurfaceRequest.resolution.width.toFloat()
                val height = newSurfaceRequest.resolution.height.toFloat()

                surfaceMeteringPointFactory = SurfaceOrientedMeteringPointFactory(
                    width,
                    height,
                ).also {
                    centeredFocusedPoint = it.createPoint(width / 2, height / 2)
                }
            }
        }

    private var processCameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var surfaceMeteringPointFactory: SurfaceOrientedMeteringPointFactory? = null

    private val cameraCacheDir = File(context.filesDir, "camera_cache")

    private fun setupCacheDir() {
        try {
            cameraCacheDir.mkdirs()
            viewModelScope.launch(dispatcherProvider.viewmodel) {
                cameraCacheDir.deleteChildFilesOlderThan(MAX_FILES_AGE_KEPT_IN_CAMERA_CACHE)
            }
        } catch (t: Throwable) {
            Timber.e(t)
        }
    }

    init {
        setupCacheDir()
        initCameraPermission()

        viewModelScope.launch(dispatcherProvider.viewmodel) {
            prefetchSignedUrls()
            dismissedNewFormatTooltipUseCase()
        }

        captureState.filterIsInstance<CameraOrganiseeCaptureState.Completed>()
            .distinctUntilChanged()
            .map {
                val format = when (it.firstMedia) {
                    is MediaCaptured.MediaGallery -> PostType.MediaFromGallery
                    is MediaCaptured.Image, is MediaCaptured.Video -> PostType.Regular
                }

                analyticsUseCase(
                    CameraEventAnalyticsDomainModel.ViewedCameraCapturedPreview(
                        postFormat = format.toAnalyticsValue(),
                    ),
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(viewModelScope)

        setUpDefaultSendOptions()

        takePictureDelegate.isCameraInitialised
            .filter { it }
            .take(1)
            .onEach {
                val (hasAlreadyTrackedInitCamera, initialLoadingTime) = initCameraTracking.value
                if (!hasAlreadyTrackedInitCamera) {
                    trackInitCameraTime(initialLoadingTime)
                }
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(viewModelScope)
    }

    private fun initCameraPermission() {
        viewModelScope.launch {
            cameraPermissionDelegate.bindCameraPermissionsWithTerms().collect {}
        }
        viewModelScope.launch {
            cameraPermissionDelegate.requestCameraPermission()
        }
    }

    val uiState: StateFlow<CameraOrganiseeUiState> = combines(
        captureState,
        takePictureDelegate.captureOptions,
        cameraPermissionDelegate.observeCameraPermissionEnabled(),
        params.cameraTypeDelegate.observeCameraTimerState(),
        observeCameraPostInfoUseCase(),
        countDownFinished,
        beRealTakenAt,
        observeCaptureInProgressMessageType(),
        cameraSendOptionsDelegate
            .observeCameraSendOptions(
                observePostVisibility = params.cameraTypeDelegate.observeVisibility(params.params?.audienceToSelect),
                observeLocationState = postLocationDelegate.observeLocation(),
                observeMusic = postMusicDelegate.observeMusic(),
                isMediaFromGallery = captureState.map {
                    if (it is CameraOrganiseeCaptureState.Completed) {
                        it.firstMedia is MediaCaptured.MediaGallery
                    } else {
                        false
                    }
                },
            )
            .stateIn(viewModelScope, defaultSendOptionsState(stringProvider)),
        sendBeRealDelegate.isSendingBeReal,
        takePictureDelegate.isCameraInitialised,
    )
        .map {
                (
                    cameraCaptureState, captureOptions, isPermissionEnabled, timerInfo,
                    postInfo, countdownFinish,
                    beRealTakenAt, captureInProgressMessageType,
                    sendOptions, isCurrentlySendingBeReal,
                    isCameraInitialised,
                ),
            ->
            cameraOrganiseeUiMapper.mapToState(
                isPermissionEnabled = isPermissionEnabled,
                timerInfo = timerInfo,
                cameraPostInfo = postInfo,
                expirationTimeMs = params.params?.expirationTimeMs ?: 0L,
                countDownFinished = countdownFinish,
                beRealTakenAt = beRealTakenAt?.toEpochMilliseconds(),
                captureState = cameraCaptureState,
                captureOptions = captureOptions,
                captureInProgressMessageType = captureInProgressMessageType,
                cameraSendOptions = sendOptions,
                isCurrentlySendingBeReal = isCurrentlySendingBeReal,
                isCameraInitialised = isCameraInitialised,
            )
        }
        .stateIn(viewModelScope, CameraOrganiseeUiState.Initial())

    // region actions & events
    fun onAction(action: CameraOrganiseeAction) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            when (action) {
                CameraOrganiseeAction.OnBackPressed -> pop()
                CameraOrganiseeAction.TakePicture -> takePrimary()
                CameraOrganiseeAction.DiscardMediasCaptured -> resetMediasCaptured()
                is CameraOrganiseeAction.SendBeReal -> sendBeReal()

                CameraOrganiseeAction.FlipCamera -> takePictureDelegate.switchCamera(
                    fromUserAction = true,
                )

                CameraOrganiseeAction.ToggleFlash -> takePictureDelegate.toggleFlash()
                is CameraOrganiseeAction.OnZoomChanged -> {
                    camera?.cameraControl?.setZoomRatio(action.zoom)
                    takePictureDelegate.updateZoom(action.zoom)
                }

                is CameraOrganiseeAction.OnBottomSheetSelected -> {
                    when (action.type) {
                        CameraBottomSheetType.PostVisibility -> {
                            cameraSendOptionsDelegate.updateSelectedBottomSheetType(action.type)
                        }

                        CameraBottomSheetType.Location -> {
                            cameraSendOptionsDelegate.updateSelectedBottomSheetType(action.type)
                        }

                        CameraBottomSheetType.Music -> {
                            postMusicDelegate.onMusicBottomSheetOpened(
                                musicState = (uiState.value as? CameraOrganiseeUiState.CameraPermissionGranted)?.sendContentState?.cameraOptionsState?.musicOptionsState,
                                isOpened = cameraSendOptionsDelegate.selectedBottomSheetType.value != null,
                                updateSelectedBottomSheetType = {
                                    cameraSendOptionsDelegate.updateSelectedBottomSheetType(
                                        action.type,
                                    )
                                },
                            )
                        }
                    }
                }

                CameraOrganiseeAction.OnHideBottomSheet -> cameraSendOptionsDelegate.updateSelectedBottomSheetType(
                    null,
                )

                is CameraOrganiseeAction.OnLocationOptionSelected -> {
                    postLocationDelegate.onShareLocationSelected(
                        location = action.location.type,
                        onClickOpenSettings = ::openLocationSettings,
                    )
                }

                is CameraOrganiseeAction.OnMusicServiceRequireLogin -> postMusicDelegate.onMusicServiceRequireLogin(
                    action.musicService, analyticsView,
                )

                is CameraOrganiseeAction.OnMusicVisibilitySelected -> {
                    viewModelScope.launch(dispatcherProvider.viewmodel) {
                        postMusicDelegate.onMusicVisibilitySelected(
                            newValue = action.musicVisibility,
                            analyticsView = analyticsView,
                        )
                    }
                }

                is CameraOrganiseeAction.OnPostVisibilityChanged -> viewModelScope.launch(
                    dispatcherProvider.viewmodel,
                ) {
                    postVisibilityDelegate.setPostVisibility(
                        action.postVisibility,
                        analyticsView,
                    )
                }

                CameraOrganiseeAction.OnChangePostVisibilityForbidden -> {
                    feedbackManager.displaySnack(
                        SnackBarDatas.TitleAndSubTitle(
                            icon = bereal.app.design.R.drawable.exclamation_mark,
                            iconTint = BeRealColors().error,
                            title = stringProvider[R.string.send_change_audience_unavailable],
                            subtitle = null,
                        ),
                    )
                }

                is CameraOrganiseeAction.TapToFocus -> {
                    tapToFocus(action.coordinateX, action.coordinateY)
                }

                CameraOrganiseeAction.ToggleMute -> sendBeRealDelegate.toggleVideoAudio()
            }
        }
    }

    private fun sendBeReal() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            val state = (uiState.value as? CameraOrganiseeUiState.CameraPermissionGranted)
            val sendContentState = state?.sendContentState
            val captureState = (captureState.value as? CameraOrganiseeCaptureState.Completed)

            if (state == null || sendContentState == null || captureState == null) {
                Timber.e("sendBeReal - state issue - expected CameraPermissionGranted")
                feedbackManager.displaySnack(stringProvider[R.string.general_error_something_went_wrong])
                return@launch
            }

            sendBeRealDelegate.sendBeReal(
                beRealTakenAt = beRealTakenAt.value,
                postMusic = postMusicDelegate.getCameraResult(),
                retakeCount = params.cameraTypeDelegate.getRetakeCount(),
                sendContentState = sendContentState,
                captureState = captureState,
                params = params.params,
                sendBeReal = { sendPostDomainModel, cameraParams ->
                    params.cameraTypeDelegate.sendBeReal(sendPostDomainModel, cameraParams)
                    if (showProfilePictureAfterBeRealCtaUseCase()) {
                        captureState.secondMedia?.toBeRealMedia()?.uri?.let { pictureUri ->
                            navigationManager.navigate(
                                destination = CameraProfilePictureDestination(
                                    pictureUri = pictureUri,
                                ),
                                popUp = MainScreenDirection.route,
                            )
                        }
                    }
                },
                cameraSessionIdentifier = cameraSessionIdentifier,
            )
        }
    }

    private fun resetMediasCaptured() {
        if (params.params?.musicEnabled == true) {
            postMusicDelegate.cleanForceCheckMusic()
            viewModelScope.launch(dispatcherProvider.viewmodel) {
                // allow catching a new music after each retake
                postMusicDelegate.catchUserCurrentMusic(CheckMusicAfter.Retake)
            }
        }
        captureState.value = CameraOrganiseeCaptureState.Idle(
            openScreenTime = beRealTimeProvider.currentTimeInstant,
        )
        beRealTakenAt.value = null
        takePictureDelegate.updateZoom(1f)
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            params.cameraTypeDelegate.trackCameraCancelCapture()
        }
    }

    private fun setUpDefaultSendOptions() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            captureState.filterIsInstance<CameraOrganiseeCaptureState.Completed>()
                .take(1)
                .collect {
                    if (params.params?.autoEnableLocation == true) {
                        postLocationDelegate.autoEnable(
                            onClickOpenSettings = ::openLocationSettings,
                        )
                    }
                    params.params?.audienceToSelect?.let {
                        postVisibilityDelegate.setPostVisibility(
                            it,
                            analyticsView,
                        )
                    }
                }
        }
    }

    fun pop(forceExit: Boolean = false) {
        if (captureState.value is CameraOrganiseeCaptureState.Completed && !forceExit) {
            resetMediasCaptured()
        } else {
            params.cameraTypeDelegate.navigateBack(params.params)
        }
    }

    // endregion

    suspend fun bindToCamera(appContext: Context, lifecycleOwner: LifecycleOwner) {
        try {
            val processCameraProvider = ProcessCameraProvider.awaitInstance(appContext)
            this.processCameraProvider = processCameraProvider
            val facing = takePictureDelegate.captureOptions.value.mandatoryOptions.facing

            camera = processCameraProvider.bindToLifecycle(
                lifecycleOwner = lifecycleOwner,
                cameraSelector = if (facing == Facing.Back) DEFAULT_BACK_CAMERA else DEFAULT_FRONT_CAMERA,
                cameraPreview,
                takePictureDelegate.cameraImage3By4Capture,
            )

            camera?.cameraInfo?.cameraState?.removeObservers(lifecycleOwner)
            camera?.cameraInfo?.cameraState?.observe(lifecycleOwner) { state ->
                state.error?.let { error ->
                    viewModelScope.launch {
                        cameraErrorDelegate.handleCriticalError(
                            processCameraProvider = processCameraProvider,
                            appContext = appContext,
                            lifecycleOwner = lifecycleOwner,
                            bindToCamera = { appContext, lifecycleOwner ->
                                val retry = retryBindToCameraCount.incrementAndGet()
                                if (retry >= MAX_CAMERA_RETRIES) {
                                    feedbackManager.displaySnack(
                                        cameraErrorDelegate.getErrorMessageFromCode(error),
                                    )
                                    cameraErrorDelegate.trackCameraError(
                                        "error in camera with code ${error.code}, type ${error.type} and message ${error.cause?.message}",
                                    )
                                    pop(forceExit = true)
                                } else {
                                    delay(retry * 200L)
                                    bindToCamera(
                                        appContext,
                                        lifecycleOwner,
                                    )
                                }
                            },
                        )
                    }
                    camera?.cameraInfo?.cameraState?.removeObservers(lifecycleOwner)
                }
            }

            val minZoom = camera?.cameraInfo?.zoomState?.value?.minZoomRatio ?: 1f
            val maxZoom = camera?.cameraInfo?.zoomState?.value?.maxZoomRatio ?: 1f

            takePictureDelegate.updateZoomRange(minZoom = minZoom, maxZoom = maxZoom)

            // when switching camera for regular BeReal
            takeSecondaryIfNeeded()

            try {
                awaitCancellation()
            } finally {
                processCameraProvider.unbindAll()
            }
        } catch (e: Exception) {
            if (e is CancellationException) {
                throw e
            } else {
                cameraErrorDelegate.trackCameraError(
                    e.message ?: "Unknown error while binding camera",
                )
            }
        }
    }

    @OptIn(ExperimentalCamera2Interop::class)
    private suspend fun takePrimary() {
        if (!canMyUserPost.value) {
            displayCantPostDialog()
            return
        } else {
            takePictureDelegate.takePrimaryPicture(
                openScreenTime = (captureState.value as? CameraOrganiseeCaptureState.Idle)?.openScreenTime,
                file = File(cameraCacheDir, params.cameraTypeDelegate.primaryFilename),
                camera = requireNotNull(camera) { "Camera cannot be null" },
                centeredFocusedPoint = centeredFocusedPoint,
                updateCaptureState = { captureState.value = it },
                incrementRetakeCount = params.cameraTypeDelegate::incrementRetakeCount,
                onTakePictureError = {
                    cameraErrorDelegate.handleRecoverableError(
                        error = it,
                        onErrorRetryClicked = ::resetMediasCaptured,
                        onErrorCancelClicked = { pop(forceExit = true) },
                    )
                },
            )
        }
    }

    @OptIn(ExperimentalCamera2Interop::class)
    private suspend fun takeSecondaryIfNeeded() {
        val state = captureState.value
        if (state is CameraOrganiseeCaptureState.FirstMediaCaptured) {
            takePictureDelegate.takeSecondaryPicture(
                state = state,
                file = File(cameraCacheDir, params.cameraTypeDelegate.secondaryFilename),
                camera = requireNotNull(camera) { "Camera cannot be null" },
                centeredFocusedPoint = centeredFocusedPoint,
                updateCaptureState = {
                    captureState.value = it
                },
                onTakePictureError = {
                    cameraErrorDelegate.handleRecoverableError(
                        error = it,
                        onErrorRetryClicked = ::resetMediasCaptured,
                        onErrorCancelClicked = { pop(forceExit = true) },
                    )
                },
                freezePreview = {
                    viewModelScope.launch {
                        processCameraProvider?.unbind(cameraPreview)
                    }
                },
            )
        }
    }

    private fun openLocationSettings() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            _events.emit(CameraOrganiseeEvent.OpenLocationSettings)
        }
    }

    private fun tapToFocus(coordinateX: Float, coordinateY: Float) {
        surfaceMeteringPointFactory
            ?.createPoint(coordinateX, coordinateY)
            ?.let {
                camera?.cameraControl?.startFocusAndMetering(
                    FocusMeteringAction.Builder(it, FocusMeteringAction.FLAG_AE).build(),
                )
            }
    }

    fun onCountDownFinished() {
        countDownFinished.value = true
    }

    private fun trackInitCameraTime(initialLoadingTime: Long) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            params.params?.let {
                analyticsUseCase(
                    CameraEventAnalyticsDomainModel.ViewedCamera(
                        origin = it.origin,
                        tapFrom = it.tapFrom,
                        loadedTimeInSeconds = (System.currentTimeMillis() - initialLoadingTime) / 1000f,
                        momentId = getCurrentMomentUseCase()?.id,
                        hasBtsEnabled = false,
                    ),
                )
            }
            initCameraTracking.value = Pair(true, initialLoadingTime)
        }
    }

    private fun displayCantPostDialog() {
        dialogDisplayer.display(
            Dialog.Alert(
                title = stringProvider[BeRealStrings.bottom_bar_camera_icon_disabled_dialog_title],
                description = stringProvider[BeRealStrings.bottom_bar_camera_icon_disabled_dialog_description],
                confirm = Confirm(
                    customText = stringProvider[BeRealStrings.general_ok],
                    isCritical = false,
                    onConfirm = { pop(forceExit = true) },
                ),
                cancel = null,
            ),
        )
    }

    override fun onCleared() {
        // clear all files with the specific identifier that were created during this session but not sent
        // only used for cameraRoll video for now
        if (!sendBeRealDelegate.hasSentBeReal.value) {
            fileStore.clearAllFilesWithIdentifier(
                identifier = cameraSessionIdentifier,
                cacheDestinationFolder = FileCacheDir.BeRealMyUserLocalVideo.path,
            )
        }
        super.onCleared()
    }

    companion object {
        private const val MAX_CAMERA_RETRIES = 5
        private val MAX_FILES_AGE_KEPT_IN_CAMERA_CACHE = 3.days
    }
}
