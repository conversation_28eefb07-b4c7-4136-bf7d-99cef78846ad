package bereal.app.care.faq.article.ui.view

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import bereal.app.care.faq.articles.ui.model.FaqIncidentArticleUiModel
import bereal.app.design.settings.SettingsInformationRow
import bereal.app.design.theme.BeRealTheme

@Composable
internal fun IncidentArticleHeader(
    modifier: Modifier = Modifier,
    incidentArticle: FaqIncidentArticleUiModel,
    onIncidentBannerClicked: () -> Unit,
) {
    SettingsInformationRow(
        modifier = modifier,
        text = incidentArticle.faqArticle.title,
        description = incidentArticle.faqArticle.description,
        icon = if (incidentArticle.ongoing) {
            bereal.app.design.R.drawable.exclamation_mark
        } else {
            bereal.app.design.R.drawable.check_mark
        },
        onClick = onIncidentBannerClicked,
        color = if (incidentArticle.ongoing) BeRealTheme.colors.error else BeRealTheme.colors.palette.green,
    )
}
