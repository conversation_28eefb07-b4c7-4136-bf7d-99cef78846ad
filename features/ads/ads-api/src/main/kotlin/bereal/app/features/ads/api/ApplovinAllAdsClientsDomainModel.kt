package bereal.app.features.ads.api

import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.api.model.AdTarget
import bereal.app.features.ads.api.model.BackoffConfig
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.supervisorScope

// TODO on another MR : move to another module
class ApplovinAllAdsClientsDomainModel(
    val allClients: List<AdClient>,
    private val backoffConfig: BackoffConfig? = null,
    private val onTriggerAd: (ad: Ad, adClient: AdClient) -> Unit = { _, _ -> },
) {

    fun getClientsForTarget(screenTarget: AdTarget): ApplovinAdsSubsetOfClientsDomainModel {
        return ApplovinAdsSubsetOfClientsDomainModel(
            clients = allClients.filter { client ->
                val clientTarget = client.config.feedTarget
                when {
                    // Client can display everywhere
                    clientTarget == AdTarget.ALL -> true
                    // the screen accept all ads
                    screenTarget == AdTarget.ALL -> true
                    // specifc ad for specific screen
                    else -> clientTarget == screenTarget
                }
            },
            backoffConfig = backoffConfig,
            onTriggerAd = onTriggerAd,
        )
    }

    suspend fun fetchAdIfNecessary(
        localExtrasProvider: () -> List<Pair<String, Any>>,
    ) {
        fetchAdIfNecessaryInternal(
            clients = allClients,
            backoffConfig = backoffConfig,
            localExtrasProvider = localExtrasProvider,
        )
    }
}

internal suspend fun fetchAdIfNecessaryInternal(
    clients: List<AdClient>,
    backoffConfig: BackoffConfig?,
    localExtrasProvider: () -> List<Pair<String, Any>>,
) {
    supervisorScope {
        val localExtras = lazy(LazyThreadSafetyMode.PUBLICATION) { localExtrasProvider() }

        clients
            .map { client ->
                // ask 1 ad per client
                async {
                    client.fetchAdIfNecessary(
                        localExtrasProvider = localExtras,
                        backoffConfig = backoffConfig,
                    )
                }
            }
            .awaitAll()
    }
}
