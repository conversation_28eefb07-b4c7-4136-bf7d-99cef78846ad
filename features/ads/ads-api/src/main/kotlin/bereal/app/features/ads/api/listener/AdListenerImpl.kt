package bereal.app.features.ads.api.listener

import java.util.concurrent.CopyOnWriteArraySet

class AdListenerImpl : AdListeners {
    private val adLoadingListeners = CopyOnWriteArraySet<AdLoadingListener>()
    private val adModerationListeners = CopyOnWriteArraySet<AdModerationListener>()
    private val adRevenueListeners = CopyOnWriteArraySet<AdRevenueListener>()
    private val adClickListeners = CopyOnWriteArraySet<AdClickListener>()

    override fun addAdLoadingListener(listener: AdLoadingListener) {
        adLoadingListeners.add(listener)
    }

    override fun removeAdLoadingListener(listener: AdLoadingListener) {
        adLoadingListeners.remove(listener)
    }

    override fun addAdModerationListener(listener: AdModerationListener) {
        adModerationListeners.add(listener)
    }

    override fun removeAdModerationListener(listener: AdModerationListener) {
        adModerationListeners.remove(listener)
    }

    override fun addAdRevenueListener(listener: AdRevenueListener) {
        adRevenueListeners.add(listener)
    }

    override fun removeAdRevenueListener(listener: AdRevenueListener) {
        adRevenueListeners.remove(listener)
    }

    override fun addAdClickListener(listener: AdClickListener) {
        adClickListeners.add(listener)
    }

    override fun removeAdClickListener(listener: AdClickListener) {
        adClickListeners.remove(listener)
    }

    override fun runLoadingListeners(body: (AdLoadingListener) -> Unit) {
        adLoadingListeners.forEach(body)
    }

    override fun runModerationListener(body: (AdModerationListener) -> Unit) {
        adModerationListeners.forEach(body)
    }

    override fun runRevenueListener(body: (AdRevenueListener) -> Unit) {
        adRevenueListeners.forEach(body)
    }

    override fun runClickListener(body: (AdClickListener) -> Unit) {
        adClickListeners.forEach(body)
    }
}
