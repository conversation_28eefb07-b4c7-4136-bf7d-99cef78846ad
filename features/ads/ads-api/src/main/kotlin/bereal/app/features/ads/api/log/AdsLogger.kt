package bereal.app.features.ads.api.log

import bereal.app.features.ads.api.AdClient
import timber.log.Timber

object AdsLogger {

    private val TAG = "ADS_LOGGER"
    private const val ENABLED = false

    fun info(message: String) {
        if (ENABLED) {
            Timber.tag(TAG).i(message)
        }
    }
    fun info(client: AdClient, message: String) {
        if (ENABLED) {
            Timber.tag(TAG).i("[${client.clientDisplayName}] $message")
        }
    }
    fun debug(client: AdClient, message: String) {
        if (ENABLED) {
            Timber.tag(TAG).d("[${client.clientDisplayName}] $message")
        }
    }
    fun error(client: AdClient, message: String, error: Throwable? = null) {
        if (ENABLED) {
            val messageLog = buildString {
                append(message)
                error?.message?.let {
                    append(" $it")
                }
            }
            Timber.tag(TAG).e("[${client.clientDisplayName}] $messageLog")
        }
    }
}
