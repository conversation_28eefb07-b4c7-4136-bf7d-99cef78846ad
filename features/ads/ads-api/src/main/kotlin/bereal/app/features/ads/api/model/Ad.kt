package bereal.app.features.ads.api.model

import android.content.Context
import android.view.View
import androidx.annotation.MainThread
import bereal.app.features.ads.api.AdClient
import java.lang.ref.WeakReference
import java.util.Date

abstract class Ad {

    private data class IsDisplayingOnClient(
        val requestId: AdRequestId,
        val onClient: WeakReference<AdClient>,
        val isDisplaying: Boolean,
    )

    abstract val longLifeAds: Boolean
    abstract val id: Id
    abstract val type: Type
    abstract val info: Info
    abstract val loadedAt: Date
    abstract val moderationResult: ModerationResult?

    val isBlocked: Boolean get() = moderationResult == ModerationResult.BLOCKED
    abstract val isExpired: Boolean

    var rendered: Boolean = false
        private set

    var isRevenuePaid: Boolean = false
        private set

    private var isDisplaying: IsDisplayingOnClient? = null

    val isDisplayingForRequestId: String?
        get() = isDisplaying?.requestId?.id

    fun isCurrentlyRendering(): Boolean {
        return if (!longLifeAds) {
            isDisplaying == null
        } else {
            isDisplaying?.isDisplaying == true
        }
    }

    fun startToDisplay(
        client: AdClient,
        requestId: AdRequestId,
    ) {
        isDisplaying = IsDisplayingOnClient(
            onClient = WeakReference(client),
            requestId = requestId,
            isDisplaying = true,
        )
    }

    @MainThread
    fun endToDisplay() {
        releaseAdView()
        if (!longLifeAds) {
            isDisplaying = null
        } else {
            isDisplaying = isDisplaying?.copy(
                isDisplaying = false,
            )
        }
    }

    fun isAssignedToRequest(requestId: AdRequestId): Boolean {
        return getRequestId() == requestId
    }

    fun getRequestId(): AdRequestId? {
        return isDisplaying?.requestId
    }

    open fun canBeServed() = !isBlocked && !isExpired

    open fun isAvailableToBeInserted(): Boolean {
        return if (longLifeAds) {
            canBeServed() && isDisplaying == null /* here we just want to know if it never has been displayed at a position */
        } else {
            canBeServed() && !isCurrentlyRendering() && !this.isRevenuePaid
        }
    }

    fun isAlreadyDisplayedAndPaid(): Boolean {
        return isRevenuePaid && isDisplaying != null
    }

    abstract fun getView(context: Context, displayedInList: Boolean): View

    fun markAsRendered() {
        rendered = true
    }

    fun markAsRevenuePaid() {
        isRevenuePaid = true
    }

    @MainThread
    protected abstract fun releaseAdView()

    // Inner class

    data class Info(
        val adUnit: String,
        val network: String,
        val revenue: Double,
        val revenuePrecision: String,
        val cohortId: String?,
        val creativeId: String?,
        val creativeType: String?,
        val campaignId: String?,
        val placement: String?,
        val reviewCreativeId: String?,
        val formatLabel: String?,
        val requestLatencyMillis: Long,
        val networkPlacement: String?,
        val adProvider: String?,
    )

    enum class ModerationResult(val analyticsValue: String) {
        UNKNOWN("unknown"),
        VERIFIED("verified"),
        BLOCKED("blocked"),
        REPORTED("reported"),
    }

    @JvmInline
    value class Id(val id: String)

    enum class Type {
        NATIVE, MREC
    }
}
