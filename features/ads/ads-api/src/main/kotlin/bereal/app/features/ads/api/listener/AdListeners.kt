package bereal.app.features.ads.api.listener

interface AdListeners {

    fun addAdLoadingListener(listener: AdLoadingListener)
    fun removeAdLoadingListener(listener: AdLoadingListener)
    fun runLoadingListeners(body: (AdLoadingListener) -> Unit)

    fun addAdModerationListener(listener: AdModerationListener)
    fun removeAdModerationListener(listener: AdModerationListener)
    fun runModerationListener(body: (AdModerationListener) -> Unit)

    fun addAdRevenueListener(listener: AdRevenueListener)
    fun removeAdRevenueListener(listener: AdRevenueListener)
    fun runRevenueListener(body: (AdRevenueListener) -> Unit)

    fun addAdClickListener(listener: AdClickListener)
    fun removeAdClickListener(listener: AdClickListener)
    fun runClickListener(body: (AdClickListener) -> Unit)

    // fun addOnAvailableAdCountChangedListener(listener: OnAvailableAdCountChangedListener)
    // fun removeOnAvailableAdCountChangedListener(listener: OnAvailableAdCountChangedListener)
    // fun runOnAdAvailableAdCountChangedListeners(body: (OnAvailableAdCountChangedListener) -> Unit)
}
