package bereal.app.features.ads.api

import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.api.model.AdAndClient
import bereal.app.features.ads.api.model.AdsAndClient
import bereal.app.features.ads.api.model.BackoffConfig

data class ApplovinAdsSubsetOfClientsDomainModel(
    val clients: List<AdClient>,
    private val backoffConfig: BackoffConfig? = null,
    val onTriggerAd: (ad: Ad, adClient: AdClient) -> Unit = { _, _ -> },
) {

    @AnyThread
    suspend fun adsReadyToBeInserted(): List<AdsAndClient> {
        return clients.mapNotNull { client ->
            client.getAdsReadyToBeInserted()
                .takeIf { it.isNotEmpty() }
                ?.let { ads ->
                    AdsAndClient(
                        ads = ads,
                        client = client,
                    )
                }
        }
    }

    @MainThread
    fun firstAdReadyToBeInserted(): AdAndClient? {
        return clients.firstNotNullOfOrNull { client ->
            client.getAnyAdReadyToBeInserted()?.let { ad ->
                return AdAndClient(
                    client = client,
                    ad = ad,
                )
            }
        }
    }

    suspend fun fetchAdIfNecessary(
        localExtrasProvider: () -> List<Pair<String, Any>>,
    ) {
        fetchAdIfNecessaryInternal(
            clients = clients,
            backoffConfig = backoffConfig,
            localExtrasProvider = localExtrasProvider,
        )
    }
}
