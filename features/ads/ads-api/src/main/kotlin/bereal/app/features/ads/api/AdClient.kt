package bereal.app.features.ads.api

import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import bereal.app.features.ads.api.listener.AdListeners
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.api.model.AdRequestId
import bereal.app.features.ads.api.model.AdTarget
import bereal.app.features.ads.api.model.FetchFailInfo

interface AdClient {

    // used for loggin purposes
    val clientDisplayName: String

    val adType: Ad.Type
    val config: Config
    val listeners: AdListeners

    // if last fetch ad was a failure, this will be filled
    var lastFailInfo: FetchFailInfo?

    // destroy the client, cannot be used anymore then
    @MainThread
    fun destroyClient()

    /**
     * @return the ad previously served via [getAdToDisplay] with the same [requestId], or null if:
     * - no previous call found for this [requestId]
     * - the ad is not in memory anymore
     * - the ad is used by another component
     * - the ad can't be served anymore (eg: blocked)
     */
    @MainThread
    fun getAdFromRequestId(requestId: AdRequestId): Ad?

    suspend fun hasAdReadyToBeInserted(): Boolean

    /**
     * @return any ad ready to be displayed in the feed
     * not blocked / not currently displayed
     */
    @MainThread
    fun getAnyAdReadyToBeInserted(): Ad?

    @AnyThread
    suspend fun getAdsReadyToBeInserted(): List<Ad>

    @MainThread
    fun getAdsAlreadyDisplayedAndPaid(): List<Ad>

    /**
     * @return an ad for the given requestId
     * it tries to return the same ad we already displayed for this requestId it it was already the case
     * otherwise it returns an available ad, if it matches the revenueThreshold
     */
    @MainThread
    fun getAdToDisplay(requestId: AdRequestId, revenueThreshold: Double = 0.0): Ad?

    /**
     * call once the UI component is removed from the hierarchy (composable/view from window)
     */
    @MainThread
    fun releaseAdView(ad: Ad)

    @MainThread
    fun destroyAds(adsToDestroy: List<Ad>)

    /**
     * Load a new ad
     *
     * Note: there's no limit to the number of ads that can be loaded,
     * if you call this 10 times, it'll load 10 different ads.
     */
    @Throws(Exception::class)
    suspend fun fetchAd(localExtras: List<Pair<String, Any>>): Ad

    data class Config(
        /**
         * Controls how many ads will be kept in memory to re-display/be re-used to fetch new ads.
         * If there's enough ads in memory, the most ancien one will be recycled for the next fetch call.
         */
        // @IntRange(from = 1) val adCacheSize: Int,
        val adUnit: String,
        val placement: String? = null,
        val feedTarget: AdTarget,
    )

    fun getRequestIdFromAd(adId: Ad.Id): String?
}
