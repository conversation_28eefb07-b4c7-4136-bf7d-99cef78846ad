package bereal.app.features.ads.api

import bereal.app.features.ads.api.log.AdsLogger
import bereal.app.features.ads.api.model.AdAlreadyLoadingException
import bereal.app.features.ads.api.model.BackoffConfig
import bereal.app.features.ads.api.model.FetchAdResult
import bereal.app.features.ads.api.model.FetchFailInfo

internal suspend fun AdClient.fetchAdIfNecessary(
    localExtrasProvider: Lazy<List<Pair<String, Any>>>,
    backoffConfig: BackoffConfig?,
): FetchAdResult {
    val client = this

    AdsLogger.info(
        client = client,
        "fetchAdIfNecessary - start",
    )

    // 1 if the client has an add not server return it
    if (client.getAdsReadyToBeInserted().isNotEmpty()) {
        AdsLogger.debug(
            client = client,
            "fetchAdIfNecessary - hasAdReadyToBeInserted",
        )
        return FetchAdResult.Success
    }

    // 2 otherwise try to fetch an ad in this client

    // Backoff
    val lastFailInfo = client.lastFailInfo
    if (backoffConfig != null && lastFailInfo != null) {
        val delayForNextAttemptMillis =
            backoffConfig.getDelay(lastFailInfo.attempt)
                ?.inWholeMilliseconds
                ?: 0
        val timeSinceLastAttempt = System.currentTimeMillis() - lastFailInfo.timestampMillis
        if (timeSinceLastAttempt < delayForNextAttemptMillis) {
            AdsLogger.error(
                client = client,
                "fetchAdIfNecessary - wait/BetweenTwoFails",
            )
            return FetchAdResult.Failure(
                FetchAdResult.Failure.Cause.BetweenTwoFails,
            )
        }
    }

    return try {
        AdsLogger.debug(
            client = client,
            "fetchAdIfNecessary --- Loading",
        )

        client.fetchAd(localExtrasProvider.value)

        // here it's a success, otherwise it throws an exception
        client.lastFailInfo = null

        AdsLogger.debug(
            client = client,
            "fetchAdIfNecessary --- Success",
        )

        FetchAdResult.Success
    } catch (e: AdAlreadyLoadingException) {
        AdsLogger.error(
            client = client,
            "fetchAdIfNecessary --- AlreadyLoading",
        )

        FetchAdResult.Failure(
            FetchAdResult.Failure.Cause.AlreadyLoading,
        )
    } catch (t: Throwable) {
        AdsLogger.error(
            client = client,
            "fetchAdIfNecessary --- Error during fetch",
            error = t,
        )

        client.lastFailInfo = FetchFailInfo(
            attempt = lastFailInfo?.attempt?.plus(1) ?: 0,
            timestampMillis = System.currentTimeMillis(),
        )
        FetchAdResult.Failure(
            FetchAdResult.Failure.Cause.LoadingFailed,
        )
    }
}
