plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.ui.get().pluginId)
    alias(libs.plugins.ksp)
    id(libs.plugins.bereal.koin.base.get().pluginId)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "bereal.app.features.ads.ui"
}

dependencies {
    implementation(libs.androidx.compose.material.navigation)
    implementation(libs.androidx.navigation.compose)
    implementation(libs.bundles.koin)
    implementation(libs.bundles.kotlinSerialization)
    implementation(libs.jetbrain.markdown)

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.common)
    implementation(projects.platform.commonAndroid)
    implementation(projects.platform.entities)
    implementation(projects.platform.translations)

    implementation(projects.features.ads.domain)
    implementation(projects.features.myUser.domain)

    implementation(projects.features.ads.adsApi)
    implementation(projects.features.ads.adsApplovin)
    implementation(projects.features.ads.privacy)

    ksp(libs.koin.ksp.compiler)
}
