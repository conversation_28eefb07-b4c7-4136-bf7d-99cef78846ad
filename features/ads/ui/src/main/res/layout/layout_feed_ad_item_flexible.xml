<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#000000">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottom_layout"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/media_view_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_gravity="center"
            android:gravity="center"
            tools:foreground="#805B5B" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/bottom_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#222222"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="12dp"
        android:paddingBottom="20dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/icon_image_view"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                app:layout_constraintBottom_toBottomOf="@id/lbl_subtitle"
                app:layout_constraintStart_toStartOf="parent"
                app:shapeAppearanceOverlay="@style/circleImageView"
                tools:background="#AA0000" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/title_text_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_bold"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="@id/ad_options_view"
                    app:layout_constraintStart_toEndOf="@id/icon_image_view"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Title" />

                <TextView
                    android:id="@+id/lbl_subtitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:elegantTextHeight="false"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_regular"
                    android:includeFontPadding="false"
                    android:maxLines="1"
                    android:text="@string/general_sponsored"
                    android:textColor="#FFFFFF"
                    android:alpha="0.8"
                    android:textSize="14sp"
                    app:layout_constraintEnd_toEndOf="@id/title_text_view"
                    app:layout_constraintStart_toStartOf="@id/title_text_view"
                    app:layout_constraintTop_toBottomOf="@id/title_text_view" />
            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/body_text_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/inter_bold"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Taxis are more ways to travel at the top of an app. Download and book your trip today." />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cta_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:paddingHorizontal="16dp"
            tools:text="Download now"
            app:backgroundTint="#FFFFFF"
            app:cornerRadius="16dp"
            app:iconPadding="8dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            app:icon="@drawable/chevron_right_native"
            app:iconGravity="textEnd"
            app:iconTint="#000000"
            android:textColor="#000000"
            android:fontFamily="@font/inter_bold"
            android:textSize="13sp"
            />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/ad_options_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:gravity="center"
        app:layout_constraintWidth_min="20dp"
        app:layout_constraintHeight_min="20dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
