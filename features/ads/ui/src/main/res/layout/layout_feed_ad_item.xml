<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="#000000">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/icon_image_view"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="8dp"
        tools:background="#AA0000"
        app:shapeAppearanceOverlay="@style/circleImageView"
        app:layout_constraintBottom_toBottomOf="@id/lbl_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/title_text_view" />

    <TextView
        android:id="@+id/title_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:fontFamily="@font/inter_bold"
        android:textColor="#FFFFFF"
        android:includeFontPadding="false"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="@id/ad_options_view"
        app:layout_constraintStart_toEndOf="@id/icon_image_view"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Title" />

    <TextView
        android:id="@+id/lbl_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:elegantTextHeight="false"
        android:fontFamily="@font/inter_regular"
        android:text="@string/general_sponsored"
        android:textColor="#99999C"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="@id/title_text_view"
        app:layout_constraintStart_toStartOf="@id/title_text_view"
        app:layout_constraintTop_toBottomOf="@id/title_text_view" />

    <LinearLayout
        android:id="@+id/ad_options_view"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_marginHorizontal="16dp"
        android:orientation="horizontal"
        android:background="@drawable/ads_options_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:gravity="center"
        app:layout_constraintBottom_toTopOf="@+id/layout_content"
        />

    <bereal.app.features.ads.ui.natives.view.MaxNativeAdViewContainer
        android:id="@+id/layout_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/lbl_subtitle">

        <TextView
            android:visibility="gone"
            android:id="@+id/advertiser_textView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:textColor="#CCC2DC"
            android:textSize="11sp"
            tools:text="Sponsored by AppLovin" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <FrameLayout
                android:id="@+id/media_view_container"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="#121214"
                app:layout_constraintDimensionRatio="3:4"
                app:layout_constraintTop_toTopOf="parent"
                tools:foreground="#805B5B" />

            <FrameLayout
                android:id="@+id/media_front_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:visibility="gone"
                app:layout_constraintHeight_percent="0.35"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.35"
                tools:background="#FFFF" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/cta_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="24dp"
            android:paddingTop="0dp"
            android:paddingEnd="24dp"
            android:paddingBottom="0dp"
            android:background="@color/white"
            android:fontFamily="@font/inter_bold"
            android:textColor="@android:color/black"
            android:textSize="13sp"
            android:gravity="center"
            android:drawableEnd="@drawable/chevron_right_native"
            android:drawablePadding="4dp"
            android:paddingHorizontal="16dp"
            android:textAlignment="viewStart"
            android:textAllCaps="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/media_view_container"
            tools:text="Watch it" />
    </bereal.app.features.ads.ui.natives.view.MaxNativeAdViewContainer>

    <FrameLayout
        android:id="@+id/star_rating_view"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_marginTop="4dp"
        android:visibility="gone"
        app:layout_goneMarginTop="0dp"
        app:layout_constraintTop_toBottomOf="@+id/layout_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="@+id/icon_image_view"
        />


    <TextView
        android:id="@+id/body_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="8dp"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/star_rating_view"
        tools:text="Body" />

</androidx.constraintlayout.widget.ConstraintLayout>
