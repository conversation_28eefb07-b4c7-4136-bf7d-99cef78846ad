package bereal.app.features.ads.ui.natives.ratio

enum class NativeAdNetworkDefaultRatio(
    val networkName: String,
    val defaultAspectRatio: String,
) {
    DEFAULT("", "4:3"),
    APPLOVIN("APPLOVIN_EXCHANGE", "4:3"),
    GOOGLE_AD_MOB("Google AdMob", "16:9"),
    LIFTOFF("Liftoff Monetize", "16:9"),
    BIGO("BigoAds", "1.91:1"),
    PANGLE("Pangle Native", "1.91:1"),
    INMOBI("InMobi", "16:9"),
    BIDMACHINE("Bidmachine", "4:3"),
    LINE("Line", "4:3"), ;

    companion object {
        fun getDefaultRatioFromNetworkName(networkName: String): String {
            return entries.firstOrNull { it.networkName == networkName }?.defaultAspectRatio
                ?: DEFAULT.defaultAspectRatio
        }

        fun needToHideBody(networkName: String): Bo<PERSON>an {
            return (networkName == GOOGLE_AD_MOB.networkName) || (networkName == APPLOVIN.networkName)
        }
    }
}
