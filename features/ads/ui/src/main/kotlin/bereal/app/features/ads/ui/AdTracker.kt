package bereal.app.features.ads.ui

import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.ext.toParam
import bereal.app.analytics.ext.toParamOrNull
import bereal.app.analytics.model.AnalyticsParam
import bereal.app.features.ads.api.AdClient
import bereal.app.features.ads.api.AdDisplayPosition
import bereal.app.features.ads.api.listener.AdLoadingListener
import bereal.app.features.ads.api.listener.AdModerationListener
import bereal.app.features.ads.api.listener.AdRevenueListener
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.adn.AdnAd
import bereal.app.features.ads.applovin.exception.MaxAdLoadException
import bereal.app.features.ads.applovin.util.buildInfo
import bereal.app.features.ads.applovin.util.id
import bereal.app.features.ads.domain.AdsLogsRepository
import bereal.app.features.ads.domain.analytics.AdsEvents
import bereal.app.features.ads.domain.usecases.BroadcastRevenuePaidEventUseCase
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdFormat
import io.adn.sdk.publisher.AdnAdError
import org.koin.core.annotation.Factory
import timber.log.Timber

@Factory
class AdTracker(
    private val broadcastRevenuePaidEventUseCase: BroadcastRevenuePaidEventUseCase,
    private val adsLogsRepository: AdsLogsRepository,
    private val analyticsManager: AnalyticsManager,
) : AdLoadingListener, AdRevenueListener, AdModerationListener {

    companion object {
        private const val TAG = "ADS_TRACKER"

        const val ANALYTICS_PARAM_AD_NETWORK = "adNetwork"
        const val ANALYTICS_PARAM_AD_PROVIDER = "adProvider"

        fun analyticsParamsOfAd(
            adType: String?,
            adNetwork: String?,
            adProvider: String?,
            revenueUSD: Double?,
            mediaType: String?,
            adCampaignId: String?,
            adCreativeId: String?,
            adDisplayPosition: Int?,
        ): List<AnalyticsParam> = listOfNotNull(
            "adType" toParamOrNull adType,
            ANALYTICS_PARAM_AD_NETWORK toParamOrNull adNetwork,
            ANALYTICS_PARAM_AD_PROVIDER toParamOrNull adProvider,
            "revenue" toParamOrNull revenueUSD,
            "revenueUSD" toParamOrNull revenueUSD,
            "adCampaignId" toParamOrNull adCampaignId,
            "adCreativeId" toParamOrNull adCreativeId,
            "mediaType" toParamOrNull mediaType,
            "revenueType" toParam "ads",
            "currency" toParamOrNull "USD",
            "adDisplayPosition" toParamOrNull adDisplayPosition,
        )
    }

    private val Ad.Type.analyticsName: String
        get() {
            return when (this) {
                Ad.Type.NATIVE -> "NATIVE"
                Ad.Type.MREC -> "MREC"
            }
        }

    override fun onAdLoadingStarted(adClient: AdClient) {
        log("LOADING_STARTED", adClient.config.adUnit)
        analyticsManager.logEvent(
            AdsEvents.StartAdLoading(
                analyticsParamsOfAd(
                    ad = null,
                    adClient = adClient,
                ),
            ),
        )

        handleOnAdLoadingStartedLog(logText = "${adClient.adType.analyticsName} ${adClient.config.adUnit}")
    }

    fun onAdnAdLoadingStarted(adNetwork: String) {
        log("LOADING_STARTED", adNetwork)
        analyticsManager.logEvent(
            AdsEvents.StartAdLoading(
                listOfNotNull(
                    ANALYTICS_PARAM_AD_NETWORK toParamOrNull adNetwork,
                    ANALYTICS_PARAM_AD_PROVIDER toParamOrNull adNetwork,
                ),
            ),
        )

        handleOnAdLoadingStartedLog(logText = adNetwork)
    }

    private fun handleOnAdLoadingStartedLog(
        logText: String,
    ) {
        adsLogsRepository.addLog(
            AdsLogsRepository.Log(
                type = AdsLogsRepository.Log.Type.Info,
                text = "Loading Ad $logText",
                createdAt = System.currentTimeMillis(),
            ),
        )
        adsLogsRepository.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.FirstLoadStart,
                at = System.currentTimeMillis(),
            ),
        )
    }

    override fun onAdLoadingFailed(adClient: AdClient, exception: Exception) {
        log("LOADING_FAILED", adClient.config.adUnit)
        analyticsManager.logEvent(
            AdsEvents.FailToLoadAd(
                analyticsParamsOfAd(
                    ad = null,
                    adClient = adClient,
                ),
            ),
        )
        val waterfall = (exception as? MaxAdLoadException)?.error?.waterfall?.toString()
        Timber.e("$TAG waterfall: $waterfall")
        handleAdLoadingFailedLog(
            waterfall = waterfall,
            logErrorMessage = "${exception.message} ${adClient.config.adUnit}",
        )
    }

    fun onAdnAdLoadFailed(
        error: AdnAdError,
        adNetwork: String,
    ) {
        log("LOADING_FAILED", adNetwork)
        analyticsManager.logEvent(
            AdsEvents.FailToLoadAd(
                listOfNotNull(
                    ANALYTICS_PARAM_AD_NETWORK toParamOrNull adNetwork,
                    ANALYTICS_PARAM_AD_PROVIDER toParamOrNull adNetwork,
                ),
            ),
        )

        handleAdLoadingFailedLog(
            waterfall = null,
            logErrorMessage = "$adNetwork | ${error.errorCode} - ${error.errorMessage}",
        )
    }

    private fun handleAdLoadingFailedLog(
        waterfall: String?,
        logErrorMessage: String,
    ) {
        adsLogsRepository.addLog(
            AdsLogsRepository.Log(
                type = AdsLogsRepository.Log.Type.Error,
                text = logErrorMessage,
                waterfall = waterfall,
                createdAt = System.currentTimeMillis(),
            ),
        )
        adsLogsRepository.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.FirstLoadStart,
                at = System.currentTimeMillis(),
            ),
        )
    }

    override fun onAdLoadingFinished(adClient: AdClient, ad: Ad) {
        log("LOADING_FINISHED", adClient.config.adUnit)
        analyticsManager.logEvent(
            AdsEvents.FinishAdLoading(
                analyticsParamsOfAd(
                    ad = ad,
                    adClient = adClient,
                ),
            ),
        )
        handleOnAdLoadingFinished(
            adClient = adClient,
            ad = ad,
        )
    }

    fun onAdnAdLoadingFinished(adnAd: AdnAd) {
        log("LOADING_FINISHED", adnAd.info.network)
        handleOnAdLoadingFinished(
            adClient = null,
            ad = adnAd,
        )
    }

    private fun handleOnAdLoadingFinished(adClient: AdClient?, ad: Ad) {
        analyticsManager.logEvent(
            AdsEvents.FinishAdLoading(
                analyticsParamsOfAd(
                    ad = ad,
                    adClient = adClient,
                ),
            ),
        )
        adsLogsRepository.addLog(
            AdsLogsRepository.Log(
                type = AdsLogsRepository.Log.Type.Success,
                text = "onAdLoadingFinished ${ad.info.adUnit} ${ad.info.network}",
                createdAt = System.currentTimeMillis(),
            ),
        )
        adsLogsRepository.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.FirstLoadEnd,
                at = System.currentTimeMillis(),
            ),
        )
    }

    override fun onAdBlocked(adClient: AdClient, ad: Ad) {
        log("AD_BLOCKED", adClient.config.adUnit)
        if (ad.rendered) {
            analyticsManager.logEvent(
                AdsEvents.BlockAdOnDisplay(
                    analyticsParamsOfAd(
                        ad = ad,
                        adClient = adClient,
                    ),
                ),
            )
        } else {
            analyticsManager.logEvent(
                AdsEvents.BlockAdOnLoad(
                    analyticsParamsOfAd(
                        ad = ad,
                        adClient = adClient,
                    ),
                ),
            )
        }
        adsLogsRepository.addLog(
            AdsLogsRepository.Log(
                type = AdsLogsRepository.Log.Type.Error,
                text = "onAdBlocked ${ad.info.adUnit} ${ad.info.network}",
                createdAt = System.currentTimeMillis(),
            ),
        )
        adsLogsRepository.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.FirstLoadEnd,
                at = System.currentTimeMillis(),
            ),
        )
    }

    override fun onAdRevenuePaid(adClient: AdClient, ad: Ad) {
        log("WATCHED", adClient.config.adUnit)
        handleAdImpression(adClient, ad)
    }

    fun onAdnAdImpression(adnAd: AdnAd) {
        log("WATCHED", adnAd.info.network)
        handleAdImpression(
            adClient = null,
            ad = adnAd,
        )
    }

    private fun handleAdImpression(adClient: AdClient?, ad: Ad) {
        analyticsManager.logEvent(
            AdsEvents.WatchAd(
                analyticsParamsOfAd(
                    ad = ad,
                    adClient = adClient,
                ),
            ),
        )
        broadcastRevenuePaidEventUseCase(
            ad.info.revenue,
            ad.info.network,
            ad.info.adUnit,
            ad.info.placement,
        )
        adsLogsRepository.addLog(
            AdsLogsRepository.Log(
                type = AdsLogsRepository.Log.Type.Info,
                text = "onAdRevenuePaid ${ad.info.adUnit} ${ad.info.network}",
                createdAt = System.currentTimeMillis(),
            ),
        )
        adsLogsRepository.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.FirstAdViewed,
                at = System.currentTimeMillis(),
            ),
        )
    }

    val MaxAd.type: Ad.Type?
        get() = when (this.format) {
            MaxAdFormat.MREC -> Ad.Type.MREC
            MaxAdFormat.NATIVE -> Ad.Type.NATIVE
            else -> null // TODO add when we have others
        }

    private data class ClickedElement(
        val at: Long,
        val adUnitId: String,
    )

    private var lastClicked: ClickedElement? = null

    private fun debounceClick(ad: MaxAd, action: () -> Unit) {
        val newClick = ClickedElement(
            at = System.currentTimeMillis(),
            adUnitId = ad.adUnitId,
        )
        val lastOne = lastClicked
        val allowed = when {
            lastOne == null -> true
            lastOne.adUnitId != newClick.adUnitId -> true
            (newClick.at - lastOne.at) > 200 -> true
            else -> false
        }

        if (allowed) {
            lastClicked = newClick
            action()
        }
    }

    fun onAdClicked(ad: MaxAd, adClient: AdClient) {
        // the sdk emit twice the clicked ad action, I need to debounce it
        debounceClick(ad) {
            val adInfos = ad.buildInfo()

            Timber.d("$TAG CLICKED ${ad.nativeAd?.format?.label} ${ad.adUnitId}")

            analyticsManager.logEvent(
                AdsEvents.ClickAd(
                    analyticsParamsOfAd(
                        adInfos = adInfos,
                        adClient = adClient,
                        adDisplayPosition = AdDisplayPosition.getAdDisplayPosition(ad.id.id),
                        loaderIdentifier = ad.id.let { adClient.getRequestIdFromAd(it) },
                    ),
                ),
            )
        }
    }

    fun onAdnAdClicked(adnAd: AdnAd) {
        log("CLICKED", adnAd.info.network)
        analyticsManager.logEvent(
            AdsEvents.ClickAd(
                analyticsParamsOfAd(
                    adInfos = adnAd.info,
                    adClient = null,
                    adDisplayPosition = AdDisplayPosition.getAdDisplayPosition(adnAd.id.id),
                    loaderIdentifier = null,
                ),
            ),
        )
    }

    // when we have the opportunity of displaying an ad
    fun onAdTriggered(ad: Ad, adClient: AdClient) {
        log("TRIGGERED", adClient.config.adUnit)
        analyticsManager.logEvent(
            AdsEvents.TriggerAd(
                analyticsParamsOfAd(
                    ad = ad,
                    adClient = adClient,
                ),
            ),
        )
    }

    fun onAdnAdTriggered(adnAd: AdnAd) {
        log("TRIGGERED", adnAd.info.network)
        analyticsManager.logEvent(
            AdsEvents.TriggerAd(
                analyticsParamsOfAd(
                    ad = adnAd,
                    adClient = null,
                ),
            ),
        )
    }

    private fun log(eventName: String, adUnitId: String) {
        Timber.d("$TAG : track($eventName): (adUnit: $adUnitId)")
    }

    private fun analyticsParamsOfAd(ad: Ad?, adClient: AdClient?): List<AnalyticsParam> = analyticsParamsOfAd(
        adInfos = ad?.info,
        adClient = adClient,
        adDisplayPosition = AdDisplayPosition.getAdDisplayPosition(ad?.id?.id),
        loaderIdentifier = ad?.id?.let { adClient?.getRequestIdFromAd(it) },
    )

    private fun analyticsParamsOfAd(adInfos: Ad.Info?, adClient: AdClient?, adDisplayPosition: Int?, loaderIdentifier: String?): List<AnalyticsParam> =
        listOfNotNull(
            "adUnitIdentifier" toParamOrNull (adInfos?.adUnit ?: adClient?.config?.adUnit),
            "adReviewCreativeId" toParamOrNull adInfos?.reviewCreativeId,
            "placement" toParamOrNull (adInfos?.placement ?: adClient?.config?.placement),
            "cohortIdMax" toParamOrNull adInfos?.cohortId,
            "revenuePrecision" toParamOrNull adInfos?.revenuePrecision,
            "latency" toParamOrNull adInfos?.requestLatencyMillis,
            "revenueName" toParamOrNull adInfos?.formatLabel,
            "networkPlacement" toParamOrNull adInfos?.networkPlacement,
            "loaderIdentifier" toParamOrNull loaderIdentifier,
        ) + analyticsParamsOfAd(
            adType = (adInfos?.formatLabel ?: adClient?.adType?.analyticsName),
            adNetwork = adInfos?.network,
            adProvider = adInfos?.adProvider,
            adCampaignId = adInfos?.campaignId,
            revenueUSD = adInfos?.revenue,
            mediaType = adInfos?.creativeType,
            adCreativeId = adInfos?.creativeId,
            adDisplayPosition = adDisplayPosition,
        )
}
