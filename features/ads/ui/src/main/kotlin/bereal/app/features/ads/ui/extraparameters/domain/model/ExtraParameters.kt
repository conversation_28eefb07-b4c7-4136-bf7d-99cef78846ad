package bereal.app.features.ads.ui.extraparameters.domain.model

import androidx.compose.runtime.Immutable
import bereal.app.features.ads.domain.models.NeighbourContent
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList

@Immutable
data class ExtraParameters(
    val parameters: PersistentList<Pair<String, Any>>,
)

fun ExtraParameters.addGoogleNeighbour(neighbourContent: NeighbourContent?): ExtraParameters {
    return ExtraParameters(
        parameters = buildList {
            addAll(<EMAIL>)
            neighbourContent?.urls?.let {
                add("google_neighbouring_content_url_strings" to it)
            }
        }.toPersistentList(),
    )
}
