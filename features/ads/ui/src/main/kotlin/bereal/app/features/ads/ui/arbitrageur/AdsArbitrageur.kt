package bereal.app.features.ads.ui.arbitrageur

import androidx.annotation.MainThread
import bereal.app.features.ads.api.AdClient
import bereal.app.features.ads.api.ApplovinAdsSubsetOfClientsDomainModel
import bereal.app.features.ads.api.log.AdsLogger
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.api.model.AdAndClient
import bereal.app.features.ads.api.model.AdRequestId
import bereal.app.features.ads.api.model.AdsAndClient
import java.lang.ref.WeakReference
import kotlin.math.max

// @Factory
class AdsArbitrageur(
    val adsClients: ApplovinAdsSubsetOfClientsDomainModel,
) {

    private val adsForThisScreen = mutableListOf<WeakReference<AdAndClient>>()

    // gives a quick access to a client, using the request id
    private val clientByRequestIdMap = mutableMapOf<AdRequestId, WeakReference<AdClient>>()

    suspend fun adsReadyToBeDisplayed(): List<AdsAndClient> {
        return adsClients.adsReadyToBeInserted()
    }

    private fun findBetterAd(adAndClient: List<AdAndClient>): AdAndClient? {
        return if (adAndClient.isEmpty()) {
            null
        } else if (adAndClient.size == 1) {
            adAndClient.firstOrNull()
        } else {
            adAndClient.shuffled()
                .maxByOrNull { // shuffle to have a "randomized" client picked if all have the same value, and not only the first one
                    it.ad.info.revenue
                }
        }
    }

    /**
     * Get an ad for the given [requestId].
     *
     * @return in order of priority:
     * - Previously served ad if a call was already made with the same [requestId] and if the ad is still in memory
     * - The most profitable ad ([Ad.Info.revenue]) that's available (not already displayed)
     * - Any ad that was already displayed but is still in memory and not used by another component
     */
    @MainThread
    fun getAdFromRequestId(requestId: AdRequestId): Ad? {
        return synchronized(clientByRequestIdMap) {
            // Re-serve previous ad even if a more profitable one is available in another client
            clientByRequestIdMap[requestId]
                ?.get()
                ?.let { client ->
                    client.getAdFromRequestId(requestId)
                        ?.let { previousAd ->
                            AdsLogger.info(
                                client = client,
                                "getAdFromRequestId(${requestId.id}) usingPreviousAd=${previousAd.id}",
                            )
                            return previousAd
                        }
                }

            // Get all available ads and take most profitable
            var revenue = -1.0
            val ads: List<AdAndClient> = adsClients.clients.mapNotNull { client ->
                client.getAdToDisplay(
                    requestId = requestId,
                    revenueThreshold = revenue,
                )?.let {
                    revenue = max(revenue, it.info.revenue)
                    AdAndClient(
                        client = client,
                        ad = it,
                    )
                }
            }

            val bestAdAndClient = findBetterAd(ads)?.also {
                AdsLogger.info(
                    client = it.client,
                    "getAdFromRequestId(${requestId.id}) bestAdAndClient=${it.ad.id}",
                )
            }

            // Release non-retained ad views (since render was not called, it won't be destroyed)
            (ads - bestAdAndClient).filterNotNull().forEach {
                it.client.releaseAdView(it.ad)
            }

            // If no previous ad or no fresh ad found, return any ad that can be displayed
            // to avoid a blank UI
            val adAndClient = bestAdAndClient ?: adsClients.firstAdReadyToBeInserted()

            adAndClient?.let {
                AdsLogger.debug(
                    client = it.client,
                    "getAdFromRequestId(${requestId.id}) displays ${it.ad.id}",
                )

                adsForThisScreen.add(WeakReference(adAndClient))
                adAndClient.ad.startToDisplay(client = it.client, requestId = requestId)
                // quick access to this client using map
                clientByRequestIdMap[requestId] = WeakReference(it.client)

                adsClients.onTriggerAd(it.ad, it.client)

                return@let it.ad
            }
        }
    }

    @MainThread
    fun destroyAlreadyDisplayedAndPaidAds() {
        // keep refs to cleanup at the end of the method
        val displayedAndPaidRefs = synchronized(adsForThisScreen) {
            adsForThisScreen.filter {
                it.get()?.ad?.isAlreadyDisplayedAndPaid() == true
            }
        }
        val displayedAndPaid = displayedAndPaidRefs.mapNotNull { it.get() }

        if (displayedAndPaid.isNotEmpty()) {
            displayedAndPaid.forEach {
                it.destroyAd()
            }
            synchronized(clientByRequestIdMap) {
                displayedAndPaid.map { it.ad.getRequestId() }.forEach {
                    clientByRequestIdMap.remove(it)
                }
            }
            synchronized(adsForThisScreen) {
                adsForThisScreen.removeAll(displayedAndPaidRefs)
            }
        }
    }

    /** Forward call to [AdClient.releaseAdView] to the client that served the given [ad] */
    @MainThread
    fun releaseAdView(ad: Ad) {
        ad.endToDisplay()
    }

    @MainThread
    fun destroyArbitrageur() {
        AdsLogger.info("destroyArbitrageur")

        adsForThisScreen.mapNotNull { it.get() }.forEach {
            it.destroyAd()
        }
        adsForThisScreen.clear()
    }
}
