package bereal.app.features.ads.ui.natives

import android.content.Context
import bereal.app.features.ads.applovin.nativ.MaxNativeAdViewFactory
import bereal.app.features.ads.ui.R
import com.applovin.mediation.MaxAd
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder

class NativeAdViewFactory : MaxNativeAdViewFactory {

    /**
     * - Any ad coming from APPLOVIN_EXCHANGE, Pangle Native,  Liftoff Monetize, InMobi, Line, Bidmachine & Google AdMob &  BigoAds should be displayed in 3x4 mode.
     * - Any ad coming from Google Ad Manager, Google Ad Manager Native, VOODOO_BIDDING  should be displayed in 9x16 mode
     */
    private fun MaxAd.isBranded(): Boolean {
        return when (this.networkName) {
            "Google Ad Manager",
            "Google Ad Manager Native",
            "VOODOO_BIDDING",
            -> true
            else -> false
        }
    }

    override fun create(context: Context, ad: <PERSON><PERSON>d, displayedInList: <PERSON>ole<PERSON>): MaxNativeAdView {
        val layout = if (displayedInList) {
            R.layout.layout_feed_ad_item
        } else if (ad.isBranded()) {
            R.layout.layout_feed_ad_item_branded // draw text over the image
        } else {
            R.layout.layout_feed_ad_item_flexible
        }
        val binder = MaxNativeAdViewBinder.Builder(layout)
            .setIconImageViewId(R.id.icon_image_view)
            .setTitleTextViewId(R.id.title_text_view)
            .setBodyTextViewId(R.id.body_text_view)
            // .setStarRatingContentViewGroupId(R.id.star_rating_view)
            // .setAdvertiserTextViewId(R.id.advertiser_textView)
            .setMediaContentViewGroupId(R.id.media_view_container)
            .setOptionsContentViewGroupId(R.id.ad_options_view)
            .setCallToActionButtonId(R.id.cta_button)
            .build()

        return MaxNativeAdView(
            binder,
            context,
        )
    }
}
