package bereal.app.features.ads.ui.arbitrageur

import android.app.Activity
import bereal.app.features.ads.api.ApplovinAllAdsClientsDomainModel

// TODO on another MR : move to another module
interface AppLovinAdsClientsRepository {
    val clients: ApplovinAllAdsClientsDomainModel?
    fun setup(
        activity: Activity,
        settings: AdClientArbitrageurSettings,
    ): ApplovinAllAdsClientsDomainModel
    fun clear(value: ApplovinAllAdsClientsDomainModel)
}
