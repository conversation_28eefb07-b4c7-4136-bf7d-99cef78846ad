package bereal.app.features.ads.ui.adn

import bereal.app.common.resumeSafety
import bereal.app.commonandroid.CurrentActivity
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.adn.AdnAd
import bereal.app.features.ads.applovin.domain.ComputeLongLifeAdsUseCase
import bereal.app.features.ads.ui.AdTracker
import bereal.app.features.ads.ui.adn.model.DirectDealParams
import bereal.app.features.ads.ui.natives.AdnNativeAdViewFactoryImpl
import io.adn.sdk.publisher.AdnAdError
import io.adn.sdk.publisher.AdnAdInfo
import io.adn.sdk.publisher.AdnAdPlacement
import io.adn.sdk.publisher.AdnAdRequest
import io.adn.sdk.publisher.AdnNativeAd
import io.adn.sdk.publisher.AdnNativeAdListener
import io.adn.sdk.publisher.AdnSdk
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory
import timber.log.Timber
import java.util.Date
import java.util.UUID
import kotlin.coroutines.suspendCoroutine

@Factory
class AdnAdsLoadingRepositoryImpl(
    private val currentActivity: CurrentActivity,
    private val dispatcherProvider: DispatcherProvider,
    private val adTracker: AdTracker,
    private val computeLongLifeAdsUseCase: ComputeLongLifeAdsUseCase,
) : AdnAdsLoadingRepository {

    companion object {
        private const val TAG = "AdnNativeAdsFactory"
        private const val ADN_STANDALONE_NETWORK_NAME = "VOODOO_BIDDING_STANDALONE"
        private const val S2S_NETWORK_NAME = "VOODOO_BIDDING"
    }

    override suspend fun loadNativeAd(directDeal: DirectDealParams): AdnAd? {
        return currentActivity.current?.let {
            withContext(dispatcherProvider.data) {
                suspendCoroutine<AdnAd?> { continuation ->
                    var nativeAd: AdnNativeAd? = null
                    val listener = object : AdnNativeAdListener {
                        private var adnAd: AdnAd? = null

                        override fun onAdLoaded(adInfo: AdnAdInfo) {
                            val networkName = directDeal?.directDealId?.let { S2S_NETWORK_NAME }
                                ?: ADN_STANDALONE_NETWORK_NAME
                            val ad = buildAdnAd(
                                adnAdInfo = adInfo,
                                adnNativeAd = nativeAd,
                                networkName = networkName,
                            )
                            adnAd = ad

                            continuation.resumeSafety(ad)

                            adTracker.onAdnAdLoadingFinished(ad)
                        }

                        override fun onAdMetadataLoaded(adInfo: AdnAdInfo) {
                            // No implementation
                        }

                        override fun onAdClicked(adInfo: AdnAdInfo?) {
                            adnAd?.let {
                                adTracker.onAdnAdClicked(it)
                            }
                        }

                        override fun onAdImpression(adInfo: AdnAdInfo?) {
                            adnAd?.let {
                                adTracker.onAdnAdImpression(it)
                            }
                        }

                        override fun onAdLoadFailed(error: AdnAdError) {
                            adTracker.onAdnAdLoadFailed(
                                error = error,
                                adNetwork = ADN_STANDALONE_NETWORK_NAME,
                            )
                            continuation.resumeSafety(null)
                        }

                        override fun onAdShowFailed(adInfo: AdnAdInfo?, error: AdnAdError) {
                            Timber.Forest.tag(TAG)
                                .d("onAdShowFailed | ${error.errorCode} : ${error.errorMessage}")
                        }
                    }
                    nativeAd = AdnSdk.getNativeAdInstance(
                        it,
                        listener,
                    )
                    adTracker.onAdnAdLoadingStarted(ADN_STANDALONE_NETWORK_NAME)

                    nativeAd.load(
                        AdnAdRequest.AdBidRequest(
                            AdnAdPlacement.NATIVE,
                            directDeal.adm,
                        ),
                    )
                }
            }
        }
    }

    private fun buildAdnAd(
        adnAdInfo: AdnAdInfo,
        adnNativeAd: AdnNativeAd?,
        networkName: String,
    ): AdnAd {
        val adId = adnAdInfo.id ?: UUID.randomUUID().toString()
        return AdnAd(
            adId = adId,
            adnNativeAd = adnNativeAd,
            viewFactory = AdnNativeAdViewFactoryImpl(),
            info = Ad.Info(
                adUnit = "",
                network = networkName,
                revenue = adnAdInfo.price,
                revenuePrecision = "",
                cohortId = null,
                creativeId = adnAdInfo.creativeId,
                creativeType = adnAdInfo.creativeType,
                campaignId = adnAdInfo.campaignId,
                placement = null,
                reviewCreativeId = null,
                formatLabel = null,
                requestLatencyMillis = 0L,
                networkPlacement = null,
                adProvider = networkName,
            ),
            loadedAt = Date(),
            longLifeAds = computeLongLifeAdsUseCase(),
        )
    }
}
