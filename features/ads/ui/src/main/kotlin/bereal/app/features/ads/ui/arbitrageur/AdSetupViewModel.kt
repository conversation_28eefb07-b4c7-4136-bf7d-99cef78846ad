package bereal.app.features.ads.ui.arbitrageur

import android.app.Activity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.ads.AdsSetup
import bereal.app.features.ads.api.ApplovinAllAdsClientsDomainModel
import bereal.app.features.ads.api.model.AdTarget
import bereal.app.features.ads.api.model.BackoffConfig
import bereal.app.features.ads.domain.models.AdsEnabledResult
import bereal.app.features.ads.domain.usecases.ObserveAdsSettingsIfIsInitializedUseCase
import bereal.app.features.ads.ui.extraparameters.domain.ObserveExtraParametersUseCase
import bereal.app.features.ads.ui.extraparameters.domain.model.ExtraParameters
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import kotlin.time.Duration.Companion.seconds

@KoinViewModel
class AdSetupViewModel(
    observeIsAdsEnabledUseCase: ObserveAdsSettingsIfIsInitializedUseCase,
    private val dispatcherProvider: DispatcherProvider,
    private val appLovinAdsClientsRepository: AppLovinAdsClientsRepository,
    private val observeExtraParametersUseCase: ObserveExtraParametersUseCase,
) : ViewModel() {

    private val adsSettings: StateFlow<AdClientArbitrageurSettings?> =
        observeIsAdsEnabledUseCase()
            .map {
                when (it) {
                    is AdsEnabledResult.Disabled -> null
                    is AdsEnabledResult.Enabled -> AdClientArbitrageurSettings(
                        backoffConfig = BackoffConfig(
                            maxDelay = 10.seconds,
                        ),
                        clients = it.adsUnitIds.map { adUnit ->
                            AdClientConfig(
                                adUnitId = adUnit.adUnitId,
                                type = when (adUnit.type) {
                                    AdsSetup.AdUnit.Type.Mrec -> AdClientConfig.Type.Mrec
                                    AdsSetup.AdUnit.Type.Native -> AdClientConfig.Type.Native
                                },
                                maximumPoolSize = adUnit.maximumPoolSize,
                                feedTarget = when (adUnit.target) {
                                    AdsSetup.AdUnit.AdTarget.All -> AdTarget.ALL
                                    AdsSetup.AdUnit.AdTarget.Friends -> AdTarget.FRIENDS
                                    AdsSetup.AdUnit.AdTarget.Discovery -> AdTarget.DISCOVERY
                                    AdsSetup.AdUnit.AdTarget.FoF -> AdTarget.FOF
                                },
                            )
                        },
                    )
                }
            }
            .flowOn(dispatcherProvider.viewmodel)
            .stateIn(viewModelScope, SharingStarted.Eagerly, null)

    fun initialize(activity: Activity, lifecycle: Lifecycle) {
        viewModelScope.launch(dispatcherProvider.ui) { // for speed execution
            adsSettings
                .filterNotNull()
                .map { settings ->
                    appLovinAdsClientsRepository.setup(
                        settings = settings,
                        activity = activity,
                    )
                }
                .onEach { loaders ->
                    // be sure it's destroyed with the activity
                    var lifecycleObserver: LifecycleObserver? = null
                    lifecycleObserver = object : DefaultLifecycleObserver {
                        override fun onDestroy(owner: LifecycleOwner) {
                            appLovinAdsClientsRepository.clear(loaders)
                            lifecycleObserver?.let {
                                lifecycle.removeObserver(it)
                            }
                        }
                    }
                    lifecycle.addObserver(lifecycleObserver)

                    // preload the first ad
                    viewModelScope.launch(dispatcherProvider.viewmodel) {
                        preloadFirstAdsIfNecessary(loaders)
                    }
                }
                .collect {}
        }
    }

    private suspend fun preloadFirstAdsIfNecessary(loaders: ApplovinAllAdsClientsDomainModel) {
        val extraParams: ExtraParameters = observeExtraParametersUseCase().firstOrNull()
            ?: observeExtraParametersUseCase.defaultValue()

        loaders.fetchAdIfNecessary {
            extraParams.parameters
        }
    }
}
