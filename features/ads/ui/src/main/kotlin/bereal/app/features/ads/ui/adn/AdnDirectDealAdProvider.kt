package bereal.app.features.ads.ui.adn

import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.features.ads.applovin.adn.AdnAd
import bereal.app.features.ads.ui.adn.model.DirectDealAdState
import bereal.app.features.ads.ui.adn.model.DirectDealParams
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory

@Factory
class AdnDirectDealAdProvider(
    private val adnAdsLoadingRepository: AdnAdsLoadingRepository,
    private val dispatcherProvider: DispatcherProvider,
) {
    private val directDealsMutex = Mutex()
    private val nativeDirectDealsLoaded =
        MutableStateFlow<Map<String, DirectDealAdState>>(emptyMap())

    fun observeDirectDeal(
        directDeal: DirectDealParams,
        loadingScope: () -> CoroutineScope,
    ): Flow<AdnAd?> {
        return nativeDirectDealsLoaded
            .map { it[directDeal.directDealId] }
            .distinctUntilChanged()
            .map { ad ->
                if (ad != null) {
                    return@map when (ad.state) {
                        is DirectDealAdState.State.Loaded -> ad.state.ad

                        is DirectDealAdState.State.Loading -> null
                    }
                } else {
                    // to not block the flow because of the mutex inside, and not be scopped to the flow scope
                    loadingScope().launch {
                        // be sure only 1 flow can edit at the time the nativeDirectDealsLoaded & perform the load
                        loadDirectDeal(
                            loadingScope,
                            directDeal,
                        )
                    }
                    // return null during the loading
                    return@map null
                }
            }
    }

    private suspend fun loadDirectDeal(
        loadingScope: () -> CoroutineScope,
        directDeal: DirectDealParams,
    ) {
        directDealsMutex.withLock {
            // I don't want to use the flow scope here, but another managed scope (here the delegate one)
            val loadJob = loadingScope().launch {
                adnAdsLoadingRepository.loadNativeAd(directDeal = directDeal)
                    ?.let { adnAd ->
                        directDealsMutex.withLock {
                            nativeDirectDealsLoaded.update {
                                it + Pair(
                                    directDeal.directDealId,
                                    DirectDealAdState(
                                        directDealId = directDeal.directDealId,
                                        state = DirectDealAdState.State.Loaded(
                                            ad = adnAd,
                                        ),
                                    ),
                                )
                            }
                        }
                    }
            }
            nativeDirectDealsLoaded.update {
                it + Pair(
                    directDeal.directDealId,
                    DirectDealAdState(
                        directDealId = directDeal.directDealId,
                        state = DirectDealAdState.State.Loading(
                            loadJob = loadJob,
                        ),
                    ),
                )
            }
        }
    }

    suspend fun releaseDirectDealAd(idsToDestroy: Set<String>) {
        if (idsToDestroy.isNotEmpty()) {
            directDealsMutex.withLock {
                nativeDirectDealsLoaded.update { map ->
                    idsToDestroy.forEach {
                        map[it]?.let {
                            when (val state = it.state) {
                                is DirectDealAdState.State.Loaded -> {
                                    withContext(dispatcherProvider.ui) {
                                        state.ad.also {
                                            it.endToDisplay()
                                            it.adnNativeAd?.destroy()
                                        }
                                    }
                                }

                                is DirectDealAdState.State.Loading -> {
                                    runCatching {
                                        state.loadJob.cancel()
                                    }
                                }
                            }
                        }
                    }
                    map - idsToDestroy
                }
            }
        }
    }
}
