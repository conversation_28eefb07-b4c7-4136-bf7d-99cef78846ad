package bereal.app.features.ads.ui.model

import androidx.compose.runtime.Immutable
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.adn.AdnAd

// wrapper for compose stability
@Immutable
sealed interface AdViewState {
    val id: String

    @Immutable
    sealed interface AppLovin : AdViewState {
        val adNetworkInfo: String

        @Immutable
        data class Mrec(val ad: Ad) : AppLovin {
            override val id: String = ad.id.id
            override val adNetworkInfo: String = ad.info.network
        }

        @Immutable
        data class Native(val ad: Ad) : AppLovin {
            override val id: String = ad.id.id
            override val adNetworkInfo: String = ad.info.network
        }
    }

    @Immutable
    data class ADNNative(val ad: AdnAd) : AdViewState {
        override val id: String = ad.id.id
    }
}

fun AdViewState.AppLovin.getApplovinAd(): Ad {
    return when (this) {
        is AdViewState.AppLovin.Mrec -> ad
        is AdViewState.AppLovin.Native -> ad
    }
}

fun Ad.toViewState(): AdViewState = when (this.type) {
    Ad.Type.MREC -> AdViewState.AppLovin.Mrec(ad = this)
    Ad.Type.NATIVE -> {
        if (this is AdnAd) {
            AdViewState.ADNNative(ad = this)
        } else {
            AdViewState.AppLovin.Native(ad = this)
        }
    }
}
