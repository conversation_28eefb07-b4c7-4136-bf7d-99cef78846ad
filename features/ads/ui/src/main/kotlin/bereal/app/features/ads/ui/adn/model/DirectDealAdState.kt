package bereal.app.features.ads.ui.adn.model

import bereal.app.features.ads.applovin.adn.AdnAd
import kotlinx.coroutines.Job

internal data class DirectDealAdState(
    val directDealId: String,
    val state: State,
) {
    sealed interface State {
        data class Loaded(
            val ad: AdnAd,
        ) : State

        data class Loading(
            val loadJob: Job,
        ) : State
    }
}
