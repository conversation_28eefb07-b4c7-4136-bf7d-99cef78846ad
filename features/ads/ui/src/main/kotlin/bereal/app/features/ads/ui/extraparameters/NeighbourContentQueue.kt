package bereal.app.features.ads.ui.extraparameters

import androidx.compose.runtime.Immutable
import bereal.app.features.ads.domain.models.NeighbourContent
import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference

@Immutable
class NeighbourContentQueue {
    private val items = AtomicReference<List<NeighbourContent>>(emptyList())
    private var index = AtomicInteger(0)

    private val withLogs = false

    fun update(newValue: List<NeighbourContent>) {
        items.set(newValue)
    }

    fun next(): NeighbourContent? {
        return items.get().let { list ->
            list.getOrNull(index.getAndIncrement() % list.size)
        }?.also {
            if (withLogs) {
                Timber.d("NeighbourContent: used urls : ${it.urls}")
            }
        }
    }
}
