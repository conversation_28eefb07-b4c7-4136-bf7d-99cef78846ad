package bereal.app.features.ads.ui.extraparameters.domain

import bereal.app.features.ads.ui.extraparameters.domain.model.ExtraParameters
import bereal.app.user.usecases.ObserveMyUserAgeAndBirthdateUseCase
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory
import java.util.concurrent.TimeUnit

@Factory
class ObserveExtraParametersUseCase(
    private val observeMyUserAgeAndBirthdateUseCase: ObserveMyUserAgeAndBirthdateUseCase,
) {

    operator fun invoke(): Flow<ExtraParameters> {
        return bigoParameters().map { bigoParameters ->
            ExtraParameters(
                buildList {
                    add("bigoads_activated_time" to TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()))
                    bigoParameters?.let {
                        addAll(it)
                    }
                }.toPersistentList(),
            )
        }
    }

    private fun bigoParameters(): Flow<List<Pair<String, Any>>?> {
        return observeMyUserAgeAndBirthdateUseCase().map {
            it?.age?.let {
                listOf(
                    "bigoads_age" to it,
                    // "bigoads_gender" to 2, // gender male
                )
            }
        }
    }

    fun defaultValue() = ExtraParameters(
        persistentListOf(),
    )
}
