package bereal.app.features.ads.ui.privacy

import androidx.activity.ComponentActivity
import bereal.app.features.ads.domain.AdsKeys
import bereal.app.features.ads.domain.usecases.GetPrivacyOutdatedRepromptFlagUseCase
import io.voodoo.apps.privacy.VoodooPrivacyManager
import io.voodoo.apps.privacy.VoodooPrivacyManager.ConsentStatus
import io.voodoo.apps.privacy.config.SourcepointConfiguration
import org.koin.core.annotation.Factory

@Factory
class VoodooPrivacyManagerFactory(
    private val adsKeys: AdsKeys,
    private val getPrivacyOutdatedRepromptFlagUseCase: GetPrivacyOutdatedRepromptFlagUseCase,
) {
    fun create(
        activity: ComponentActivity,
        onStatusUpdate: ((ConsentStatus) -> Unit)? = null,
    ): VoodooPrivacyManager {
        return VoodooPrivacyManager(
            lifecycleOwner = activity,
            currentActivity = activity,
            autoShowPopup = true,
            sourcepointConfiguration = SourcepointConfiguration(
                accountId = adsKeys.config.accountId,
                propertyId = adsKeys.config.propertyId,
                gdprPrivacyManagerId = adsKeys.config.gdprPrivacyManagerId,
                usMspsPrivacyManagerId = adsKeys.config.usMspsPrivacyManagerId,
                propertyName = adsKeys.config.propertyName,
            ),
            onError = {
                // Might be executed from background thread
                it.printStackTrace()
            },
            onStatusUpdate = onStatusUpdate,
            getPrivacyOutdatedRepromptFlag = {
                getPrivacyOutdatedRepromptFlagUseCase()
            },
        )
    }
}
