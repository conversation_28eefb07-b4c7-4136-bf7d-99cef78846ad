package bereal.app.features.ads.ui.natives.ratio

import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import bereal.app.features.ads.applovin.nativ.NativeAd
import bereal.app.features.ads.applovin.nativ.NativeAdsRenderListener
import com.applovin.mediation.nativeAds.MaxNativeAdView
import timber.log.Timber

class MaxNativeAdRenderListener : NativeAdsRenderListener {

    companion object {
        const val TAG = "MaxNativeAdRenderListener"
    }

    override fun onPreRender(ad: NativeAd, view: MaxNativeAdView) {
        // Because ad view need to have a fixed size, we need to update it before rendering
        Timber.d("$TAG network name: ${ad.ad.networkName}")

        // retrieve ratio from nativeAd if possible, if not use predefined aspect ratio per network
        val (ratio, needToHideBody) = ad.ad.nativeAd?.mediaContentAspectRatio
            ?.takeIf { it >= 3f / 4f }
            ?.let { it.toString() to false }
            ?: run {
                val defaultRatio = NativeAdNetworkDefaultRatio.getDefaultRatioFromNetworkName(ad.ad.networkName)
                defaultRatio to NativeAdNetworkDefaultRatio.needToHideBody(ad.ad.networkName)
            }

        Timber.d("$TAG onPreRender ratio $ratio")

        view.mediaContentViewGroup.updateLayoutParams<ConstraintLayout.LayoutParams> {
            dimensionRatio = ratio
        }

        updateRenderedView(
            nativeAdView = view,
            needToHideBody = needToHideBody,
        )
    }

    private fun updateRenderedView(
        nativeAdView: MaxNativeAdView,
        needToHideBody: Boolean,
    ): MaxNativeAdView {
        nativeAdView.optionsContentViewGroup?.let { optionsView ->
            optionsView.visibility = if (optionsView.childCount == 0) {
                View.GONE
            } else {
                View.VISIBLE
            }
        }

        nativeAdView.bodyTextView?.let { view ->
            view.visibility = if (view.text == null || view.text.isBlank() || needToHideBody) {
                View.GONE
            } else {
                View.VISIBLE
            }
        }

        return nativeAdView
    }
}
