package bereal.app.features.ads.ui.natives

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import bereal.app.features.ads.applovin.adn.AdnNativeAdViewFactory
import bereal.app.features.ads.ui.R
import com.google.android.material.imageview.ShapeableImageView
import io.adn.sdk.publisher.AdnClickElement
import io.adn.sdk.publisher.AdnNativeAd
import io.adn.sdk.publisher.AdnNativeInteractionHandler

// TODO TO Remove when we migrate to compose
class AdnNativeAdViewFactoryImpl : AdnNativeAdViewFactory {

    override fun create(context: Context, ad: AdnNativeAd?, displayedInList: Boolean): View {
        val renderingInfo = ad?.renderingInfo() ?: return View(context)

        val rootView = LayoutInflater.from(context).inflate(R.layout.layout_feed_ad_item, null)

        val iconImageView = rootView.findViewById<ShapeableImageView>(R.id.icon_image_view)
        val titleTextView = rootView.findViewById<TextView>(R.id.title_text_view)
        val bodyTextView = rootView.findViewById<TextView>(R.id.body_text_view)
        val mediaContainer = rootView.findViewById<FrameLayout>(R.id.media_view_container)
        val mediaFrontView = rootView.findViewById<FrameLayout>(R.id.media_front_view)
        val callToActionButton = rootView.findViewById<Button>(R.id.cta_button)
        val optionsView = rootView.findViewById<LinearLayout>(R.id.ad_options_view)
        optionsView.visibility = View.GONE

        // Bind text fields
        titleTextView.text = renderingInfo.title
        bodyTextView.text = renderingInfo.body
        callToActionButton.text = renderingInfo.callToAction

        // Bind icon image (if available)
        renderingInfo.iconUri?.let { uri ->
            runCatching { iconImageView.setImageURI(uri) }
        } ?: run {
            iconImageView.visibility = View.GONE
        }

        // Bind media view
        mediaContainer.removeAllViews()
        renderingInfo.mainMediaView?.let { mediaView ->
            (mediaView.parent as? ViewGroup)?.removeView(mediaView)
            mediaContainer.addView(mediaView)
            mediaFrontView.visibility = View.VISIBLE
        }

        val interactionHandler = AdnNativeInteractionHandler().apply {
            addClickableView(AdnClickElement.Unknown, rootView)
            addClickableView(AdnClickElement.Cta, callToActionButton)
            addClickableView(AdnClickElement.Icon, iconImageView)
            addClickableView(AdnClickElement.Title, titleTextView)
            addClickableView(AdnClickElement.Body, bodyTextView)
            addClickableView(AdnClickElement.MainMedia, mediaContainer)
            addClickableView(AdnClickElement.Custom("front"), mediaFrontView)
        }

        ad.prepareForInteraction(interactionHandler)

        return rootView
    }
}
