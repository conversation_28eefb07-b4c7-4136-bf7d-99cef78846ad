package bereal.app.features.ads.ui.arbitrageur

import android.app.Activity
import android.content.Context
import bereal.app.features.ads.api.AdClient
import bereal.app.features.ads.api.ApplovinAllAdsClientsDomainModel
import bereal.app.features.ads.api.listener.AdListenerImpl
import bereal.app.features.ads.api.listener.AdListeners
import bereal.app.features.ads.api.model.AdTarget
import bereal.app.features.ads.applovin.domain.ComputeLongLifeAdsUseCase
import bereal.app.features.ads.applovin.mrec.MaxMRECAdClient
import bereal.app.features.ads.applovin.nativ.MaxNativeAdClient
import bereal.app.features.ads.ui.AdTracker
import bereal.app.features.ads.ui.listener.DefaultMaxAdViewAdListener
import bereal.app.features.ads.ui.natives.NativeAdViewFactory
import bereal.app.features.ads.ui.natives.ratio.MaxNativeAdRenderListener
import com.applovin.mediation.MaxAd
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import kotlinx.coroutines.flow.MutableStateFlow
import org.koin.core.annotation.Single
import java.util.concurrent.atomic.AtomicInteger

// TODO on another MR : move to another module
@Single(binds = [AppLovinAdsClientsRepository::class])
internal class AppLovinAdsClientsRepositoryImpl(
    private val applicationContext: Context,
    private val adTracker: AdTracker,
    private val longLifeAdsUseCase: ComputeLongLifeAdsUseCase,
) : AppLovinAdsClientsRepository {

    private val _clients: MutableStateFlow<ApplovinAllAdsClientsDomainModel?> = MutableStateFlow(null)
    override val clients: ApplovinAllAdsClientsDomainModel?
        get() = _clients.value

    override fun clear(value: ApplovinAllAdsClientsDomainModel) {
        val currentClients = _clients.value
        if (currentClients == value) {
            currentClients.allClients.forEach {
                it.destroyClient()
            }
            _clients.value = null
        }
    }

    // TODO maybe find a better way
    private val adListener: AdListeners = AdListenerImpl().also {
        it.addAdLoadingListener(adTracker)
        it.addAdRevenueListener(adTracker)
        it.addAdModerationListener(adTracker)
    }

    private val clientCount = AtomicInteger(0)

    override fun setup(
        activity: Activity,
        settings: AdClientArbitrageurSettings,
    ): ApplovinAllAdsClientsDomainModel {
        val loaders = createLoaders(
            activity = activity,
            settings = settings,
        )
        _clients.value = loaders
        return loaders
    }

    private fun createLoaders(
        activity: Activity,
        settings: AdClientArbitrageurSettings,
    ): ApplovinAllAdsClientsDomainModel {
        return ApplovinAllAdsClientsDomainModel(
            backoffConfig = settings.backoffConfig,
            allClients = buildList {
                settings.clients.map { client ->
                    when (client.type) {
                        AdClientConfig.Type.Mrec -> add(
                            createMRECClient(
                                activity = activity,
                                mrectAdUnitId = client.adUnitId,
                                target = client.feedTarget,
                            ),
                        )
                        // create X (NUMBER_OF_NATIVE_CLIENTS or value from experiment) instances of this native client to have always X preloaded ads
                        AdClientConfig.Type.Native -> repeat(
                            client.maximumPoolSize ?: DEFAULT_MAXIMUM_POOL_SIZE,
                        ) {
                            add(
                                createNativeClient(
                                    nativesAdUnitId = client.adUnitId,
                                    target = client.feedTarget,
                                ),
                            )
                        }
                    }
                }
            },
            onTriggerAd = adTracker::onAdTriggered,
        )
    }

    private fun createNativeClient(
        nativesAdUnitId: String,
        target: AdTarget,
    ): AdClient {
        return MaxNativeAdClient(
            clientDisplayName = "NATIVE_${clientCount.getAndIncrement()}",
            config = AdClient.Config(
                // adCacheSize = 1,
                adUnit = nativesAdUnitId,
                feedTarget = target,
            ),
            adViewFactory = NativeAdViewFactory(),
            applicationContext = applicationContext,
            // Provide extras via here if more convenient than the UI
            localExtrasProviders = emptyList(),
            renderListener = MaxNativeAdRenderListener(),
            listeners = adListener,
            longLifeAdsUseCase = longLifeAdsUseCase,
        ).apply {
            addMaxNativeAdListener(object : MaxNativeAdListener() {
                override fun onNativeAdClicked(ad: MaxAd) {
                    adTracker.onAdClicked(
                        ad = ad,
                        adClient = this@apply,
                    )
                }
            })
        }
    }

    private fun createMRECClient(
        activity: Activity,
        mrectAdUnitId: String,
        target: AdTarget,
    ): AdClient {
        return MaxMRECAdClient(
            clientDisplayName = "MREC_${clientCount.getAndIncrement()}",
            config = AdClient.Config(
                // adCacheSize = 1,
                adUnit = mrectAdUnitId,
                feedTarget = target,
            ),
            activity = activity, // for view creation
            localExtrasProviders = emptyList(),
            listeners = adListener,
            longLifeAdsUseCase = longLifeAdsUseCase,
        ).apply {
            addMaxAdViewListener(object : DefaultMaxAdViewAdListener() {
                override fun onAdClicked(ad: MaxAd) {
                    adTracker.onAdClicked(
                        ad = ad,
                        adClient = this@apply,
                    )
                }
            })
        }
    }

    companion object {
        const val DEFAULT_MAXIMUM_POOL_SIZE = 3
    }
}
