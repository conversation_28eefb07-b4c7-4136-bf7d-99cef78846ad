plugins {
    id(libs.plugins.bereal.domain.get().pluginId)
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.serialization.get().pluginId)
}

android {
    namespace = "bereal.app.features.ads.domain"
}

dependencies {
    implementation(libs.kotlinx.serialization.json)

    implementation(projects.features.myUser.domain)

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.debugMode.debugModeCore)
    implementation(projects.platform.device.domain)
    implementation(projects.platform.settings.domain)

    ksp(libs.koin.ksp.compiler)
}
