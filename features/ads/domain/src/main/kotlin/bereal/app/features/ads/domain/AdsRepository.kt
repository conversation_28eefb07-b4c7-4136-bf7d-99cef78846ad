package bereal.app.features.ads.domain

import bereal.app.entities.ads.AdsSetup
import bereal.app.features.ads.domain.models.AdsAdjustEvent
import bereal.app.features.ads.domain.models.AdsExtraParameters
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant

interface AdsRepository {

    val advertisingID: Flow<String?> // IDFA / AD_ID
    suspend fun initializeAdvertisingID(hasUserContent: Boolean)

    val appSetId: Flow<String?> // App Set ID
    suspend fun initializeAppSetId(hasUserContent: Boolean)

    suspend fun init(
        adsSetup: AdsSetup,
    )

    fun observeAdsProviderInitialized(): Flow<Boolean>
    fun openAdsMediationDebugger()

    fun sendAdsExtraParameters(adsExtraParameters: AdsExtraParameters)

    // region adjust
    fun initAdjust()
    fun sendRevenuePaidEventToAdjust(
        revenue: Double,
        network: String,
        adUnit: String,
        placement: String?,
    )
    fun sendAdjustEvent(adjustEvent: AdsAdjustEvent)
    // endregion

    suspend fun getRePromptLastDate(): Instant?
    suspend fun setRePromptLastDate(value: Instant?)
    suspend fun getGrpdConsentDescription(): String?
}
