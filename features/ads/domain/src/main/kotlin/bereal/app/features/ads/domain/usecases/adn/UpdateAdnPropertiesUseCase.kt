package bereal.app.features.ads.domain.usecases.adn

import bereal.app.features.ads.domain.AdnAdsRepository
import bereal.app.features.ads.domain.AdsRepository
import org.koin.core.annotation.Factory

@Factory
class UpdateAdnPropertiesUseCase(
    private val adnAdsRepository: AdnAdsRepository,
    private val adsRepository: AdsRepository,
) {

    suspend operator fun invoke(hasUserContent: <PERSON><PERSON><PERSON>, doNotSell: <PERSON>ole<PERSON>) {
        adsRepository.initializeAdvertisingID(hasUserContent = hasUserContent)
        adsRepository.initializeAppSetId(hasUserContent = hasUserContent)
        adnAdsRepository.update(
            hasUserContent = hasUserContent,
            doNotSell = doNotSell,
        )
    }
}
