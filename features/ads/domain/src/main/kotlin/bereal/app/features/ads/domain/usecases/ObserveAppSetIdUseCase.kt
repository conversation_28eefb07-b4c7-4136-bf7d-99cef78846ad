package bereal.app.features.ads.domain.usecases

import bereal.app.features.ads.domain.AdsRepository
import kotlinx.coroutines.flow.Flow
import org.koin.core.annotation.Factory

/**
 * Returns the App Set ID,
 * null if :
 * - the user did not accepted the ads / GDPR POPUP
 * - there was an error retrieving the App Set ID
 *
 * App Set ID is a unique identifier that:
 * - Persists across app uninstalls/reinstalls within the same app set
 * - Remains consistent when users reset their Advertising ID
 * - Resets only when the user factory resets their device or doesn't use the app for 13 months
 */
@Factory
class ObserveAppSetIdUseCase(
    private val adsRepository: AdsRepository,
) {
    operator fun invoke(): Flow<String?> {
        return adsRepository.appSetId
    }
}
