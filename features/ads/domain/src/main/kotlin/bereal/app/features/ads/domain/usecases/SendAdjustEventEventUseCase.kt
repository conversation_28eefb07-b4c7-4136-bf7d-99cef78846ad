package bereal.app.features.ads.domain.usecases

import bereal.app.features.ads.domain.AdsRepository
import bereal.app.features.ads.domain.models.AdsAdjustEvent
import org.koin.core.annotation.Factory

@Factory
class SendAdjustEventEventUseCase(
    private val adsRepository: AdsRepository,
) {
    operator fun invoke(event: AdsAdjustEvent) {
        adsRepository.sendAdjustEvent(adjustEvent = event)
    }
}
