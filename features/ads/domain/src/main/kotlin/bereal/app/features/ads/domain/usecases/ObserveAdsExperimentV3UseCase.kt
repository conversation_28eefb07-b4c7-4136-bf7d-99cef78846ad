package bereal.app.features.ads.domain.usecases

import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.ads.AdsSetup
import bereal.app.settings.model.ads.AdsPoolSizeDomainModel
import bereal.app.settings.model.ads.AdsV3ExperimentDomainModel
import bereal.app.settings.repositories.ExperimentRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
class ObserveAdsExperimentV3UseCase(
    private val experimentRepository: ExperimentRepository,
    private val dispatcherProvider: DispatcherProvider,
    private val observeAdsMaximumPoolSizeExperimentUseCase: ObserveAdsMaximumPoolSizeExperimentUseCase,
) {

    operator fun invoke(): Flow<AdsV3ExperimentDomainModel> =
        experimentRepository.adsV3Experiment.observe()
            .flatMapLatest { adsV3 ->
                when (adsV3) {
                    is AdsV3ExperimentDomainModel.Disabled -> flowOf(adsV3)
                    is AdsV3ExperimentDomainModel.Enabled -> observeAdsMaximumPoolSizeExperimentUseCase()
                        .map { poolSizeObjectList ->
                            adsV3.copy(
                                adsV3.adUnits.map { adUnit ->
                                    adUnit.copy(
                                        maximumPoolSize = poolSizeObjectList.getMaximumPoolSizeForAdTypeAndProvider(
                                            adProvider = adUnit.provider,
                                            adType = adUnit.type,
                                        ),
                                    )
                                },

                            )
                        }
                }
            }
            .flowOn(dispatcherProvider.domain)
            .distinctUntilChanged()

    private fun List<AdsPoolSizeDomainModel>.getMaximumPoolSizeForAdTypeAndProvider(
        adProvider: AdsSetup.AdUnit.Provider?,
        adType: AdsSetup.AdUnit.Type?,
    ): Int? =
        this.find { it.provider != null && it.type != null && it.provider == adProvider && it.type == adType }?.maximumPoolSize
}
