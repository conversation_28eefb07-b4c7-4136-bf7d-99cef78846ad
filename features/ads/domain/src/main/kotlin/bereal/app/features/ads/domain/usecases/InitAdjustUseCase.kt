package bereal.app.features.ads.domain.usecases

import bereal.app.features.ads.domain.AdsRepository
import kotlinx.coroutines.flow.firstOrNull
import org.koin.core.annotation.Factory

@Factory
class InitAdjustUseCase(
    private val adsRepository: AdsRepository,
    private val observeRejectConsentEnforcementExperimentUseCase: ObserveRejectConsentEnforcementExperimentUseCase,
) {
    suspend operator fun invoke() {
        if (observeRejectConsentEnforcementExperimentUseCase().firstOrNull() == false) {
            adsRepository.initAdjust()
        }
    }
}
