package bereal.app.features.ads.domain.usecases

import bereal.app.analytics.AEvent
import bereal.app.analytics.AnalyticsManager
import bereal.app.common.combines
import bereal.app.entities.ads.AdsSetup
import bereal.app.entities.ads.formatKeywordsForAnalytics
import bereal.app.features.ads.domain.AdsRepository
import bereal.app.features.ads.domain.customtargetting.ObserveCustomTargetingUseCase
import bereal.app.features.ads.domain.models.AdsEnabledResult
import bereal.app.features.ads.domain.usecases.adn.UpdateAdnPropertiesUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory
import timber.log.Timber

@Factory
class InitAdsUseCase(
    private val adsRepository: AdsRepository,
    private val analyticsManager: AnalyticsManager,
    private val observeAdsTimeMachineExperimentUseCase: ObserveAdsTimeMachineExperimentUseCase,
    private val observeIsAdsEnabledUseCase: ObserveIsAdsEnabledUseCase,
    private val observeCustomTargetingUseCase: ObserveCustomTargetingUseCase,
    private val observeIsAdsInitializedUseCase: ObserveIsAdsInitializedUseCase,
    private val updateAdnProperties: UpdateAdnPropertiesUseCase,
    private val observeRejectConsentEnforcementExperimentUseCase: ObserveRejectConsentEnforcementExperimentUseCase,
) {

    enum class InitStatus {
        Initialized,
        WaitingForUserAge,
        Disabled,
    }

    suspend operator fun invoke(
        doNotSellDataEnabled: Boolean,
        gdprConsentDescription: String?,
    ): Flow<InitStatus> {
        return combines(
            observeRejectConsentEnforcementExperimentUseCase(),
            observeAdsTimeMachineExperimentUseCase(),
            observeIsAdsEnabledUseCase(),
            observeIsAdsInitializedUseCase(),
        )
            .distinctUntilChanged()
            .flatMapLatest { (isRejectConsentEnforcementEnabled, adsTimeMachine, adsEnabled, adsInitialized) ->
                if (!adsTimeMachine.shouldInitAds || isRejectConsentEnforcementEnabled) {
                    updateAdnProperties(
                        doNotSell = doNotSellDataEnabled,
                        hasUserContent = false,
                    )
                    return@flatMapLatest flowOf(InitStatus.Disabled)
                }

                updateAdnProperties(
                    doNotSell = doNotSellDataEnabled,
                    hasUserContent = true,
                )

                if (adsInitialized) {
                    flowOf(InitStatus.Initialized)
                } else when (adsEnabled) {
                    is AdsEnabledResult.Enabled -> {
                        observeCustomTargetingUseCase()
                            .distinctUntilChanged()
                            .map { customTargeting ->
                                // null if we don't have yet the user age
                                customTargeting?.let {
                                    try {
                                        analyticsManager.logEvent(
                                            AEvent.InitAdsSdk(
                                                yearOfBirth = it.yearOfBirth?.toString() ?: "?",
                                                segment = formatKeywordsForAnalytics(customTargeting.keyWords),
                                                adUnits = adsEnabled.adsUnitIds
                                                    .map { it.adUnitId }
                                                    .joinToString(separator = ","),
                                            ),
                                        )
                                    } catch (t: Throwable) {
                                        Timber.e(t)
                                    }

                                    adsRepository.init(
                                        adsSetup = AdsSetup(
                                            doNotSellDataEnabled = doNotSellDataEnabled,
                                            adsUnitIds = adsEnabled.adsUnitIds,
                                            customTargeting = customTargeting,
                                            gdprConsent = gdprConsentDescription,
                                        ),
                                    )
                                    InitStatus.Initialized
                                } ?: run {
                                    InitStatus.WaitingForUserAge
                                }
                            }
                    }

                    is AdsEnabledResult.Disabled -> {
                        Timber.e("ADS NOT INITIALIZED BECAUSE : ADS NOT ENABLED FROM EXPERIMENT")
                        flowOf(InitStatus.Disabled)
                    }
                }
            }
    }
}
