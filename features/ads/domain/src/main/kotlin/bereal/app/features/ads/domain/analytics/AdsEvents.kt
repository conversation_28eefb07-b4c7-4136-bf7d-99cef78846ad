@file:Suppress("detekt:EnumNaming")

package bereal.app.features.ads.domain.analytics

import bereal.app.analytics.ext.toParam
import bereal.app.analytics.model.AnalyticsEvent
import bereal.app.analytics.model.AnalyticsParam

sealed interface AdsEvents : AnalyticsEvent {
    data class WatchAd(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "watchAd"
    }

    data class ClickAd(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "clickAd"
    }

    data class TriggerAd(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "triggerAd"
    }

    data class StartAdLoading(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "startAdLoading"
    }

    data class FinishAdLoading(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "finishAdLoading"
    }

    data class FailToLoadAd(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "failToLoadAd"
    }

    data class BlockAdOnLoad(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "blockAdOnLoad"
    }

    data class BlockAdOnDisplay(
        override val params: List<AnalyticsParam>,
    ) : AdsEvents {
        override val name = "blockAdOnDisplay"
    }

    data class ClickGdprPopup(
        val buttonUserClicked: ButtonUserClick,
        val placement: String,
    ) : AdsEvents {
        override val name = "clickGdprPopup"
        override val params: List<AnalyticsParam> = listOf(
            "buttonUserClicked" toParam buttonUserClicked.name,
            "placement" toParam placement,
        )

        enum class ButtonUserClick {
            ads_consent_accept,
            ads_consent_refuse,
            analytics_consent_accept,
            analytics_consents_refuse,
            data_sharing_with_voodoo_accept,
            data_sharing_with_voodoo_refuse,
        }
    }

    data class ScreenViewGdprPopup(
        val placement: String,
        val view: String,
    ) : AdsEvents {
        override val name = "screenViewGdprPopup"
        override val params: List<AnalyticsParam> = listOf(
            "placement" toParam placement,
            "view" toParam view,
        )
    }

    data class ScreenViewGDPRPrePopup(
        val placement: String,
    ) : AdsEvents {
        override val name = "screenViewGDPRPrePopup"
        override val params: List<AnalyticsParam> = listOf(
            "placement" toParam placement,
        )
    }

    data class ClickGDPRPrePopup(
        val accepted: Boolean,
        val placement: String,
    ) : AdsEvents {
        override val name = "clickGDPRPrePopup"
        override val params: List<AnalyticsParam> = listOf(
            "placement" toParam placement,
            "accepted" toParam accepted,
        )
    }
}
