package bereal.app.features.ads.domain.customtargetting

import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.model.AnalyticsUserPropertyType
import bereal.app.entities.ads.AdsSetup
import bereal.app.entities.ads.formatKeywordsForAnalytics
import bereal.app.user.usecases.ObserveMyUserAgeAndBirthdateUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import org.koin.core.annotation.Factory

@Factory
class ObserveCustomTargetingUseCase(
    private val observeMyUserAgeAndBirthdateUseCase: ObserveMyUserAgeAndBirthdateUseCase,
    private val analyticsManager: AnalyticsManager,
) {

    companion object {
        const val CUSTOM_TARGETING_AGE_KEY = 1
    }

    operator fun invoke(): Flow<AdsSetup.CustomTargeting?> {
        return observeMyUserAgeAndBirthdateUseCase()
            .map {
                // null if we don't have yet the user age
                it?.let { userAgeAndBirthdate ->
                    AdsSetup.CustomTargeting(
                        yearOfBirth = userAgeAndBirthdate.birthdate.year,
                        keyWords = listOfNotNull(
                            userAgeAndBirthdate.age.ageToCustomTargetingKeywordValue().let {
                                AdsSetup.CustomTargeting.Keyword(
                                    key = CUSTOM_TARGETING_AGE_KEY,
                                    value = it,
                                )
                            },
                        ),
                    )
                }
            }.onEach {
                it?.let {
                    analyticsManager.updateUserProperty(
                        mapOf(
                            AnalyticsUserPropertyType.AdsCustomTargeting.value to formatKeywordsForAnalytics(it.keyWords),
                        ),
                    )
                }
            }
    }

    // codes from https://docs.google.com/spreadsheets/d/1r70n175YiZjbXfd-hmeq-vMSiB6T0Lc8coTMLoIKFnU/edit?gid=0#gid=0
    private fun Int.ageToCustomTargetingKeywordValue(): Int {
        return when (this) {
            in 0..17 -> 10
            in 18..20 -> 11
            in 21..23 -> 12
            in 24..27 -> 13
            in 28..30 -> 14
            else -> 15
        }
    }
}
