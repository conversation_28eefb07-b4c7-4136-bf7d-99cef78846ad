package bereal.app.features.ads.domain

import kotlinx.coroutines.flow.Flow

interface AdsLogsRepository {
    val logs: Flow<List<Log>>
    fun addLog(log: Log)
    val infos: Flow<Map<Info.Type, Info>>
    fun setInfo(info: Info)

    val enableLogToasts: Flow<Boolean>
    suspend fun updateLogToasts(logToasts: Boolean)

    data class Log(
        val text: String,
        val waterfall: String? = null,
        val createdAt: Long,
        val type: Type,
    ) {
        enum class Type {
            Info,
            Error,
            Success,
        }
    }

    data class Info(
        val type: Type,
        val at: Long,
    ) {
        enum class Type {
            AppStarted,
            InitSdkStart,
            InitSdkEnd,
            FirstLoadStart,
            FirstLoadEnd,
            FirstAdViewed,
        }
    }
}
