package bereal.app.features.ads.domain

import android.util.Base64
import bereal.app.device.model.DeviceInfos
import bereal.app.entities.CorePost
import bereal.app.entities.pictureOrPlaceholderUrl
import bereal.app.features.ads.domain.models.ContentMapping
import bereal.app.features.ads.domain.models.NeighbourContent
import kotlinx.collections.immutable.toPersistentList
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

private const val HTTPS_PREFIX = "https://"
private const val CONTENT_MAPPING_PREFIX = "https://bereal.com/admob/"
private const val CONTENT_MAPPING_NUMBER_OF_REALMOJI = 5
private const val CONTENT_MAPPING_NUMBER_OF_COMMENTS = 5

fun buildContentMappingCorePost(
    showContentMappingDebugView: Boolean,
    posts: List<CorePost>?,
    deviceInfos: DeviceInfos,
) = NeighbourContent(
    showContentMappingDebugView = showContentMappingDebugView,
    urls = posts?.map { post ->
        with(post) {
            val contentUrls: List<String> = buildList {
                add(primary.pictureOrPlaceholderUrl().removePrefix(HTTPS_PREFIX))
                secondary?.let {
                    add(it.pictureOrPlaceholderUrl().removePrefix(HTTPS_PREFIX))
                }
            }
            val contentMapping = ContentMapping(
                m = contentUrls,
                mc = buildCaptionContentMapping(caption),
                mt = musicInfo?.let { "${it.artist} + ${it.track}" } ?: "",
                r = buildCoreRealmojiContentMapping(realMojis),
                c = getCoreCommentsForContentMapping(comments),
                v = buildVersionName(deviceInfos),
            )
            val base64 = encodeToBase64(contentMapping)
            "$CONTENT_MAPPING_PREFIX$base64"
        }
    }?.toPersistentList(),
)

private fun buildVersionName(deviceInfos: DeviceInfos): String {
    return "android-" + deviceInfos.clientVersion
}

private fun encodeToBase64(contentMapping: ContentMapping): String? {
    val json = Json.encodeToString(contentMapping)
    val base64 = Base64.encodeToString(json.toByteArray(), Base64.DEFAULT)
    return base64
}
private fun getCoreCommentsForContentMapping(comments: List<CorePost.Comment>): List<String> =
    comments.take(CONTENT_MAPPING_NUMBER_OF_COMMENTS).map { comment ->
        comment.text
    }

private fun buildCaptionContentMapping(caption: String?): String = caption ?: ""

private fun buildCoreRealmojiContentMapping(realmojis: List<CorePost.RealMoji>): List<String> =
    buildList {
        realmojis.take(CONTENT_MAPPING_NUMBER_OF_REALMOJI).forEach { realMoji ->
            add(
                realMoji.url.removePrefix(HTTPS_PREFIX),
            )
        }
    }
