package bereal.app.features.ads.domain.usecases

import bereal.app.settings.repositories.ExperimentRepository
import kotlinx.coroutines.flow.Flow
import org.koin.core.annotation.Factory

@Factory
class ObserveRejectConsentEnforcementExperimentUseCase(private val repository: ExperimentRepository) {

    operator fun invoke(): Flow<Boolean> =
        repository.rejectConsentEnforcementExperiment.observe()
}
