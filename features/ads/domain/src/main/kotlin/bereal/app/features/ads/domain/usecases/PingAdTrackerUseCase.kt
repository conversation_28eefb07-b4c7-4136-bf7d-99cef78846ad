package bereal.app.features.ads.domain.usecases

import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import bereal.app.features.ads.domain.AdsTrackerRepository
import org.koin.core.annotation.Factory

@Factory
class PingAdTrackerUseCase(
    private val adsTrackerRepository: AdsTrackerRepository,
) {
    suspend operator fun invoke(adTrackerUrl: String): Either<GenericError, Unit> {
        return adsTrackerRepository.pingAdTracker(url = adTrackerUrl)
    }
}
