package bereal.app.features.ads.domain.usecases

import bereal.app.features.ads.domain.AdsRepository
import org.koin.core.annotation.Factory

@Factory
class BroadcastRevenuePaidEventUseCase(
    private val adsRepository: AdsRepository,
) {
    operator fun invoke(
        revenue: Double,
        network: String,
        adUnit: String,
        placement: String?,
    ) {
        adsRepository.sendRevenuePaidEventToAdjust(
            revenue = revenue,
            network = network,
            adUnit = adUnit,
            placement = placement,
        )
    }
}
