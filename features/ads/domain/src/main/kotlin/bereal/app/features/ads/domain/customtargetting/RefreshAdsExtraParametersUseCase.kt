package bereal.app.features.ads.domain.customtargetting

import bereal.app.features.ads.domain.AdsRepository
import bereal.app.features.ads.domain.models.AdsExtraParameters
import bereal.app.user.usecases.ObserveMyUserAgeAndBirthdateUseCase
import bereal.app.user.usecases.ObserveMyUserUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import org.koin.core.annotation.Factory

@Factory
class RefreshAdsExtraParametersUseCase(
    private val adsRepository: AdsRepository,
    private val observeMyUserAgeAndBirthdateUseCase: ObserveMyUserAgeAndBirthdateUseCase,
    private val observeMyUserUseCase: ObserveMyUserUseCase,
) {

    operator fun invoke(): Flow<Unit> {
        return combine(
            observeMyUserAgeAndBirthdateUseCase(),
            observeMyUserUseCase(),
        ) { ageAndBirthdate, user ->

            AdsExtraParameters(
                userId = user.uid,
                yearOfBirth = ageAndBirthdate?.birthdate?.year,
                age = ageAndBirthdate?.age,
                gender = user.gender,
            )
        }.distinctUntilChanged().onEach {
            adsRepository.sendAdsExtraParameters(it)
        }.map { }
    }
}
