package bereal.app.features.ads.domain.usecases

import bereal.app.features.ads.domain.AdsRepository
import kotlinx.coroutines.flow.Flow
import org.koin.core.annotation.Factory

/**
 * Returns the AA_ID (google ads identifier),
 * null if :
 * - the user did not accepted the ads / GRPD POPUP
 * - the user disabled in inside his phone settings
 */
@Factory
class ObserveAdvertisingIdUseCase(
    private val adsRepository: AdsRepository,
) {
    operator fun invoke(): Flow<String?> {
        return adsRepository.advertisingID
    }
}
