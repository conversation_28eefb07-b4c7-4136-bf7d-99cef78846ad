package bereal.app.features.ads.domain.usecases

import bereal.app.features.ads.domain.models.AdsEnabledResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import org.koin.core.annotation.Factory

@Factory
class ObserveAdsSettingsIfIsInitializedUseCase(
    private val observeIsAdsEnabledUseCase: ObserveIsAdsEnabledUseCase,
    private val observeIsAdsInitializedUseCase: ObserveIsAdsInitializedUseCase,
) {

    operator fun invoke(): Flow<AdsEnabledResult> = combine(
        observeIsAdsInitializedUseCase(),
        observeIsAdsEnabledUseCase(),
    ) { initialized, isAdsEnabled ->
        if (initialized) {
            isAdsEnabled
        } else {
            AdsEnabledResult.Disabled
        }
    }
}
