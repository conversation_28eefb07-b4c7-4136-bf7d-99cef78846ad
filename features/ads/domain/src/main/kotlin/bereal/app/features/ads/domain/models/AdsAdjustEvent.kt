package bereal.app.features.ads.domain.models

abstract class AdsAdjustEvent(
    val id: String,
) {

    data object AccountCreated : AdsAdjustEvent("yggjqv")

    data object FirstBeRealPost : AdsAdjustEvent("alpvmx")

    data object Login : AdsAdjustEvent("dp80ya")

    data object SentRealmoji : AdsAdjustEvent("68iub4")

    data object SentComment : AdsAdjustEvent("ngtjb9")

    data object FriendsAdded {
        operator fun invoke(count: Int): AdsAdjustEvent? {
            val id = when (count) {
                1 -> "j3ocjj"
                2 -> "w3fuji"
                3 -> "juoppo"
                4 -> "fsz33q"
                5 -> "q3u61f"
                else -> return null
            }
            return object : AdsAdjustEvent(id) {}
        }
    }

    data object FriendInviteSent : AdsAdjustEvent("y6yeat")

    data object ViewBeReal : AdsAdjustEvent("d9qgtw")

    data object ViewedWelcomeCard : AdsAdjustEvent("ovoabr")

    data object MyUserAcceptAFriendRequest : AdsAdjustEvent("b94huq")

    data object FiveFriendsOnePost : AdsAdjustEvent("qdxq4y") {
        const val NUMBER_OF_FRIENDS_REQUIRED = 5
    }

    data object BeRealPosted : AdsAdjustEvent("3kwpmj")

    data object FriendFeedSize : AdsAdjustEvent("6visvy") {
        const val FRIEND_FEED_SIZE_THRESHOLD = 10
    }

    data object ReceivedRealmoji : AdsAdjustEvent("igtyln")

    data object UserReturnsAfter28Days : AdsAdjustEvent("cc9up2")
}
