package bereal.app.features.ads.domain.usecases

import bereal.app.entities.ads.AdsSetup
import bereal.app.features.ads.domain.models.AdsEnabledResult
import bereal.app.settings.model.ads.AdsV3ExperimentDomainModel
import bereal.app.settings.repositories.SettingsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
class ObserveIsAdsEnabledUseCase(
    settingsRepository: SettingsRepository,
    observeAdsExperimentV3UseCase: ObserveAdsExperimentV3UseCase,
) {

    private val settingsAds = settingsRepository.settings
        .map { it.featureFlagsSettings?.ads }
        .distinctUntilChanged()

    private val experimentsAds = observeAdsExperimentV3UseCase()
        .distinctUntilChanged()

    operator fun invoke(): Flow<AdsEnabledResult> =
        experimentsAds
            .flatMapLatest { experiment ->
                // if the experiment is enabled -> we use only the ad units from this one
                when (experiment) {
                    is AdsV3ExperimentDomainModel.Enabled -> {
                        flowOf(experiment.adUnits)
                    }
                    // if it's disabled -> we use ads units from the setting api
                    is AdsV3ExperimentDomainModel.Disabled -> {
                        settingsAds.map { adsSettings ->
                            (adsSettings?.adUnits ?: emptyList())
                        }
                    }
                }
            }.map { adUnits ->
                val clients = adUnits
                    .removeDisabled()

                AdsEnabledResult.Enabled(
                    adsUnitIds = clients,
                )
            }

    private fun List<AdsSetup.AdUnit>.removeDisabled(): List<AdsSetup.AdUnit> {
        return this.filterNot { it.adUnitId.contains("disable") || it.adUnitId.contains("debug") }
    }
}
