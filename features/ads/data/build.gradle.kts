plugins {
    id(libs.plugins.bereal.data.impl.get().pluginId)
    id(libs.plugins.bereal.obfuscator.get().pluginId)
    id(libs.plugins.bereal.retrofit.get().pluginId)
    id(libs.plugins.bereal.datastore.get().pluginId)
}

android {
    buildFeatures {
        buildConfig = true
    }
    namespace = "bereal.app.features.ads.data"
    setupProdSandboxFlavors()

    buildTypes {
        named("debug") {
            buildConfigField("Boolean", "appLovinLogs", "true")
            buildConfigField("Boolean", "appLovinShakeToSeeInfos", "true")
            buildConfigField("Boolean", "forceDebugDevice", "false")
            buildConfigField("Boolean", "adjustSandbox", "true")
        }
        named("appDistribution") {
            buildConfigField("Boolean", "appLovinLogs", "false")
            buildConfigField("Boolean", "appLovinShakeToSeeInfos", "true")
            buildConfigField("Boolean", "forceDebugDevice", "false")
            buildConfigField("Boolean", "adjustSandbox", "true")
        }
        named("release") {
            buildConfigField("Boolean", "appLovinLogs", "false")
            buildConfigField("Boolean", "appLovinShakeToSeeInfos", "false")
            buildConfigField("Boolean", "forceDebugDevice", "false")
            buildConfigField("Boolean", "adjustSandbox", "false")
        }
        internal {
            buildConfigField("Boolean", "appLovinLogs", "true")
            buildConfigField("Boolean", "appLovinShakeToSeeInfos", "true")
            buildConfigField("Boolean", "forceDebugDevice", "false")
            buildConfigField("Boolean", "adjustSandbox", "true")
        }
        if (isBenchmarkEnabled()) {
            create("benchmark") {
                initWith(getByName("release"))
                matchingFallbacks += listOf("release")

                buildConfigField("Boolean", "appLovinLogs", "false")
                buildConfigField("Boolean", "appLovinShakeToSeeInfos", "false")
                buildConfigField("Boolean", "forceDebugDevice", "true")
            }
        }
    }
}

fun DependencyHandlerScope.adProviders() {
    // copied from https://github.com/VoodooTeam/APPS-ad-tools-android/issues/3
    // Ad networks
    // Supported by apphrbr

    implementation(libs.applovin.googleAdapter)
    implementation(libs.applovin.bidmachineAdapter)
    implementation(libs.applovin.googleAdManagerAdapter)
    implementation(libs.applovin.inMobiAdapter)
    implementation(libs.applovin.byteDanceAdapter)
    implementation(libs.applovin.vungleAdapter)
    implementation(libs.adn.sdk)
    implementation(libs.applovin.bigoAds)
    implementation(libs.applovin.bigoAdsMediationNew)
    implementation(libs.amazon.aps)
}

fun DependencyHandlerScope.adProvidersInternalDependencies() {
    implementation(libs.bundles.retrofit)
    implementation(libs.picasso)
    implementation(libs.play.services.adsIdentifier)
}

dependencies {
    implementation(libs.android.ump)
    implementation(libs.androidx.recyclerview)
    implementation(libs.applovin)
    implementation(libs.appharbr.android.sdk)

    adProviders()
    adProvidersInternalDependencies()

    implementation(projects.features.ads.domain)

    implementation(libs.play.services.appset)
    implementation(libs.adjust.android)
    implementation(libs.installReferrer)
}
