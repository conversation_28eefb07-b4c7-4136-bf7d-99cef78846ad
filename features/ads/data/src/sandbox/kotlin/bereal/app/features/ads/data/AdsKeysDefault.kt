package bereal.app.features.ads.data

import bereal.app.features.ads.domain.AdsKeys
import bereal.app.obfuscator.Obfuscate
import org.koin.core.annotation.Single

@Obfuscate
@Single(
    binds = [AdsKeys::class],
)
class AdsKeysDefault : AdsKeys {
    // copied from prod/debug, maybe ask for sandbox values

    override val appLovinSdkKey: String = "E1M7r57HoT7PoxvgxbXnJLA55TKI1GOGHmO6rVNdzV1mQwQMWz7rJIxOrGgtW48prWwf1II-oKkDF9Zn7gbQzX"

    override val appHrbr: String = "349a90a7-18ce-4c55-b127-50338951bf9a" // Apphrbr sdk key

    override val config = AdsKeys.Configuration(
        accountId = 1909, // TODO
        propertyId = 36348,
        gdprPrivacyManagerId = "1144976",
        usMspsPrivacyManagerId = "1145461",
        propertyName = "voodoo.app.social",
    )
}
