package bereal.app.features.ads.data

import bereal.app.features.ads.data.log.AdsLogsDataStore
import bereal.app.features.ads.data.log.AdsLogsDebugDisplayer
import bereal.app.features.ads.domain.AdsLogsRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
internal class AdsLogsRepositoryImpl(
    private val applicationScope: CoroutineScope,
    private val dataSource: AdsLogsDataSource,
    private val displayer: AdsLogsDebugDisplayer,
    private val localDataSource: AdsLogsDataStore,
) : AdsLogsRepository {

    override val logs = dataSource.logs
    override val infos = dataSource.infos

    override fun addLog(log: AdsLogsRepository.Log) {
        applicationScope.launch {
            dataSource.addLog(log)
            if (localDataSource.getIsDebugLogEnabled()) {
                displayer.display(log.text)
            }
        }
    }

    override fun setInfo(info: AdsLogsRepository.Info) {
        dataSource.setInfo(info)
    }

    override val enableLogToasts: Flow<Boolean> = localDataSource.isDebugLogEnabled

    override suspend fun updateLogToasts(logToasts: Boolean) {
        localDataSource.setIsDebugLogEnabled(logToasts)
    }
}
