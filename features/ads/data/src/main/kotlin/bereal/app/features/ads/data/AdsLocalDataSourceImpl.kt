package bereal.app.features.ads.data

import android.content.Context
import bereal.app.datastores.keyed.KeyedDataStore
import bereal.app.datastores.keyed.KeyedDataStoreKeyProvider
import bereal.app.datastores.keyed.row
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

@Single
class AdsLocalDataSourceImpl(
    context: Context,
    keyedDataStoreKeyProvider: KeyedDataStoreKeyProvider,
) : AdsLocalDataSource {

    private val dataStore = KeyedDataStore(
        context = context,
        name = ADS_DATASTORE,
        keyProvider = keyedDataStoreKeyProvider,
    )

    private val rePromptLastDateRow = dataStore.row<Long>(ADS_REPROMPT_LAST_DATE_ROW)
    private val grpdConsentDescription = dataStore.row<String?>(GRPD_CONSENT_DESCRIPTION)

    override suspend fun getRePromptLastDate(): Instant? = rePromptLastDateRow.get()?.let { lastRePromptMs ->
        Instant.fromEpochMilliseconds(lastRePromptMs)
    }

    override suspend fun setRePromptLastDate(value: Instant?) {
        rePromptLastDateRow.set(value?.toEpochMilliseconds())
    }

    override suspend fun updateGrpdConsentDescription(gdprConsent: String?) {
        grpdConsentDescription.set(gdprConsent)
    }

    override suspend fun getGrpdConsentDescription(): String? {
        return grpdConsentDescription.get()
    }

    companion object {
        // random name for obfuscation / security
        private const val ADS_DATASTORE = "wsfazv"
        private const val ADS_REPROMPT_LAST_DATE_ROW = "erdxrr"
        private const val GRPD_CONSENT_DESCRIPTION = "pmolikujyuh"
    }
}
