package bereal.app.features.ads.data.adn

import bereal.app.features.ads.domain.AdnAdsRepository
import bereal.app.features.ads.domain.usecases.GetAdnStandaloneSdkTestModeUseCase
import kotlinx.coroutines.flow.Flow
import org.koin.core.annotation.Single

@Single
class AdnAdsRepositoryImpl(
    private val getAdnStandaloneSdkTestModeUseCase: GetAdnStandaloneSdkTestModeUseCase,
    private val datasource: AdnStandaloneSdkDatasource,
) : AdnAdsRepository {

    override val isSdkInitialized: Flow<Boolean> = datasource.isInitialized

    override fun initialize() {
        datasource.initialize()
    }

    override suspend fun update(
        hasUserContent: Boolean,
        doNotSell: Boolean,
    ) {
        datasource.update(
            enableTestMode = getAdnStandaloneSdkTestModeUseCase(),
            hasUserContent = hasUserContent,
            doNotSell = doNotSell,
        )
    }

    override fun getAdnSdkVersion(): String {
        return datasource.getAdnSdkVersion()
    }
}
