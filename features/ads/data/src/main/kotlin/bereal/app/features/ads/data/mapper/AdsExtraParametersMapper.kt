package bereal.app.features.ads.data.mapper

import bereal.app.entities.User
import bereal.app.features.ads.domain.models.AdsExtraParameters

internal fun AdsExtraParameters.toAdnExtraParams(): Map<String, Any> {
    return mutableMapOf<String, Any>().apply {
        userId?.let { put("bereal_user_id", it) }
        age?.let { put("age", it) }
        gender?.let {
            put(
                "gender",
                when (it) {
                    User.Gender.Female -> 1
                    User.Gender.Male -> 2
                },
            )
        }
    }
}
