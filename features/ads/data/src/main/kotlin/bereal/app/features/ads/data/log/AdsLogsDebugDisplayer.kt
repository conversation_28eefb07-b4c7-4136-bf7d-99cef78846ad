package bereal.app.features.ads.data.log

import android.content.Context
import android.widget.Toast
import bereal.app.commonandroid.injection.DispatcherProvider
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory

@Factory
internal class AdsLogsDebugDisplayer(
    private val context: Context,
    private val dispatcherProvider: DispatcherProvider,
) {
    suspend fun display(text: String) {
        withContext(dispatcherProvider.ui) {
            Toast.makeText(context, text, Toast.LENGTH_SHORT).show()
        }
    }
}
