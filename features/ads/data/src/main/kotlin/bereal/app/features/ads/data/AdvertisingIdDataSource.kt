package bereal.app.features.ads.data

import android.content.Context
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.datastores.keyed.KeyedDataStore
import bereal.app.datastores.keyed.KeyedDataStoreKeyProvider
import bereal.app.datastores.keyed.row
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory
import org.koin.core.annotation.Single

@Factory
internal class AdvertisingIdDataSource(
    private val appContext: Context,
    private val dispatcherProvider: DispatcherProvider,
    private val advertisingIdLocalDataSource: AdvertisingIdLocalDataSource,
) {
    val advertisingId = advertisingIdLocalDataSource.advertisingId

    suspend fun initializeAdvertisingId(hasUserContent: Boolean) {
        if (!hasUserContent) {
            // set the cached value directly
            advertisingIdLocalDataSource.clear()
        } else {
            // if we have consent, fetch the aa_id then store it for next usages
            // will be cleaned as soon as the user declines
            advertisingIdLocalDataSource.saveAdvertisingId(
                getAdvertisingId(),
            )
        }
    }

    private suspend fun getAdvertisingId(): String? {
        return withContext(dispatcherProvider.data) {
            try {
                val adInfo = AdvertisingIdClient.getAdvertisingIdInfo(appContext)
                if (!adInfo.isLimitAdTrackingEnabled) {
                    adInfo.id // Advertising ID
                } else {
                    null // the user refused the tracking
                }
            } catch (t: Throwable) {
                t.printStackTrace()
                null
            }
        }
    }
}

@Single
internal class AdvertisingIdLocalDataSource(
    context: Context,
    keyedDataStoreKeyProvider: KeyedDataStoreKeyProvider,
) {

    private val dataStore = KeyedDataStore(
        context = context,
        name = ADS_DATASTORE,
        keyProvider = keyedDataStoreKeyProvider,
    )

    // cache the aa_id, only if we have the user consent
    // be sure to clear it as soon as the user decline the consent
    private val advertisingIdRow = dataStore.row<String>(ADS_IFA_ROW)
    val advertisingId = advertisingIdRow.value

    suspend fun saveAdvertisingId(value: String?) {
        advertisingIdRow.set(value)
    }

    suspend fun clear() {
        saveAdvertisingId(null)
    }

    companion object {
        // random name for obfuscation / security
        private const val ADS_DATASTORE = "sdklsdklsdkss"
        private const val ADS_IFA_ROW = "sdklsdklsdks"
    }
}
