package bereal.app.features.ads.data

import android.content.Context
import bereal.app.datastores.keyed.KeyedDataStore
import bereal.app.datastores.keyed.KeyedDataStoreKeyProvider
import bereal.app.datastores.keyed.row
import org.koin.core.annotation.Single

@Single
internal class AppSetIdLocalDataSource(
    context: Context,
    keyedDataStoreKeyProvider: KeyedDataStoreKeyProvider,
) {

    private val dataStore = KeyedDataStore(
        context = context,
        name = APPSET_DATASTORE,
        keyProvider = keyedDataStoreKeyProvider,
    )

    // cache the app_set_id, only if we have the user consent
    // be sure to clear it as soon as the user decline the consent
    private val appSetIdRow = dataStore.row<String>(APPSET_ID_ROW)
    val appSetId = appSetIdRow.value

    suspend fun saveAppSetId(value: String?) {
        appSetIdRow.set(value)
    }

    suspend fun clear() {
        saveAppSetId(null)
    }

    companion object {
        private const val APPSET_DATASTORE = "askdlaksdlaks"
        private const val APPSET_ID_ROW = "askdlaksdlak"
    }
}
