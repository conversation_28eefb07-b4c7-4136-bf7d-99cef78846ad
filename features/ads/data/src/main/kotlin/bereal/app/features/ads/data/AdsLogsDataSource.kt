package bereal.app.features.ads.data

import bereal.app.features.ads.domain.AdsLogsRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single
import timber.log.Timber

@Single
class AdsLogsDataSource(
    private val applicationScope: CoroutineScope,
) {

    companion object {
        private const val TAG = "ADS_LOGS"
    }

    private val _logs = MutableStateFlow<List<AdsLogsRepository.Log>>(emptyList())
    val logs: Flow<List<AdsLogsRepository.Log>> = _logs.asStateFlow()

    private val _infos = MutableStateFlow<Map<AdsLogsRepository.Info.Type, AdsLogsRepository.Info>>(
        emptyMap(),
    )
    val infos = _infos.asStateFlow()

    fun addLog(log: AdsLogsRepository.Log) {
        applicationScope.launch {
            _logs.update { it + log }
            Timber.d("$TAG : $log")
        }
    }

    fun setInfo(info: AdsLogsRepository.Info) {
        _infos.update {
            if (it.containsKey(info.type)) {
                it // does not add
            } else {
                it + (info.type to info)
            }
        }
    }
}
