package bereal.app.features.ads.data

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import bereal.app.features.ads.domain.models.AdsAdjustEvent
import bereal.app.obfuscator.Obfuscate
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustAdRevenue
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.AdjustEvent
import com.adjust.sdk.LogLevel
import org.koin.core.annotation.Single
import timber.log.Timber

@Single
@Obfuscate
class AdjustDataSource(
    private val applicationContext: Context,
) {

    companion object {
        private const val APP_TOKEN = "lx6mxfnphf5s"
        private const val TAG = "ADJUST_SDK"
    }

    fun initializeAdjust() {
        try {
            Timber.tag(TAG).d("initializing adjust")

            val environment = if (BuildConfig.adjustSandbox) {
                AdjustConfig.ENVIRONMENT_SANDBOX
            } else {
                AdjustConfig.ENVIRONMENT_PRODUCTION
            }
            val config = AdjustConfig(applicationContext, APP_TOKEN, environment)
            if (BuildConfig.adjustSandbox) {
                config.setLogLevel(LogLevel.VERBOSE)
            }
            Adjust.initSdk(config)

            (applicationContext as? Application)?.registerActivityLifecycleCallbacks(AdjustLifecycleCallbacks())

            Timber.tag(TAG).d("adjust successfully initialized")
        } catch (t: Throwable) {
            Timber.tag(TAG).e(t)
        }
    }

    private class AdjustLifecycleCallbacks : Application.ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            // no op
        }

        override fun onActivityStarted(activity: Activity) {
            // no op
        }

        override fun onActivityResumed(activity: Activity) {
            Adjust.onResume()
        }

        override fun onActivityPaused(activity: Activity) {
            Adjust.onPause()
        }

        override fun onActivityStopped(activity: Activity) {
            // no op
        }

        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            // no op
        }

        override fun onActivityDestroyed(activity: Activity) {
            // no op
        }
    }

    fun sendRevenuePaidEvent(
        revenue: Double,
        network: String,
        adUnit: String,
        placement: String?,
    ) {
        Timber.tag(TAG).d("trackAdRevenue revenue: $revenue network: $network adUnit:$adUnit placement:$placement")

        Adjust.trackAdRevenue(
            AdjustAdRevenue("applovin_max_sdk").apply {
                setRevenue(revenue, "USD")
                adRevenueNetwork = network
                adRevenueUnit = adUnit
                adRevenuePlacement = placement
            },
        )
    }

    fun sendEvent(event: AdsAdjustEvent) {
        Adjust.trackEvent(AdjustEvent(event.id))
    }
}
