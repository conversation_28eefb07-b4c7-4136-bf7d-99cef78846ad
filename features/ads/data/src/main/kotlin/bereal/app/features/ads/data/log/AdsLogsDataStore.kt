package bereal.app.features.ads.data.log

import android.content.Context
import bereal.app.datastores.dataStore
import bereal.app.datastores.row
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
internal class AdsLogsDataStore(
    private val context: Context,
) {
    private val dataStore = context.dataStore(DEBUG_LOG_DATASTORE)
    private val isDebugLogEnabledRow = dataStore.row<Boolean>(DEBUG_LOG_ENABLED_ROW)

    companion object {
        private const val DEBUG_LOG_DATASTORE = "xoalxoad"
        private const val DEBUG_LOG_ENABLED_ROW = "cmpalxja"
    }

    suspend fun getIsDebugLogEnabled() = isDebugLogEnabledRow.get() ?: false
    val isDebugLogEnabled: Flow<Boolean> = isDebugLogEnabledRow.value.map { it ?: false }
    suspend fun setIsDebugLogEnabled(value: Boolean) = isDebugLogEnabledRow.set(value)
}
