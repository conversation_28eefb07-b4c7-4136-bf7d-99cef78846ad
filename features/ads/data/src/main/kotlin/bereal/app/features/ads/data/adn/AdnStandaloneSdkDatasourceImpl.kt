package bereal.app.features.ads.data.adn

import android.content.Context
import io.adn.sdk.publisher.AdnInitializationCallback
import io.adn.sdk.publisher.AdnInitializationStatus
import io.adn.sdk.publisher.AdnSdk
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import org.koin.core.annotation.Single
import timber.log.Timber

@Single(
    binds = [AdnStandaloneSdkDatasource::class],
)
class AdnStandaloneSdkDatasourceImpl(
    private val context: Context,
) : AdnStandaloneSdkDatasource {

    private val _isInitialized = MutableStateFlow(false)
    override val isInitialized = _isInitialized.asStateFlow()

    override fun initialize() {
        AdnSdk.initialize(
            context,
            object : AdnInitializationCallback {
                override fun onCompletion(status: AdnInitializationStatus) {
                    when (status) {
                        AdnInitializationStatus.INITIALIZED_SUCCESS -> {
                            _isInitialized.value = true
                            Timber.d("INIT ADN STANDALONE ADS STATUS SUCCESS")
                        }

                        AdnInitializationStatus.INITIALIZED_FAILURE -> {}
                        else -> {}
                    }
                }
            },
        )
    }

    override suspend fun update(
        enableTestMode: Boolean,
        hasUserContent: Boolean,
        doNotSell: Boolean,
    ) {
        setUserConsent(
            hasUserContent = hasUserContent,
            doNotSell = doNotSell,
        )

        AdnSdk.setTestMode(enableTestMode)
    }

    private fun setUserConsent(
        hasUserContent: Boolean,
        doNotSell: Boolean,
    ) {
        AdnSdk.setHasUserConsent(hasUserContent)
        AdnSdk.setDoNotSell(doNotSell)
    }

    override fun getAdnSdkVersion(): String {
        return AdnSdk.getVersion()
    }
}
