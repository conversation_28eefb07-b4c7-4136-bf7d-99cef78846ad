package bereal.app.features.ads.data

import bereal.app.entities.ads.AdsSetup
import bereal.app.features.ads.domain.AdsRepository
import bereal.app.features.ads.domain.models.AdsAdjustEvent
import bereal.app.features.ads.domain.models.AdsExtraParameters
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.datetime.Instant
import org.koin.core.annotation.Single

@Single
internal class AdsRepositoryImpl(
    private val datasource: AdSdkLocalDatsource,
    private val adjustDataSource: AdjustDataSource,
    private val adsLocalDataSource: AdsLocalDataSource,
    private val advertisingIdDataSource: AdvertisingIdDataSource,
    private val appSetIdDataSource: AppSetIdDataSource,
) : AdsRepository {

    override val advertisingID = advertisingIdDataSource.advertisingId
    override val appSetId = appSetIdDataSource.appSetId

    override suspend fun initializeAdvertisingID(hasUserContent: Boolean) {
        advertisingIdDataSource.initializeAdvertisingId(hasUserContent)
    }

    override suspend fun initializeAppSetId(hasUserContent: Boolean) {
        appSetIdDataSource.initializeAppSetId(hasUserContent)
    }

    override suspend fun init(
        adsSetup: AdsSetup,
    ) {
        // here we have already the consent given
        val isInitialized = datasource.isInitialized.first()
        if (!isInitialized) {
            datasource.init(
                adsSetup = adsSetup,
            )
            adsLocalDataSource.updateGrpdConsentDescription(adsSetup.gdprConsent)
        }
    }

    override fun initAdjust() {
        adjustDataSource.initializeAdjust()
    }

    override fun observeAdsProviderInitialized(): Flow<Boolean> {
        return datasource.isInitialized
    }

    override fun openAdsMediationDebugger() {
        datasource.openAdsMediationDebugger()
    }

    override fun sendAdsExtraParameters(adsExtraParameters: AdsExtraParameters) {
        datasource.sendAdsExtraParameters(adsExtraParameters)
    }

    override fun sendRevenuePaidEventToAdjust(
        revenue: Double,
        network: String,
        adUnit: String,
        placement: String?,
    ) {
        adjustDataSource.sendRevenuePaidEvent(
            revenue = revenue,
            network = network,
            adUnit = adUnit,
            placement = placement,
        )
    }

    override fun sendAdjustEvent(adjustEvent: AdsAdjustEvent) {
        adjustDataSource.sendEvent(event = adjustEvent)
    }

    override suspend fun getRePromptLastDate(): Instant? = adsLocalDataSource.getRePromptLastDate()

    override suspend fun setRePromptLastDate(value: Instant?) {
        adsLocalDataSource.setRePromptLastDate(value)
    }

    override suspend fun getGrpdConsentDescription(): String? {
        return adsLocalDataSource.getGrpdConsentDescription()
    }
}
