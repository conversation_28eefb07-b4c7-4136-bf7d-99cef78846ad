package bereal.app.features.ads.data

import android.content.Context
import bereal.app.commonandroid.injection.DispatcherProvider
import com.google.android.gms.appset.AppSet
import com.google.android.gms.appset.AppSetIdClient
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Factory
import kotlin.coroutines.resume

@Factory
internal class AppSetIdDataSource(
    private val appContext: Context,
    private val dispatcherProvider: DispatcherProvider,
    private val appSetIdLocalDataSource: AppSetIdLocalDataSource,
) {
    val appSetId = appSetIdLocalDataSource.appSetId

    suspend fun initializeAppSetId(hasUserContent: Boolean) {
        if (!hasUserContent) {
            // set the cached value directly
            appSetIdLocalDataSource.clear()
        } else {
            // if we have consent, fetch the AppSetId then store it for next usages
            // will be cleaned as soon as the user declines
            appSetIdLocalDataSource.saveAppSetId(
                getAppSetId(),
            )
        }
    }

    private suspend fun getAppSetId(): String? {
        return withContext(dispatcherProvider.data) {
            try {
                val client: AppSetIdClient = AppSet.getClient(appContext)

                suspendCancellableCoroutine { continuation ->
                    client.appSetIdInfo
                        .addOnSuccessListener { appSetIdInfo ->
                            if (continuation.isActive) {
                                continuation.resume(appSetIdInfo.id)
                            }
                        }
                        .addOnFailureListener { exception ->
                            if (continuation.isActive) {
                                exception.printStackTrace()
                                continuation.resume(null)
                            }
                        }
                }
            } catch (t: Throwable) {
                null
            }
        }
    }
}
