package bereal.app.features.ads.data

import bereal.app.entities.ads.AdsSetup
import bereal.app.features.ads.domain.models.AdsExtraParameters
import kotlinx.coroutines.flow.Flow

interface AdSdkLocalDatsource {

    val isInitialized: Flow<Boolean>

    suspend fun init(
        adsSetup: AdsSetup,
    )

    fun sendAdsExtraParameters(adsExtraParameters: AdsExtraParameters)

    fun openAdsMediationDebugger()
}
