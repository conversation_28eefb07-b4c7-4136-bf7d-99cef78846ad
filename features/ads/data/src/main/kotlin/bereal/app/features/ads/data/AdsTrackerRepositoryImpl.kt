package bereal.app.features.ads.data

import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import bereal.app.features.ads.domain.AdsTrackerRepository
import org.koin.core.annotation.Single

@Single
class AdsTrackerRepositoryImpl(
    private val adTrackerDataSource: AdTrackerDataSource,
) : AdsTrackerRepository {
    override suspend fun pingAdTracker(url: String): Either<GenericError, Unit> {
        return adTrackerDataSource.pingTracker(url)
    }
}
