package bereal.app.features.ads.data

import bereal.app.common.Either
import bereal.app.common.Failure
import bereal.app.common.Success
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.error.GenericError
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request

class AdTrackerDataSource(
    private val okHttpClient: OkHttpClient,
    private val dispatcherProvider: DispatcherProvider,
) {
    suspend fun pingTracker(url: String): Either<GenericError, Unit> {
        return withContext(dispatcherProvider.data) {
            try {
                val request = Request.Builder()
                    .url(url)
                    .get()
                    .build()

                okHttpClient.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        Failure(GenericError.Unhandled("Unexpected HTTP code: ${response.code}"))
                    } else {
                        Success(Unit)
                    }
                }
            } catch (t: Throwable) {
                Failure(GenericError.Unhandled("Unexpected HTTP error: ${t.message}"))
            }
        }
    }
}
