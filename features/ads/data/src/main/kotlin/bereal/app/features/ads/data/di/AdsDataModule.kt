package bereal.app.features.ads.data.di

import bereal.app.common.network.okhttp.OkHttpClientProvider
import bereal.app.features.ads.data.AdTrackerDataSource
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.dsl.module
import org.koin.ksp.generated.module

val adsDataModule = module {
    includes(AdsDataModule().module)
    factory<AdTrackerDataSource> {
        val okHttpClientProvider: OkHttpClientProvider = get()
        AdTrackerDataSource(
            okHttpClient = okHttpClientProvider.externalClient,
            dispatcherProvider = get(),
        )
    }
}

@Module
@ComponentScan("bereal.app.features.ads.data")
internal class AdsDataModule
