package bereal.app.features.ads.data

import android.annotation.SuppressLint
import android.content.Context
import bereal.app.common.resumeSafety
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.ads.AdsSetup
import bereal.app.features.ads.data.mapper.toAdnExtraParams
import bereal.app.features.ads.domain.AdsKeys
import bereal.app.features.ads.domain.AdsLogsRepository
import bereal.app.features.ads.domain.models.AdsExtraParameters
import com.appharbr.sdk.configuration.AHSdkConfiguration
import com.appharbr.sdk.engine.AppHarbr
import com.appharbr.sdk.engine.InitializationFailureReason
import com.appharbr.sdk.engine.listeners.OnAppHarbrInitializationCompleteListener
import com.applovin.mediation.MaxSegment
import com.applovin.mediation.MaxSegmentCollection
import com.applovin.sdk.AppLovinMediationProvider
import com.applovin.sdk.AppLovinPrivacySettings
import com.applovin.sdk.AppLovinSdk
import com.applovin.sdk.AppLovinSdkInitializationConfiguration
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import io.adn.sdk.publisher.AdnSdk
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import timber.log.Timber
import kotlin.coroutines.suspendCoroutine

@Single(
    binds = [AdSdkLocalDatsource::class],
)
class AdSdkLocalDatsourceImpl(
    private val context: Context,
    private val dispatcherProvider: DispatcherProvider,
    private val adsKeys: AdsKeys,
    private val applicationScope: CoroutineScope,
    private val logDataSource: AdsLogsDataSource,
) : AdSdkLocalDatsource {

    private val _isInitialized = MutableStateFlow(false)
    override val isInitialized: Flow<Boolean> = _isInitialized.asStateFlow()

    private fun getSdk(): AppLovinSdk {
        return AppLovinSdk.getInstance(context)
    }

    private suspend fun getAdvertisingId(): String? {
        return if (BuildConfig.forceDebugDevice) {
            withContext(dispatcherProvider.data) {
                try {
                    AdvertisingIdClient.getAdvertisingIdInfo(context)?.id
                } catch (t: Throwable) {
                    Timber.e(t)
                    null
                }
            }
        } else null
    }

    override suspend fun init(adsSetup: AdsSetup) {
        Timber.d("INIT_ADS $adsSetup")
        logDataSource.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.InitSdkStart,
                at = System.currentTimeMillis(),
            ),
        )
        logDataSource.addLog(
            AdsLogsRepository.Log(
                text = "Initializing SDK ${adsSetup.adsUnitIds.map { it.adUnitId }}",
                createdAt = System.currentTimeMillis(),
                type = AdsLogsRepository.Log.Type.Info,
            ),
        )
        withContext(dispatcherProvider.data) {
            val config =
                AppLovinSdkInitializationConfiguration.builder(
                    adsKeys.appLovinSdkKey,
                    context,
                )
                    .setMediationProvider(AppLovinMediationProvider.MAX)
                    .setAdUnitIds(
                        buildList {
                            addAll(adsSetup.adsUnitIds.map { it.adUnitId })
                        },
                    )
                    .setSegmentCollection(
                        MaxSegmentCollection.builder()
                            .also { segment ->
                                Timber.d("INIT_ADS with segments ${adsSetup.customTargeting.keyWords.map { it.formatted() }}")

                                adsSetup.customTargeting.keyWords.forEach { (key, value) ->
                                    segment.addSegment(MaxSegment(key, listOf(value)))
                                }
                            }
                            .build(),
                    )
                    .setTestDeviceAdvertisingIds(
                        listOfNotNull(
                            getAdvertisingId(), // only if BuildConfig.forceDebugDevice == true
                            // DEBUG : force test ads on device, put the AA_ID here
                            // "579823b0-0ce3-4847-ba62-10ca1ad1abe1", // florent redmi
                        ),
                    )
                    .build()

            getSdk().apply {
                settings.also {
                    it.setVerboseLogging(BuildConfig.appLovinLogs)
                    // Shake to see debug info about ads
                    it.isCreativeDebuggerEnabled = BuildConfig.appLovinShakeToSeeInfos
                    it.setExtraParameter("enable_black_screen_fixes", "true")
                    // DEBUG : force google
                    // it.setExtraParameter("test_mode_network", "ADMOB_BIDDING")
                }

                initialize(config) {
                    applicationScope.launch(dispatcherProvider.data) {
                        logDataSource.addLog(
                            AdsLogsRepository.Log(
                                text = "Initializing SDK - Success ${adsSetup.adsUnitIds.map { it.adUnitId }}",
                                createdAt = System.currentTimeMillis(),
                                type = AdsLogsRepository.Log.Type.Success,
                            ),
                        )
                        initializeSdks()
                    }
                }
            }

            // SDK shouldn't be initialized if we don't have user's consent
            AppLovinPrivacySettings.setHasUserConsent(true, context)
            AppLovinPrivacySettings.setDoNotSell(adsSetup.doNotSellDataEnabled, context)

            // TEMPORARY : bug in applovin sdk with mediation that do not send those values
            AdnSdk.setHasUserConsent(true)
            AdnSdk.setDoNotSell(adsSetup.doNotSellDataEnabled)
        }
    }

    private suspend fun initializeSdks() {
        initModerationSdk()

        // mark ads as enabled and ready to use in your code
        // only from context point you should instantiate AdClients
        _isInitialized.value = true

        logDataSource.setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.InitSdkEnd,
                at = System.currentTimeMillis(),
            ),
        )
    }

    override fun openAdsMediationDebugger() {
        getSdk().showMediationDebugger()
    }

    override fun sendAdsExtraParameters(adsExtraParameters: AdsExtraParameters) {
        AdnSdk.addExtraParameters(adsExtraParameters.toAdnExtraParams())
    }

    @SuppressLint("MissingPermission")
    private suspend fun initModerationSdk() = suspendCoroutine<Boolean> { continuation ->
        val ahSdkConfiguration = AHSdkConfiguration.Builder(adsKeys.appHrbr)
            // .withDebugConfig(AHSdkDebug(true).withBlockAll(true))
            .build()

        AppHarbr.initialize(
            context,
            ahSdkConfiguration,
            object : OnAppHarbrInitializationCompleteListener {
                override fun onSuccess() {
                    Timber.d("AppHarbr SDK Initialization success")
                    continuation.resumeSafety(true)
                }

                override fun onFailure(reason: InitializationFailureReason) {
                    Timber.e("AppHarbr SDK Initialization Failed: ${reason.readableHumanReason}")
                    continuation.resumeSafety(false)
                }
            },
        )
    }
}
