plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.ui.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
}

android {
    namespace = "io.voodoo.apps.ads.privacy"
}

dependencies {
    //noinspection UseTomlInstead
    implementation("com.sourcepoint.cmplibrary:cmplibrary:7.10.2")
    //noinspection UseTomlInstead
    // implementation("com.google.code.gson:gson:2.11.0")
    testImplementation(libs.bundles.tests.domain)
}
