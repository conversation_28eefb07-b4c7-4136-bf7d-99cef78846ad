package io.voodoo.apps.privacy

import com.sourcepoint.cmplibrary.model.exposed.TargetingParam
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNull

class ConsentHelperTest {

    @Test
    fun `returns null when consentedAll is true`() {
        // Given
        val result = getGrpdOutdatedConsentTargetintParam(
            consentedAll = true,
            rejectedAny = false,
            dateCreatedStr = OffsetDateTime.now().toString(),
        )
        // Then
        assertNull(result)
    }

    @Test
    fun `returns null when rejectedAny is true and date is older than 6 months`() {
        // Given
        val oldDate = OffsetDateTime.now().minusMonths(7).toString()
        // When
        val result = getGrpdOutdatedConsentTargetintParam(
            consentedAll = false,
            rejectedAny = true,
            dateCreatedStr = oldDate,
        )
        // Then
        assertNull(result)
    }

    @Test
    fun `returns outdated when rejectedAny is true and date is within 6 months`() {
        // Given
        val recentDate = OffsetDateTime.now().minusMonths(2).toString()
        // When
        val result = getGrpdOutdatedConsentTargetintParam(
            consentedAll = false,
            rejectedAny = true,
            dateCreatedStr = recentDate,
        )
        assertEquals(TargetingParam("consentStatus", "outdated"), result)
    }

    @Test
    fun `returns outdated when not consentedAll and not rejectedAny`() {
        // Given
        val recentDate = OffsetDateTime.now().toString()
        // When
        val result = getGrpdOutdatedConsentTargetintParam(
            consentedAll = false,
            rejectedAny = false,
            dateCreatedStr = recentDate,
        )
        // Then
        assertEquals(TargetingParam("consentStatus", "outdated"), result)
    }
}
