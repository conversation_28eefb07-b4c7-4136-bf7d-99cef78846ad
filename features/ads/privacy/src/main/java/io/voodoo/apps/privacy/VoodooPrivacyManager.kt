package io.voodoo.apps.privacy

import android.app.Activity
import android.util.Log
import android.view.View
import androidx.compose.ui.util.fastReduce
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.sourcepoint.cmplibrary.NativeMessageController
import com.sourcepoint.cmplibrary.SpClient
import com.sourcepoint.cmplibrary.core.nativemessage.MessageStructure
import com.sourcepoint.cmplibrary.creation.ConfigOption
import com.sourcepoint.cmplibrary.creation.config
import com.sourcepoint.cmplibrary.creation.delegate.spConsentLibLazy
import com.sourcepoint.cmplibrary.exception.CampaignType
import com.sourcepoint.cmplibrary.model.ConsentAction
import com.sourcepoint.cmplibrary.model.PMTab
import com.sourcepoint.cmplibrary.model.exposed.ActionType
import com.sourcepoint.cmplibrary.model.exposed.SPConsents
import com.sourcepoint.cmplibrary.model.exposed.SpCampaign
import com.sourcepoint.cmplibrary.model.exposed.SpConfig
import com.sourcepoint.cmplibrary.util.clearAllData
import io.voodoo.apps.privacy.config.CmpPurpose
import io.voodoo.apps.privacy.config.CmpPurposeHelper
import io.voodoo.apps.privacy.config.CmpVendorHelper
import io.voodoo.apps.privacy.config.SourcepointConfiguration
import io.voodoo.apps.privacy.model.VoodooPrivacyConsent
import kotlinx.serialization.json.Json
import org.json.JSONObject
import java.lang.ref.WeakReference

/**
 *
 */
class VoodooPrivacyManager(
    lifecycleOwner: LifecycleOwner,
    private val currentActivity: Activity,
    private var autoShowPopup: Boolean = true,
    private val sourcepointConfiguration: SourcepointConfiguration,
    private var onConsentReceived: ((VoodooPrivacyConsent) -> Unit)? = null,
    private var onUiReady: (() -> Unit)? = null,
    private var onError: ((Throwable) -> Unit)? = null,
    private var onStatusUpdate: ((ConsentStatus) -> Unit)? = null,
    private val getPrivacyOutdatedRepromptFlag: () -> Boolean,
) : DefaultLifecycleObserver {

    private var doNotSellDataEnabled = false
    private var forceAutoShow = false
    private var consentStatus: ConsentStatus = ConsentStatus.NA
    private var receivedConsent: SPConsents? = null
    private var isInitializing = false

    init {
        lifecycleOwner.lifecycle.addObserver(this)
        if (weakRefConsent?.get() != null)
            receivedConsent = weakRefConsent?.get()!!
    }

    private val cmpConfig: SpConfig = config {
        accountId = sourcepointConfiguration.accountId
        propertyId = sourcepointConfiguration.propertyId
        propertyName = sourcepointConfiguration.propertyName
        messLanguage = VoodooPrivacyLanguageMapper.getLanguage()
        addCampaign(
            SpCampaign(
                CampaignType.GDPR,
                getGrpdTargetingParam(
                    currentActivity,
                    privacyOutdatedRepromptFlagEnabled = getPrivacyOutdatedRepromptFlag(),
                ),
            ),
        )
        +CampaignType.USNAT to setOf(ConfigOption.TRANSITION_CCPA_AUTH)
    }

    private val spConsentLib by spConsentLibLazy {
        activity = currentActivity
        spClient = LocalClient()
        spConfig = cmpConfig
    }

    private var viewToShow: View? = null

    private fun loadMessage() {
        setConsentStatus(ConsentStatus.LOADING)
        spConsentLib.loadMessage()
    }

    /**
     * Show consent edit settings
     */
    fun changePrivacyConsent() {
        if (!isFirstMessageLoaded()) {
            Log.w("PrivacyManager", "Privacy -- first message is not loaded yet")
            return
        }

        if (isPrivacyApplies()) {
            forceAutoShow = true
            setConsentStatus(ConsentStatus.LOADING)
            if (isUsNatApplicable()) {
                spConsentLib.loadPrivacyManager(
                    sourcepointConfiguration.usMspsPrivacyManagerId,
                    PMTab.PURPOSES,
                    CampaignType.USNAT,
                )
            } else {
                spConsentLib.loadPrivacyManager(
                    sourcepointConfiguration.gdprPrivacyManagerId,
                    PMTab.PURPOSES,
                    CampaignType.GDPR,
                )
            }
        } else {
            Log.w("PrivacyManager", "Privacy -- not available in your country")
        }
    }

    /**
     * Close consent edit setting
     *
     * @return true if it was closed
     */
    fun closeIfVisible(): Boolean {
        return viewToShow?.let {
            spConsentLib.removeView(it)
            setConsentStatus(ConsentStatus.NA)
            viewToShow = null
            true
        } ?: false
    }

    /**
     * Initialize the consent manager and download the FTL message
     */
    fun initializeConsent() {
        if ((isPrivacyInitialized && receivedConsent != null) || isInitializing) {
            return
        }

        isInitializing = true
        loadMessage()
    }

    fun rejectAllConsentsIfNeeded(isRejectConsentExperimentEnabled: Boolean) {
        if (isRejectConsentExperimentEnabled) {
            spConsentLib.rejectAll(CampaignType.GDPR)
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        onConsentReceived = null
        onStatusUpdate = null
        onError = null
        onUiReady = null
        spConsentLib.dispose()
        super.onDestroy(owner)
    }

    /**
     * Set the onConsentReady callback, onConsentReady will be called once the client received
     * the saved consent
     */
    fun setOnConsentReady(onConsentReceived: ((VoodooPrivacyConsent) -> Unit)?) {
        this.onConsentReceived = onConsentReceived
    }

    /**
     *
     */
    fun setOnStatusUpdate(onStatusUpdate: (ConsentStatus) -> Unit) {
        this.onStatusUpdate = onStatusUpdate
    }

    private fun computePersonalizedAdvertisingGroup(): Boolean {
        // personalizedAdvertising is enabled only if all these 10 sub items are enabled
        // see https://portal.sourcepoint.com/consent_v2/vendor_lists/edit?id=6656fcd54f9aee2746149a8b&non_iab=false

        val useLimitedDataForAdvertising =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e562c") // Use limited data to select advertising
        val createProfilesForPersonalisedAdvertising =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e55e7") // Create profiles for personalised advertising
        val useProfilesForPersonalisedAdvertising =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e557e") // Use profiles to select personalised advertising
        val createProfilesToPersonaliseContent =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e5574") // Create profiles to personalise content
        val useProfilesToSelectPersonalisedContent =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e556a") // Use profiles to select personalised content
        val measureAdvertisingPerformance =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e5415") // Measure advertising performance
        val measureContentPerformance =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e5490") // Measure content performance
        val understandAudiencesThroughStatistics =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e54a9") // Understand audiences through statistics or combinations of data from different sources
        val developAndImproveServices =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e54f3") // Develop and improve services
        val useLimitedDataForContent =
            CmpPurposeHelper.get("6656fcd5a0fa9305065e5561") // Use limited data to select content

        return try {
            listOf(
                useLimitedDataForAdvertising,
                createProfilesForPersonalisedAdvertising,
                useProfilesForPersonalisedAdvertising,
                createProfilesToPersonaliseContent,
                useProfilesToSelectPersonalisedContent,
                measureAdvertisingPerformance,
                measureContentPerformance,
                understandAudiencesThroughStatistics,
                developAndImproveServices,
                useLimitedDataForContent,
            ).fastReduce { a, b -> a && b }
        } catch (t: Throwable) {
            false
        }
    }

    fun getPrivacyConsent(): VoodooPrivacyConsent {
        val personalizedAdvertisingGroup =
            computePersonalizedAdvertisingGroup() // use the 10 sub items to compute this one
        val storeAndAccessInfoOnDevice =
            CmpPurposeHelper.get(CmpPurpose.STORE_AND_ACCESS_INFO_ON_DEVICE)
        val dataSharingWithVoodooConsentGiven =
            CmpPurposeHelper.get(CmpPurpose.DATA_SHARING_WITH_VOODOO_CONSENT_GIVEN)

        val gdprConsentDescriptionJson =
            receivedConsent?.gdpr?.consent?.tcData[GDPR_CONSENT_DESCRIPTION_KEY]
        val gdprConsentDescription = gdprConsentDescriptionJson?.let {
            try {
                Json.decodeFromString<String>(it.toString())
            } catch (t: Throwable) {
                // cannot deserialize this json wrapped string, send it like this
                it.toString()
            }
        }

        return VoodooPrivacyConsent(
            adConsent = storeAndAccessInfoOnDevice && personalizedAdvertisingGroup,
            analyticsConsent = storeAndAccessInfoOnDevice &&
                CmpPurposeHelper.get(CmpPurpose.MEASURE_CONTENT_PERFORMANCE) &&
                CmpPurposeHelper.get(CmpPurpose.DEVELOP_AND_IMPROVE_SERVICE) &&
                CmpPurposeHelper.get(CmpPurpose.GATHER_AUDIENCE_STATISTICS),
            doNotSellDataEnabled = doNotSellDataEnabled,
            gdprApplicable = isGdprApplicable(),
            storeAccessDataConsent = storeAndAccessInfoOnDevice,
            personalisedAdvertising = personalizedAdvertisingGroup,
            dataSharingWithVoodooConsentGiven = dataSharingWithVoodooConsentGiven,
            grpdUUId = receivedConsent?.gdpr?.consent?.uuid,
            gdprConsentDescription = gdprConsentDescription?.toString(),
        )
    }

    fun isFirstMessageLoaded(): Boolean {
        return receivedConsent != null
    }

    fun isPrivacyApplies(): Boolean {
        return isGdprApplicable() || isUsNatApplicable() || isCcpaApplicable()
    }

    private fun isGdprApplicable(): Boolean {
        return receivedConsent?.gdpr?.consent?.applies == true
    }

    private fun isUsNatApplicable(): Boolean {
        return receivedConsent?.usNat?.consent?.applies == true
    }

    private fun isCcpaApplicable(): Boolean {
        return receivedConsent?.ccpa?.consent?.applies == true
    }

    fun getStatus(): ConsentStatus {
        return consentStatus
    }

    private fun processConsent(consent: SPConsents) {
        // The SDK will set consentedToAll as True if user allow us to sell / share their data
        // It will return false if user tick the Do Not Sell / Share my data
        if (consent.usNat?.consent?.statuses?.consentedToAll == false) {
            doNotSellDataEnabled = true
        }

        val gdprGrants = consent.gdpr?.consent?.grants ?: mapOf()
        CmpPurposeHelper.setInitialized()
        CmpVendorHelper.setInitialized()
        gdprGrants.entries.forEach { grant ->
            CmpVendorHelper.put(grant.key, grant.value.granted)
            grant.value.purposeGrants.forEach { purpose ->
                CmpPurposeHelper.put(purpose.key, purpose.value)
            }
        }
    }

    private fun setConsentStatus(status: ConsentStatus) {
        consentStatus = if (status == ConsentStatus.RECEIVED && !isPrivacyApplies()) {
            ConsentStatus.NON_APPLICABLE
        } else
            status
        onStatusUpdate?.invoke(consentStatus)
        if (status == ConsentStatus.RECEIVED || status == ConsentStatus.ERROR) {
            isInitializing = false
            if (status == ConsentStatus.RECEIVED) isPrivacyInitialized = true
        }
    }

    fun clearConsent() {
        clearAllData(context = currentActivity)
        receivedConsent = null
        weakRefConsent?.clear()
        setConsentStatus(ConsentStatus.NA)
    }

    @Suppress("EmptyFunctionBlock")
    internal inner class LocalClient : SpClient {
        override fun onUIFinished(view: View) {
            viewToShow = null
            spConsentLib.removeView(view)
        }

        override fun onUIReady(view: View) {
            viewToShow = view
            setConsentStatus(ConsentStatus.UI_READY)
            if (autoShowPopup || forceAutoShow) {
                setConsentStatus(ConsentStatus.UI_SHOWN)
                spConsentLib.showView(view)
                forceAutoShow = false
            }
            onUiReady?.invoke()
        }

        override fun onNativeMessageReady(
            message: MessageStructure,
            messageController: NativeMessageController,
        ) {
        }

        override fun onError(error: Throwable) {
            setConsentStatus(ConsentStatus.ERROR)
            onError?.invoke(error)
        }

        @Deprecated("Will be removed in next version of SP")
        override fun onMessageReady(message: JSONObject) {
        }

        override fun onConsentReady(consent: SPConsents) {
            processConsent(consent)
            receivedConsent = consent
            if (weakRefConsent?.get() == null) {
                weakRefConsent = WeakReference(consent)
            }

            setConsentStatus(ConsentStatus.RECEIVED)
            onConsentReceived?.invoke(getPrivacyConsent())
        }

        override fun onAction(view: View, consentAction: ConsentAction): ConsentAction {
            if (consentAction.actionType == ActionType.SHOW_OPTIONS && !autoShowPopup) {
                forceAutoShow = true
            }
            return consentAction
        }

        override fun onNoIntentActivitiesFound(url: String) {
        }

        override fun onSpFinished(sPConsents: SPConsents) {
        }
    }

    enum class ConsentStatus {
        NA,
        NON_APPLICABLE,
        LOADING,
        UI_READY,
        UI_SHOWN,
        ERROR,
        RECEIVED,
    }

    companion object {
        var isPrivacyInitialized = false
        var weakRefConsent: WeakReference<SPConsents>? = null

        const val GDPR_CONSENT_DESCRIPTION_KEY = "IABTCF_TCString"
    }
}
