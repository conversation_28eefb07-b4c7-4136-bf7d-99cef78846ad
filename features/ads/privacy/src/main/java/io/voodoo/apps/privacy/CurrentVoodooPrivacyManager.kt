package io.voodoo.apps.privacy

import org.koin.core.annotation.Single
import java.lang.ref.WeakReference

@Single
class CurrentVoodooPrivacyManager {

    private var ref: WeakReference<VoodooPrivacyManager?> = WeakReference(null)
    val value: VoodooPrivacyManager?
        get() = ref.get()

    fun set(voodooConsentManager: VoodooPrivacyManager) {
        ref = WeakReference(voodooConsentManager)
    }
}
