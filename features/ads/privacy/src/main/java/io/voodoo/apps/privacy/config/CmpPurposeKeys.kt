package io.voodoo.apps.privacy.config

/**
 * Maintain the configuration of the purpose in this classes
 */
enum class CmpPurpose(private val key: String) : BaseCmpEnum {
    STORE_AND_ACCESS_INFO_ON_DEVICE("6656fcd5a0fa9305065e56a3"),
    DATA_SHARING_WITH_VOODOO_CONSENT_GIVEN("6777ba5d67aa0906190c1ac3"),

// analytics
    MEASURE_CONTENT_PERFORMANCE("6656fcd5a0fa9305065e5490"),
    GATHER_AUDIENCE_STATISTICS("6656fcd5a0fa9305065e54a9"),
    DEVELOP_AND_IMPROVE_SERVICE("6656fcd5a0fa9305065e54f3"),
    ;

    override fun getKey(): String {
        return key
    }
}

object CmpPurposeHelper : BaseCmpHelper<CmpPurpose>()
