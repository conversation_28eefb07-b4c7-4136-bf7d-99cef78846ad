package io.voodoo.apps.privacy

import android.content.Context
import android.content.SharedPreferences
import android.preference.PreferenceManager
import com.sourcepoint.cmplibrary.model.exposed.TargetingParam
import org.json.JSONObject
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

fun getGrpdTargetingParam(
    context: Context,
    privacyOutdatedRepromptFlagEnabled: Boolean,
): List<TargetingParam> {
    return if (privacyOutdatedRepromptFlagEnabled) {
        listOfNotNull(
            getGrpdOutdatedConsentTargetingParam(
                PreferenceManager.getDefaultSharedPreferences(
                    context,
                ),
            ),
        )
    } else {
        return emptyList()
    }
}

internal fun getGrpdOutdatedConsentTargetingParam(preferences: SharedPreferences): TargetingParam? {
    val consentStatusJson = preferences.getString("sp.gdpr.key.consent.status", null)
    if (consentStatusJson.isNullOrBlank()) {
        return null
    }

    return try {
        val obj = JSONObject(consentStatusJson)
        val consentStatus = obj.getJSONObject("consentStatus")
        val consentedAll = consentStatus.optBoolean("consentedAll", false)
        val rejectedAny = consentStatus.optBoolean("rejectedAny", false)
        val dateCreatedStr = obj.optString("dateCreated")

        getGrpdOutdatedConsentTargetintParam(
            consentedAll = consentedAll,
            rejectedAny = rejectedAny,
            dateCreatedStr = dateCreatedStr,
        )
    } catch (t: Throwable) {
        return null // fallback
    }
}

internal fun getGrpdOutdatedConsentTargetintParam(
    consentedAll: Boolean,
    rejectedAny: Boolean,
    dateCreatedStr: String,
): TargetingParam? {
    val dateCreated = OffsetDateTime.parse(dateCreatedStr, DateTimeFormatter.ISO_DATE_TIME)
    val sixMonthsAgo = OffsetDateTime.now().minusMonths(6)

    return if (consentedAll) {
        null
    } else if (rejectedAny && dateCreated.isBefore(sixMonthsAgo)) {
        null
    } else {
        TargetingParam("consentStatus", "outdated")
    }
}
