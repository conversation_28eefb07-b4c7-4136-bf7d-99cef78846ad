plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
    alias(libs.plugins.ksp)
}

android {
    namespace = "bereal.app.features.ads.applovin"
}

dependencies {
    implementation(libs.bundles.koin)

    implementation(projects.features.ads.adsApi)
    implementation(projects.platform.config)
    implementation(projects.platform.settings.domain)
    implementation(libs.timber)
    api(libs.applovin)
    api(libs.adn.sdk)
    implementation(libs.appharbr.android.sdk)

    ksp(libs.koin.ksp.compiler)
}
