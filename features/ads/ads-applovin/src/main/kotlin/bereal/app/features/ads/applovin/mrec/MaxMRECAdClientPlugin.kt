package bereal.app.features.ads.applovin.mrec

import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import java.io.Closeable

interface MaxMRECAdClientPlugin : Closeable {

    suspend fun onPreLoadAd(adView: MaxAdView)
    suspend fun onAdLoadException(adView: MaxAd<PERSON>iew, error: MaxError)
    suspend fun onAdLoaded(adView: MaxAdView, ad: MRECAd)

    fun onDestroyAd(ad: MRECAd)
}
