package bereal.app.features.ads.applovin.nativ

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.MainThread
import androidx.core.view.doOnNextLayout
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.util.removeFromParent
import java.util.Date
import java.util.concurrent.atomic.AtomicInteger
import kotlin.random.Random

class FakeNativeAd(
    val adId: String,
    override val loadedAt: Date,
    override val longLifeAds: Boolean,
) : Ad() {

    companion object {
        private val adCounter = AtomicInteger(0)
        fun createAdId(): Int = adCounter.incrementAndGet()
    }

    override val type: Type = Type.NATIVE

    override val id: Id = Id(adId)
    override val info: Info = Info(
        adUnit = "fakeAdUnit",
        network = "fakeNetwork",
        revenue = 0.01,
        revenuePrecision = "0",
        cohortId = "fakeCohort",
        creativeId = "fakeCreativeId",
        creativeType = "fakeCreativeType",
        campaignId = "fakeCampaignId",
        placement = "fakePlacement",
        reviewCreativeId = "fakeReviewCreativeId",
        formatLabel = "fakeFormatLabel",
        requestLatencyMillis = 100,
        networkPlacement = "fakeNetworkPlacement",
        adProvider = "fakeAdProvider",
    )

    override val moderationResult: ModerationResult? = null

    override val isExpired: Boolean
        get() = false

    internal var view: View? = null
        private set

    fun colorFromAdId(adId: String): Int {
        val hash = Random(adId.hashCode() * 30).nextInt()
        val r = (hash shr 16 and 0xFF)
        val g = (hash shr 8 and 0xFF)
        val b = (hash and 0xFF)
        return Color.rgb(r, g, b)
    }

    private fun createView(context: Context, displayedInList: Boolean): View {
        return FrameLayout(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                900,
            )
            setBackgroundColor(Color.GRAY)

            background = GradientDrawable().apply {
                shape = GradientDrawable.RECTANGLE
                setColor(colorFromAdId(adId))
                setStroke(
                    TypedValue.applyDimension(
                        TypedValue.COMPLEX_UNIT_DIP,
                        2f,
                        context.resources.displayMetrics,
                    ).toInt(),
                    Color.WHITE,
                )
                cornerRadius = TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP,
                    20f,
                    context.resources.displayMetrics,
                )
            }

            val textContainer = LinearLayout(context).apply {
                orientation = LinearLayout.VERTICAL
                gravity = Gravity.CENTER

                val labelView = TextView(context).apply {
                    text = "fake ad"
                    setTextColor(Color.WHITE)
                    setTypeface(null, Typeface.BOLD)
                    textAlignment = TextView.TEXT_ALIGNMENT_CENTER
                    textSize = 14f
                    gravity = Gravity.CENTER
                    setShadowLayer(4f, 2f, 2f, Color.BLACK)
                }

                val textView = TextView(context).apply {
                    text = "ad id : $adId"
                    setTextColor(Color.WHITE)
                    setTypeface(null, Typeface.BOLD)
                    textAlignment = TextView.TEXT_ALIGNMENT_CENTER
                    gravity = Gravity.CENTER
                    setShadowLayer(4f, 2f, 2f, Color.BLACK)
                }

                addView(labelView)
                addView(textView)
            }

            addView(
                textContainer,
                FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.WRAP_CONTENT,
                    FrameLayout.LayoutParams.WRAP_CONTENT,
                ).apply {
                    gravity = Gravity.CENTER
                },
            )
        }
    }

    @MainThread
    override fun getView(context: Context, displayedInList: Boolean): View {
        // remove from previous parent view
        releaseAdView()

        val view: View = this.view ?: run {
            createView(context = context, displayedInList = displayedInList)
                .also { this.view = it }
        }

        markAsRendered()
        markAsRevenuePaid()

        view.doOnNextLayout {
            view.rootView.requestLayout() // try to fix the impression count on admob
        }

        return view
    }

    @MainThread
    protected override fun releaseAdView() {
        view?.removeFromParent()
        view = null
    }
}
