package bereal.app.features.ads.applovin.mrec

import android.app.Activity
import android.graphics.Color
import android.util.Log
import android.view.ViewGroup
import androidx.annotation.MainThread
import androidx.lifecycle.LifecycleOwner
import bereal.app.features.ads.api.AdClient
import bereal.app.features.ads.api.LocalExtrasProvider
import bereal.app.features.ads.api.listener.AdListeners
import bereal.app.features.ads.api.log.AdsLogger
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.api.model.AdAlreadyLoadingException
import bereal.app.features.ads.api.model.AdRequestId
import bereal.app.features.ads.api.model.FetchFailInfo
import bereal.app.features.ads.applovin.domain.ComputeLongLifeAdsUseCase
import bereal.app.features.ads.applovin.exception.MaxAdLoadException
import bereal.app.features.ads.applovin.listener.DefaultMaxAdViewAdListener
import bereal.app.features.ads.applovin.listener.MultiMaxAdViewAdListener
import bereal.app.features.ads.applovin.util.PreviewMaxAd
import bereal.app.features.ads.applovin.util.id
import com.appharbr.sdk.engine.AdBlockReason
import com.appharbr.sdk.engine.AdResult
import com.appharbr.sdk.engine.AdSdk
import com.appharbr.sdk.engine.AdStateResult
import com.appharbr.sdk.engine.AppHarbr
import com.appharbr.sdk.engine.listeners.AHListener
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdFormat
import com.applovin.mediation.MaxAdViewAdListener
import com.applovin.mediation.MaxError
import com.applovin.mediation.ads.MaxAdView
import com.applovin.sdk.AppLovinSdkUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Date
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

class MaxMRECAdClient(
    override val clientDisplayName: String,
    override val config: AdClient.Config,
    private val activity: Activity,
    private val plugins: List<MaxMRECAdClientPlugin> = emptyList(),
    override val listeners: AdListeners,
    private val localExtrasProviders: List<LocalExtrasProvider> = emptyList(),
    private val longLifeAdsUseCase: ComputeLongLifeAdsUseCase,
) : AdClient {

    override val adType: Ad.Type = Ad.Type.MREC

    private val loadedAds = mutableListOf<MRECAd>()

    private val fetchAdMutex = Mutex()

    private val useModeration by lazy { AppHarbr.isInitialized() }
    private val appharbrListener: AHListener

    private val maxAdViewListener = MultiMaxAdViewAdListener()

    init {
        appharbrListener = AHListener { infos ->
            markAdAsBlocked(
                view = (infos?.view as? MaxAdView) ?: return@AHListener,
                reasons = infos.blockReasons,
            )
        }
    }

    override var lastFailInfo: FetchFailInfo? = null

    fun addMaxAdViewListener(listener: MaxAdViewAdListener) {
        maxAdViewListener.add(listener)
    }

    fun removeMaxAdViewListener(listener: MaxAdViewAdListener) {
        maxAdViewListener.remove(listener)
    }

    @MainThread
    override fun getAnyAdReadyToBeInserted(): MRECAd? {
        return loadedAds
            .firstOrNull { it.isAvailableToBeInserted() }
    }

    override suspend fun getAdsReadyToBeInserted(): List<Ad> {
        return withContext(Dispatchers.Main) {
            loadedAds
                .filter { it.isAvailableToBeInserted() }
        }
    }

    @MainThread
    override fun destroyClient() {
        Timber.tag("AdClient").w("destroyClient()")
        loadedAds.forEach(::destroyAd)
        loadedAds.clear()
        runPlugin { it.close() }
    }

    private fun destroyAd(ad: MRECAd) {
        Timber.tag("AdClient").w("destroyAd " + ad.id)
        if (useModeration) {
            AppHarbr.removeBannerView(ad.view)
        }
        runPlugin { it.onDestroyAd(ad) }
        ad.view.destroy()
    }

    @Throws(Exception::class)
    override suspend fun fetchAd(localExtras: List<Pair<String, Any>>): Ad {
        return if (fetchAdMutex.tryLock()) {
            try {
                fetchAdSafe(localExtras)
            } finally {
                fetchAdMutex.unlock()
            }
        } else {
            throw AdAlreadyLoadingException()
        }
    }

    /** see https://developers.applovin.com/en/android/ad-formats/banner-mrec-ads/ */
    @Throws(Exception::class)
    private suspend fun fetchAdSafe(localExtras: List<Pair<String, Any>>): Ad {
        listeners.runLoadingListeners { it.onAdLoadingStarted(this) }

        val reusedAd = getAdsToDestroy().firstOrNull()

        val view = reusedAd?.view ?: createView(activity).apply {
            // see https://developers.applovin.com/en/android/ad-formats/banner-mrec-ads#stopping-and-starting-auto-refresh
            setExtraParameter("allow_pause_auto_refresh_immediately", "true")
            stopAutoRefresh()
        }

        val providersExtras = localExtrasProviders.flatMap { it.getLocalExtras() }
        val ad = withContext(Dispatchers.IO) {
            try {
                runPlugin { it.onPreLoadAd(view) }

                // Wrap ad loading into a coroutine
                suspendCancellableCoroutine<MRECAd> { continuation ->
                    val callback = object : DefaultMaxAdViewAdListener() {
                        override fun onAdLoaded(ad: MaxAd) {
                            maxAdViewListener.remove(this)
                            val adWrapper = MRECAd(
                                ad = ad,
                                view = view,
                                loadedAt = Date(),
                                longLifeAds = longLifeAdsUseCase(),
                            )
                            try {
                                continuation.resume(adWrapper)
                            } catch (e: Exception) {
                                // Avoid crashes if callback is called multiple times
                                Log.e("MaxMRECAdClient", "Failed to notify fetchAd", e)
                            }
                        }

                        override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
                            maxAdViewListener.remove(this)
                            try {
                                continuation.resumeWithException(MaxAdLoadException(error))
                            } catch (e: Exception) {
                                // Avoid crashes if callback is called multiple times
                                Log.e("MaxMRECAdClient", "Failed to notify fetchAd error", e)
                            }
                        }
                    }

                    Log.i("MaxMRECAdClient", "fetchAd")
                    maxAdViewListener.add(callback)
                    providersExtras.forEach { (key, value) ->
                        view.setLocalExtraParameter(key, value)
                    }
                    localExtras.forEach { (key, value) ->
                        view.setLocalExtraParameter(key, value)
                    }
                    view.loadAd()

                    continuation.invokeOnCancellation {
                        maxAdViewListener.remove(callback)
                    }
                }
            } catch (e: MaxAdLoadException) {
                Log.e("MaxMRECAdClient", "Failed to load ad", e)
                runPlugin { it.onAdLoadException(view, e.error) }
                listeners.runLoadingListeners { it.onAdLoadingFailed(this@MaxMRECAdClient, e) }

                // Keep reused ad instead of destroying it
                // If none, add to pool with a MaxDummyAd to re-use the same view next call
                val ad = reusedAd ?: MRECAd(
                    ad = PreviewMaxAd(adUnit = config.adUnit, format = MaxAdFormat.MREC),
                    view = view,
                    loadedAt = Date(),
                    longLifeAds = longLifeAdsUseCase(),
                )
                ad.isFailure = true
                addLoadedAd(ad, isAlreadyServed = reusedAd != null)

                throw e
            }
        }

        ad.isFailure = false
        runPlugin { it.onAdLoaded(view, ad) }
        Log.i("MaxMRECAdClient", "fetchAd success")
        addLoadedAd(ad)
        listeners.runLoadingListeners { it.onAdLoadingFinished(this, ad) }
        return ad
    }

    private suspend fun createView(activity: Activity): MaxAdView {
        return withContext(Dispatchers.Main.immediate) {
            MaxAdView(config.adUnit, MaxAdFormat.MREC, activity).apply {
                val view = this
                val widthPx = AppLovinSdkUtils.dpToPx(activity, 300)
                val heightPx = AppLovinSdkUtils.dpToPx(activity, 250)
                layoutParams = ViewGroup.LayoutParams(widthPx, heightPx)

                setBackgroundColor(Color.TRANSPARENT)
                setListener(maxAdViewListener)
                setRevenueListener { ad ->
                    val adWrapper = findOrCreateAdWrapperForAnalytics(ad, view)
                    adWrapper.markAsRevenuePaid()
                    listeners.runRevenueListener {
                        it.onAdRevenuePaid(this@MaxMRECAdClient, adWrapper)
                    }
                }

                // Re-wrap the multi listener with another layer to have a specific AdClick listener
                // because we need the view instance to call findOrCreateWrapperAd :facepalm:
                val wrappedListener = MultiMaxAdViewAdListener().apply {
                    add(object : DefaultMaxAdViewAdListener() {
                        override fun onAdClicked(ad: MaxAd) {
                            val adWrapper = findOrCreateAdWrapperForAnalytics(ad, view)
                            listeners.runClickListener {
                                it.onAdClick(
                                    this@MaxMRECAdClient,
                                    adWrapper,
                                )
                            }
                        }
                    })
                    add(maxAdViewListener)
                }
                setListener(wrappedListener)

                config.placement?.let { placement = it }

                if (useModeration) {
                    AppHarbr.addBannerView(
                        AdSdk.MAX,
                        this,
                        (activity as LifecycleOwner).lifecycle,
                        appharbrListener,
                    )
                }
            }
        }
    }

    // TODO: the ad value could change before apphrbr listener call
    //  thus calling this listener with incorrect ad
    @MainThread
    private fun markAdAsBlocked(view: MaxAdView, reasons: Array<out AdBlockReason>) {
        Log.e(
            "MaxMRECAdClient",
            "Ad blocked: ${reasons.joinToString { it.reason }}",
        )
        val ad = synchronized(loadedAds) {
            loadedAds.firstOrNull {
                it.view === view
            }
        } ?: return

        // Ad was already moderated, drop event
        if (ad.apphrbrModerationResult != null) return

        val moderationResult = AdResult(AdStateResult.BLOCKED).apply {
            blockReasons.addAll(reasons)
        }
        ad.apphrbrModerationResult = moderationResult
        listeners.runModerationListener { it.onAdBlocked(this, ad) }
    }

    private inline fun runPlugin(body: (MaxMRECAdClientPlugin) -> Unit) {
        plugins.forEach {
            // try/catch plugin to not crash if an error occurs
            try {
                body(it)
            } catch (e: Exception) {
                Log.e("MaxMRECAdClient", "Failed to run plugin", e)
            }
        }
    }

    @MainThread
    private fun findOrCreateAdWrapperForAnalytics(ad: MaxAd, view: MaxAdView): MRECAd {
        return loadedAds.firstOrNull { it.ad === ad }
            ?: MRECAd(
                ad = ad,
                view = view,
                loadedAt = Date(),
                longLifeAds = longLifeAdsUseCase(),
            )
    }

    override suspend fun hasAdReadyToBeInserted(): Boolean {
        return withContext(Dispatchers.Main) {
            getAnyAdReadyToBeInserted() != null
        }
    }

    @MainThread
    override fun getAdFromRequestId(requestId: AdRequestId): MRECAd? {
        // Try to find the previously returned ad for this request id
        return loadedAds
            .firstOrNull { it.isAssignedToRequest(requestId) }
            ?.takeIf {
                it.canBeServed() && !it.isCurrentlyRendering()
            }
    }

    @MainThread
    override fun getAdToDisplay(
        requestId: AdRequestId,
        revenueThreshold: Double,
    ): MRECAd? {
        // Return previously served ad for the same requestId if still exists
        val previousAd = getAdFromRequestId(requestId)
        if (previousAd != null) {
            return previousAd
        }

        // Take first ad ready for display that matches revenue threshold
        return loadedAds.firstOrNull {
            it.isAvailableToBeInserted() && it.info.revenue >= revenueThreshold
        }
    }

    @MainThread
    override fun getAdsAlreadyDisplayedAndPaid(): List<Ad> {
        return loadedAds
            .filter {
                it.isAlreadyDisplayedAndPaid()
            }
    }

    @MainThread
    override fun releaseAdView(ad: Ad) {
        AdsLogger.info(this, "releaseAdView[${ad.id}]")

        ad.endToDisplay()
    }

    private suspend fun addLoadedAd(ad: MRECAd, isAlreadyServed: Boolean = false) {
        withContext(Dispatchers.Main) {
            if (isAlreadyServed) {
                loadedAds.add(0, ad)
            } else {
                loadedAds.add(ad)
            }
        }
    }

    @MainThread
    override fun getRequestIdFromAd(adId: Ad.Id): String? {
        return loadedAds.find { it.ad.id == adId }?.isDisplayingForRequestId
    }

    /** unsafe threading, call in `syncrhonized(loadedAds)` block */
    override fun destroyAds(adsToDestroy: List<Ad>) {
        loadedAds.removeAll(adsToDestroy.toSet())
        adsToDestroy.forEach { it.endToDisplay() }
        adsToDestroy.filterIsInstance<MRECAd>().forEach(::destroyAd)
    }

    /** unsafe threading, call in `syncrhonized(loadedAds)` block */
    private suspend fun getAdsToDestroy(): List<MRECAd> {
        return withContext(Dispatchers.Main) {
            loadedAds.filter {
                !it.canBeServed() && !it.isCurrentlyRendering()
            }
        }
    }
}
