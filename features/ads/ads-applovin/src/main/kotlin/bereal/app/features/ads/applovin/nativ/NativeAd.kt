package bereal.app.features.ads.applovin.nativ

import android.content.Context
import android.view.View
import androidx.annotation.MainThread
import androidx.core.view.doOnNextLayout
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.util.buildInfo
import bereal.app.features.ads.applovin.util.id
import bereal.app.features.ads.applovin.util.removeFromParent
import bereal.app.features.ads.applovin.util.toModerationResult
import com.appharbr.sdk.engine.AdResult
import com.applovin.mediation.MaxAd
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import java.util.Date

class NativeAd internal constructor(
    val ad: MaxAd,
    internal val loader: MaxNativeAdLoader,
    private val viewFactory: MaxNativeAdViewFactory,
    private val renderListener: NativeAdsRenderListener,
    internal val apphrbrModerationResult: AdResult? = null,
    override val loadedAt: Date,
    override val longLifeAds: Boolean,
) : Ad() {

    override val type: Type = Type.NATIVE

    override val id: Id = ad.id
    override val info: Info = ad.buildInfo()

    override val moderationResult: ModerationResult?
        get() = apphrbrModerationResult?.adStateResult?.toModerationResult()

    override val isExpired: Boolean
        get() = ad.nativeAd?.isExpired == true

    internal var view: MaxNativeAdView? = null
        private set

    @MainThread
    override fun getView(context: Context, displayedInList: Boolean): View {
        // remove from previous parent view
        releaseAdView()

        val view: MaxNativeAdView = this.view ?: run {
            viewFactory.create(context = context, ad = this.ad, displayedInList = displayedInList)
                .also { this.view = it }
        }

        renderListener.onPreRender(this, view)
        loader.render(view, ad)
//        renderListener.onPostRender(this, view)

        updateRenderedView(view, ad)
        markAsRendered()

        if (ad.networkName.contains("google", ignoreCase = true)) {
            view.doOnNextLayout {
                view.rootView.requestLayout() // try to fix the impression count on admob
            }
        }

        return view
    }

    private fun updateRenderedView(
        nativeAdView: MaxNativeAdView,
        nativeAd: MaxAd,
    ): MaxNativeAdView {
        nativeAdView.optionsContentViewGroup?.let { optionsView ->
            optionsView.visibility = if (optionsView.childCount == 0) {
                View.GONE
            } else {
                View.VISIBLE
            }
        }

        nativeAdView.bodyTextView?.let { view ->
            view.visibility = if (view.text == null || view.text.isBlank()) {
                View.GONE
            } else {
                View.VISIBLE
            }
        }

        return nativeAdView
    }

    @MainThread
    protected override fun releaseAdView() {
        view?.removeFromParent()
        view = null
    }
}
