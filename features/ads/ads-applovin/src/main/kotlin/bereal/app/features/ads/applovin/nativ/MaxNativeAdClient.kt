package bereal.app.features.ads.applovin.nativ

import android.content.Context
import androidx.annotation.AnyThread
import androidx.annotation.MainThread
import bereal.app.config.LocalFeatureFlags
import bereal.app.features.ads.api.AdClient
import bereal.app.features.ads.api.LocalExtrasProvider
import bereal.app.features.ads.api.listener.AdListeners
import bereal.app.features.ads.api.log.AdsLogger
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.api.model.AdAlreadyLoadingException
import bereal.app.features.ads.api.model.AdRequestId
import bereal.app.features.ads.api.model.FetchFailInfo
import bereal.app.features.ads.applovin.domain.ComputeLongLifeAdsUseCase
import bereal.app.features.ads.applovin.exception.MaxAdLoadException
import bereal.app.features.ads.applovin.listener.MultiMaxNativeAdListener
import bereal.app.features.ads.applovin.util.id
import com.appharbr.sdk.engine.AdResult
import com.appharbr.sdk.engine.AdSdk
import com.appharbr.sdk.engine.AppHarbr
import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxError
import com.applovin.mediation.nativeAds.MaxNativeAdListener
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.sdk.AppLovinSdk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.Date
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.time.Duration.Companion.seconds

private val FAKE_NATIVE_ADS = LocalFeatureFlags.Feed.fakeNativeAds
private val FAKE_NATIVE_INSERT_DELAY = 3.seconds

class MaxNativeAdClient(
    override val clientDisplayName: String,
    override val config: AdClient.Config,
    private val applicationContext: Context,
    appLovinSdk: AppLovinSdk = AppLovinSdk.getInstance(applicationContext),
    private val adViewFactory: MaxNativeAdViewFactory,
    private val renderListener: NativeAdsRenderListener,
    private val localExtrasProviders: List<LocalExtrasProvider> = emptyList(),
    override val listeners: AdListeners,
    private val longLifeAdsUseCase: ComputeLongLifeAdsUseCase,
) : AdClient {

    override val adType: Ad.Type = Ad.Type.NATIVE

    private val maxNativeAdListener = MultiMaxNativeAdListener()

    private val loadedAds = mutableListOf<Ad>()

    private val fetchAdMutex = Mutex()

    override var lastFailInfo: FetchFailInfo? = null

    private val loader = MaxNativeAdLoader(
        config.adUnit,
        appLovinSdk,
        applicationContext,
    )

    init {
        require(appLovinSdk.isInitialized) { "AppLovin instance not initialized" }
        loader.setNativeAdListener(maxNativeAdListener)
        loader.setRevenueListener { ad ->
            val adWrapper = findOrCreateAdWrapperForAnalytics(ad)
            adWrapper.markAsRevenuePaid()
            listeners.runRevenueListener { it.onAdRevenuePaid(this, adWrapper) }
        }

        maxNativeAdListener.add(object : MaxNativeAdListener() {
            override fun onNativeAdExpired(ad: MaxAd) {
                // ad expired, can't be served anymore
            }

            override fun onNativeAdClicked(ad: MaxAd) {
                val adWrapper = findOrCreateAdWrapperForAnalytics(ad)
                listeners.runClickListener {
                    it.onAdClick(
                        this@MaxNativeAdClient,
                        adWrapper,
                    )
                }
            }
        })

        config.placement?.let { loader.placement = it }
    }

    fun addMaxNativeAdListener(listener: MaxNativeAdListener) {
        maxNativeAdListener.add(listener)
    }

    fun removeMaxNativeAdListener(listener: MaxNativeAdListener) {
        maxNativeAdListener.remove(listener)
    }

    @Throws(Exception::class)
    override suspend fun fetchAd(localExtras: List<Pair<String, Any>>): Ad {
        return if (fetchAdMutex.tryLock()) {
            try {
                fetchAdSafe(localExtras)
            } finally {
                fetchAdMutex.unlock()
            }
        } else {
            throw AdAlreadyLoadingException()
        }
    }

    /** see https://developers.applovin.com/en/android/ad-formats/native-ads#templates */
    @Throws(Exception::class)
    private suspend fun fetchAdSafe(localExtras: List<Pair<String, Any>>): Ad {
        listeners.runLoadingListeners { it.onAdLoadingStarted(this) }

        // try to reuse a destroyed ad (but still in memory)

        val reusedAd = getAdsToDestroy().firstOrNull()?.also { ad ->
            withContext(Dispatchers.Main) {
                loadedAds.remove(ad)
            }
            destroyAds(listOf(ad))
        }

        val providersExtras = localExtrasProviders.flatMap { it.getLocalExtras() }
        val ad = withContext(Dispatchers.IO) {
            try {
                if (FAKE_NATIVE_ADS) {
                    val adWrapper = FakeNativeAd(
                        adId = FakeNativeAd.createAdId().toString() + " / " + config.feedTarget,
                        loadedAt = Date(),
                        longLifeAds = longLifeAdsUseCase(),
                    )

                    delay(FAKE_NATIVE_INSERT_DELAY)
                    return@withContext adWrapper
                }

                // Wrap ad loading into a coroutine
                suspendCancellableCoroutine<Ad> { continuation ->
                    val callback = object : MaxNativeAdListener() {
                        override fun onNativeAdLoaded(view: MaxNativeAdView?, ad: MaxAd) {
                            maxNativeAdListener.remove(this)
                            val adWrapper = NativeAd(
                                ad = ad,
                                loadedAt = Date(),
                                loader = loader,
                                viewFactory = adViewFactory,
                                renderListener = renderListener,
                                apphrbrModerationResult = if (AppHarbr.isInitialized()) {
                                    ad.getNativeAdModerationResult()
                                } else {
                                    null
                                },
                                longLifeAds = longLifeAdsUseCase(),
                            )
                            try {
                                continuation.resume(adWrapper)
                            } catch (e: Exception) {
                                // Avoid crashes if callback is called multiple times
                                Timber.tag("MaxNativeAdClient").e(e, "Failed to notify fetchAd")
                            }
                        }

                        override fun onNativeAdLoadFailed(adUnitId: String, error: MaxError) {
                            maxNativeAdListener.remove(this)
                            try {
                                continuation.resumeWithException(MaxAdLoadException(error))
                            } catch (e: Exception) {
                                // Avoid crashes if callback is called multiple times
                                Timber.tag("MaxNativeAdClient")
                                    .e(e, "Failed to notify fetchAd error")
                            }
                        }
                    }

                    Timber.tag("MaxNativeAdClient").i("fetchAd")
                    maxNativeAdListener.add(callback)
                    providersExtras.forEach { (key, value) ->
                        loader.setLocalExtraParameter(key, value)
                    }
                    localExtras.forEach { (key, value) ->
                        loader.setLocalExtraParameter(key, value)
                    }
                    loader.loadAd()

                    continuation.invokeOnCancellation {
                        maxNativeAdListener.remove(callback)
                    }
                }
            } catch (e: MaxAdLoadException) {
                Timber.tag("MaxNativeAdClient").e(e, "Failed to load ad")
                listeners.runLoadingListeners {
                    it.onAdLoadingFailed(
                        this@MaxNativeAdClient,
                        e,
                    )
                }

                // Keep reused ad instead of destroying it
                reusedAd?.let {
                    withContext(Dispatchers.Main) {
                        loadedAds.add(0, it)
                    }
                }

                throw e
            }
        }

        // in case of success, otherwise it throws an exception

        if (ad.isBlocked) {
            listeners.runModerationListener { it.onAdBlocked(this, ad) }
        }

        Timber.tag("MaxNativeAdClient").i("fetchAd success")
        withContext(Dispatchers.Main) {
            loadedAds.add(ad)
        }
        listeners.runLoadingListeners { it.onAdLoadingFinished(this, ad) }
        return ad
    }

    @MainThread
    private fun findOrCreateAdWrapperForAnalytics(ad: MaxAd): Ad {
        return loadedAds.firstOrNull {
            getMaxAd(it) == ad
        } ?: NativeAd(
            ad = ad,
            loader = loader,
            viewFactory = adViewFactory,
            renderListener = renderListener,
            loadedAt = Date(),
            longLifeAds = longLifeAdsUseCase(),
        )
    }

    @MainThread
    override fun getAdFromRequestId(requestId: AdRequestId): Ad? {
        // Try to find the previously returned ad for this request id
        return loadedAds.firstOrNull {
            it.isAssignedToRequest(requestId)
        }?.takeIf {
            it.canBeServed() && !it.isCurrentlyRendering() // here do not check if it's already paid
        }
    }

    override suspend fun hasAdReadyToBeInserted(): Boolean {
        return withContext(Dispatchers.Main) {
            getAnyAdReadyToBeInserted() != null
        }
    }

    @MainThread
    override fun getRequestIdFromAd(adId: Ad.Id): String? {
        return loadedAds.find { getMaxAd(it)?.id == adId }?.isDisplayingForRequestId
    }

    @MainThread
    override fun getAdToDisplay(
        requestId: AdRequestId,
        revenueThreshold: Double,
    ): Ad? {
        // Return previously served ad for the same requestId if still exists
        val previousAd = getAdFromRequestId(requestId)
        if (previousAd != null) {
            return previousAd
        }

        // Take first ad ready for display that matches revenue threshold
        return loadedAds.firstOrNull {
            it.isAvailableToBeInserted() && it.info.revenue >= revenueThreshold
        }
    }

    @MainThread
    override fun getAnyAdReadyToBeInserted(): Ad? {
        return loadedAds
            .firstOrNull {
                it.isAvailableToBeInserted()
            }
    }

    @AnyThread
    override suspend fun getAdsReadyToBeInserted(): List<Ad> {
        return withContext(Dispatchers.Main) {
            loadedAds
                .filter {
                    it.isAvailableToBeInserted()
                }
        }
    }

    @MainThread
    override fun getAdsAlreadyDisplayedAndPaid(): List<Ad> {
        return loadedAds
            .filter {
                it.isAlreadyDisplayedAndPaid()
            }
    }

    @MainThread
    // only release the memory allowed to this ad, but it can still be reused
    override fun releaseAdView(ad: Ad) {
        AdsLogger.info(this, "releaseAdView[${ad.id}]")

        synchronized(loadedAds) {
            ad.endToDisplay()
        }
    }

    @MainThread
    // region destroy
    /** unsafe threading, call in `syncrhonized(loadedAds)` block */
    override fun destroyAds(adsToDestroy: List<Ad>) {
        adsToDestroy.forEach {
            it.endToDisplay()
        }

        adsToDestroy.forEach {
            getMaxAd(it).let {
                loader.destroy(it)
            }
        }
    }

    /** unsafe threading, call in `syncrhonized(loadedAds)` block */
    private suspend fun getAdsToDestroy(): List<Ad> {
        return withContext(Dispatchers.Main) {
            loadedAds.filter {
                // here add an "expiration" or 5min ?
                !it.canBeServed() && !it.isCurrentlyRendering()
            }
        }
    }

    @MainThread
    override fun destroyClient() {
        loader.destroy()
        destroyAds(loadedAds)
        loadedAds.clear()
    }

    // endregion
}

private fun MaxAd.getNativeAdModerationResult(): AdResult {
    return AppHarbr.shouldBlockNativeAd(AdSdk.MAX, this, null, adUnitId)
}

private fun getMaxAd(ad: Ad): MaxAd? {
    return if (ad is NativeAd) {
        ad.ad
    } else null
}
