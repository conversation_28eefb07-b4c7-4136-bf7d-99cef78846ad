package bereal.app.features.ads.applovin.listener

import com.applovin.mediation.MaxAd
import com.applovin.mediation.MaxAdListener
import com.applovin.mediation.MaxError

open class DefaultMaxAdListener : MaxAdListener {

    override fun onAdLoaded(ad: <PERSON>Ad) {
    }

    override fun onAdDisplayed(ad: <PERSON>Ad) {
    }

    override fun onAdHidden(ad: MaxAd) {
    }

    override fun onAdClicked(ad: MaxAd) {
    }

    override fun onAdLoadFailed(adUnitId: String, error: MaxError) {
    }

    override fun onAdDisplayFailed(ad: MaxAd, error: MaxError) {
    }
}
