package bereal.app.features.ads.applovin.mrec

import android.content.Context
import android.view.View
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.util.buildInfo
import bereal.app.features.ads.applovin.util.id
import bereal.app.features.ads.applovin.util.removeFromParent
import bereal.app.features.ads.applovin.util.toModerationResult
import com.appharbr.sdk.engine.AdResult
import com.applovin.mediation.MaxAd
import com.applovin.mediation.ads.MaxAdView
import java.util.Date

class MRECAd internal constructor(
    val ad: MaxAd,
    val view: MaxAdView,
    override val loadedAt: Date,
    override val longLifeAds: Boolean,
) : Ad() {

    override val type: Type = Type.MREC
    override val id: Id = ad.id
    override val info: Info = ad.buildInfo()

    internal var apphrbrModerationResult: AdResult? = null
    override val moderationResult: ModerationResult?
        get() = apphrbrModerationResult?.adStateResult?.toModerationResult()

    /**
     * Last time we tried to load an mrec ad using this view, it was a failure
     * We can still use this view to load a new ad, but not to display it
     * @see canBeServed
     */
    var isFailure: Boolean = false

    override val isExpired: Boolean
        get() = false

    override fun canBeServed(): Boolean {
        return super.canBeServed() && !isFailure
    }

    override fun getView(context: Context, displayedInList: Boolean): View {
        // remove from previous parent view
        releaseAdView()
        // displayedInList is not used in MREC
        markAsRendered()
        return this.view
    }

    override fun releaseAdView() {
        view.removeFromParent()
    }
}
