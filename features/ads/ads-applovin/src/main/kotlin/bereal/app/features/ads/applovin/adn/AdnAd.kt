package bereal.app.features.ads.applovin.adn

import android.content.Context
import android.view.View
import bereal.app.features.ads.api.model.Ad
import bereal.app.features.ads.applovin.util.removeFromParent
import io.adn.sdk.publisher.AdnNativeAd
import java.lang.ref.WeakReference
import java.util.Date

class AdnAd(
    val adId: String,
    val adnNativeAd: AdnNativeAd?,
    private val viewFactory: AdnNativeAdViewFactory,
    override val info: Info,
    override val loadedAt: Date,
    override val longLifeAds: Boolean,
) : Ad() {
    override val id: Id = Id(adId)
    override val type: Type = Type.NATIVE

    override val moderationResult: ModerationResult? = null
    override val isExpired: Boolean = false

    internal var view: WeakReference<View>? = null
        private set

    override fun getView(context: Context, displayedInList: <PERSON><PERSON><PERSON>): View {
        return viewFactory.create(context, adnNativeAd, displayedInList).also {
            view = WeakReference(it)
        }
    }

    override fun releaseAdView() {
        view?.get()?.removeFromParent()
        view = null
    }
}
