package bereal.app.features.ads.applovin.domain

import bereal.app.settings.model.ads.GetIsOldAdsInsertionBehaviorEnabled
import org.koin.core.annotation.Factory

/**
 * a long life ad : an ad that's inserted at a specific index, and will never change
 * as long as the banner is clicked and the feed refreshed
 */
@Factory
class ComputeLongLifeAdsUseCase(
    private val getIsOldAdsInsertionBehaviorEnabled: GetIsOldAdsInsertionBehaviorEnabled,
) {
    operator fun invoke(): Boolean {
        return getIsOldAdsInsertionBehaviorEnabled().not()
    }
}
