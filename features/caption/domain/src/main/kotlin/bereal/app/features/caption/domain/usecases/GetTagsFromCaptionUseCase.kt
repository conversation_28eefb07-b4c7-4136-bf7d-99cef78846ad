package bereal.app.features.caption.domain.usecases

import bereal.app.entities.Caption
import bereal.app.entities.tag.TagDomainModel
import org.koin.core.annotation.Factory

@Factory
class GetTagsFromCaptionUseCase {

    operator fun invoke(caption: Caption): List<TagDomainModel> = when (caption) {
        Caption.NotSet -> emptyList()
        is Caption.Set -> caption.tags
        is Caption.ToBeSent -> caption.tags
    }
}
