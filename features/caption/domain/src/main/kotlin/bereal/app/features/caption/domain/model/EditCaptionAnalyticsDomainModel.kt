package bereal.app.features.caption.domain.model

import bereal.app.analytics.AEvent.Companion.KEY_MEDIA_TYPE_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_MOMENT_ID_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_POST_ID_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_POST_TYPE_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_USER_ID_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_VIEW_VALUE
import bereal.app.analytics.model.AnalyticsEvent
import bereal.app.analytics.model.AnalyticsMediaType
import bereal.app.analytics.model.AnalyticsParam
import bereal.app.analytics.model.AnalyticsPostType
import bereal.app.analytics.model.AnalyticsView

sealed interface EditCaptionAnalyticsDomainModel : AnalyticsEvent {
    data class ActionAutocompleteMenuOpen(
        val postAnalytics: PostAnalytics,
        val view: AnalyticsView,
    ) : EditCaptionAnalyticsDomainModel {
        override val name = "actionAutocompleteMenuOpen"
        override val params = postAnalytics.toAnalyticsValues() +
            listOf(
                AnalyticsParam(name = KEY_VIEW_VALUE, value = view.value),
            )
    }

    data class ActionAutocompleteMenuSelectUser(
        val selectedUserId: String,
        val postAnalytics: PostAnalytics,
        val view: AnalyticsView,
    ) : EditCaptionAnalyticsDomainModel {
        override val name = "actionAutocompleteMenuSelectUser"
        override val params = postAnalytics.toAnalyticsValues() + listOf(
            AnalyticsParam(name = "selectedUid", value = selectedUserId),
            AnalyticsParam(name = KEY_VIEW_VALUE, value = view.value),
        )
    }
}

data class PostAnalytics(
    val postId: String,
    val postOwnerId: String,
    val notificationId: String,
    val postType: AnalyticsPostType,
    val mediaType: AnalyticsMediaType,
)

fun PostAnalytics.toAnalyticsValues(): List<AnalyticsParam> = listOf(
    AnalyticsParam(name = KEY_MOMENT_ID_VALUE, value = notificationId),
    AnalyticsParam(name = KEY_USER_ID_VALUE, value = postOwnerId),
    AnalyticsParam(name = KEY_POST_ID_VALUE, value = postId),
    AnalyticsParam(name = KEY_POST_TYPE_VALUE, value = postType.value),
    AnalyticsParam(name = KEY_MEDIA_TYPE_VALUE, value = mediaType.value),
)
