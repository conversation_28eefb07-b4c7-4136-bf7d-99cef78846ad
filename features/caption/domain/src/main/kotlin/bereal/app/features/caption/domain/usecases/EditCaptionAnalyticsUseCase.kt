package bereal.app.features.caption.domain.usecases

import bereal.app.analytics.AnalyticsManager
import bereal.app.features.caption.domain.model.EditCaptionAnalyticsDomainModel
import org.koin.core.annotation.Factory

@Factory
class EditCaptionAnalyticsUseCase(private val analyticsManager: AnalyticsManager) {
    operator fun invoke(event: EditCaptionAnalyticsDomainModel) {
        analyticsManager.logEvent(event)
    }
}
