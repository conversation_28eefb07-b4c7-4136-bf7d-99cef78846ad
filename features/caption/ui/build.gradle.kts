plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.ui.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
}

android {
    namespace = "bereal.app.features.caption.ui"
}

dependencies {
    // This should be deleted
    implementation(projects.features.timeline.timelineDomain)

    implementation(projects.features.berealview.ui)
    implementation(projects.features.caption.domain)
    implementation(projects.features.comment.ui)
    implementation(projects.features.mypost.ui)

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.common)
    implementation(projects.platform.entities)
    implementation(projects.platform.config)

    implementation(projects.platform.data.post.post)
    implementation(projects.platform.image.coreUi)
    implementation(projects.platform.data.coreStorage.domain)
}
