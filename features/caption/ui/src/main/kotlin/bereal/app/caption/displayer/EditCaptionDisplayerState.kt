package bereal.app.caption.displayer

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.remember
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

@Immutable
class EditCaptionDisplayerState {
    private val _postIdentifiersForCaption = MutableStateFlow<String?>(null)
    val postIdentifiersForCaption = _postIdentifiersForCaption.asStateFlow()

    fun display(postId: String) {
        _postIdentifiersForCaption.value = postId
    }

    fun resetPostIdentifiersForCaption() {
        _postIdentifiersForCaption.value = null
    }
}

@Composable
fun rememberCaptionState() = remember { EditCaptionDisplayerState() }
