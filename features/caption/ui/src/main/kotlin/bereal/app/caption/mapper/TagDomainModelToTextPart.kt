package bereal.app.caption.mapper

import bereal.app.design.token.controller.model.TextPart
import bereal.app.entities.tag.TagDomainModel
import java.util.UUID

internal fun TagDomainModel.toTextPartMention(): TextPart.Mentionable =
    TextPart.Mentionable(
        mentionUid = UUID.randomUUID().toString(),
        userId = this.userId,
        userNameWithAnnotation = this.usernameWithAnnotation,
        fullName = this.fullName,
        startIndex = this.startIndex,
        isEnabled = this.isEnabled,
    )
