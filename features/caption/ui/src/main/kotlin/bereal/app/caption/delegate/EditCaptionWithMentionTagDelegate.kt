package bereal.app.caption.delegate

import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.text.input.TextFieldValue
import bereal.app.caption.mapper.toTextPartMention
import bereal.app.caption.mapper.toUiModel
import bereal.app.caption.model.CaptionAndTagsDomainModel
import bereal.app.caption.model.EditCaptionTextViewState
import bereal.app.caption.model.MentionablesViewState
import bereal.app.comment.mention.mapper.toMentionnable
import bereal.app.design.token.controller.domain.InitializeTextWithTagsStateUseCase
import bereal.app.design.token.controller.domain.OnMentionSelectedUseCase
import bereal.app.design.token.controller.domain.UpdateTextWithTagsStateWithNewTextUseCase
import bereal.app.design.token.controller.domain.model.TextWithTagsStateDomainModel
import bereal.app.design.token.controller.model.ClickedMention
import bereal.app.entities.BasicUser
import bereal.app.entities.Caption
import bereal.app.entities.tag.TagDomainModel
import bereal.app.entities.tag.TagTypeDomainModel
import bereal.app.features.caption.domain.usecases.GetTagsFromCaptionUseCase
import bereal.app.features.caption.domain.usecases.GetTextFromCaptionUseCase
import bereal.app.timeline.domain.model.CaptionMentionnablesDomainModel
import bereal.app.timeline.domain.usecases.ObserveCaptionMentionnablesUseCase
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

/**
 * Analyse the given text & produce a list of [Regular,Mentionable,PendingMentionable], converted to a String to be displayed inside the textfield
 * - Regular : a normal text
 * - PendingMentionable : a text that start with a @ like @flo, it exposes this value to the receiver, and wait for a call to `onMentionSelected` to replace it with an actual mention
 * - Mentionable : a mention validated by the user (after a call to onMentionSelected), it contains the username & the user fullname
 */
@Factory
internal class EditCaptionWithMentionTagDelegate(
    private val initializeStateFromString: InitializeTextWithTagsStateUseCase,
    private val onMentionSelectedUseCase: OnMentionSelectedUseCase,
    private val updateTextWithTagsState: UpdateTextWithTagsStateWithNewTextUseCase,
    private val getTextFromCaptionUseCase: GetTextFromCaptionUseCase,
    private val getTagsFromCaptionUseCase: GetTagsFromCaptionUseCase,
    observeCaptionMentionnablesUseCase: ObserveCaptionMentionnablesUseCase,
) {
    private var initialPostCaption: Caption? = null

    private val state = mutableStateOf<TextWithTagsStateDomainModel?>(null)

    val captionState: State<EditCaptionTextViewState> = derivedStateOf {
        state.value?.let {
            EditCaptionTextViewState.WithTokens(
                textFieldValue = it.textFieldValue,
                mentions = it.mentions.map {
                    it.toUiModel()
                }.toPersistentList(),
            )
        } ?: EditCaptionTextViewState.Idle
    }

    fun initialize(postCaption: Caption) {
        initialPostCaption = postCaption

        state.value = initializeStateFromString(
            fullText = getTextFromCaptionUseCase(postCaption),
            mentions = getTagsFromCaptionUseCase(postCaption).mapNotNull {
                it.toTextPartMention()
            },
        )
    }

    private fun String.numberOfStartingSpaces(): Int {
        var count = 0
        this.forEach { c ->
            if (c == ' ') {
                count++
            } else {
                return count
            }
        }
        return count
    }

    fun getCaptionAndTagsMention(): CaptionAndTagsDomainModel {
        val rawCaption = state.value?.textFieldValue?.text ?: ""
        val numberOfStartingSpaces = rawCaption.numberOfStartingSpaces()
        val trimmedStartCaption = rawCaption.substring(startIndex = numberOfStartingSpaces)
        return CaptionAndTagsDomainModel(
            caption = trimmedStartCaption,
            mentions = state.value?.mentions?.map {
                TagDomainModel(
                    userId = it.userId,
                    startIndex = it.original.startIndex - numberOfStartingSpaces,
                    endIndex = it.original.endIndex - numberOfStartingSpaces,
                    fullName = it.transformed.fullName,
                    usernameWithAnnotation = it.original.userNameWithAnnotation,
                    isEnabled = it.isEnabled,
                    type = TagTypeDomainModel.Mention,
                    user = null,
                )
            } ?: emptyList(),
        )
    }

    fun onMentionSelected(clickedMention: ClickedMention) {
        state.value?.also { currentState ->
            state.value = onMentionSelectedUseCase(
                mention = clickedMention,
                currentState = currentState,
            )
        }
    }

    fun onTextChange(newTextFieldValue: TextFieldValue) {
        state.value = updateTextWithTagsState(
            newTextFieldValue = newTextFieldValue,
            oldState = state.value,
        )
    }

    fun hasTextChanged(): Boolean {
        if (state.value == null)
            return true
        else {
            val captionAndTag = getCaptionAndTagsMention()
            val captionText = initialPostCaption?.let { getTextFromCaptionUseCase(it) }
            val captionTags = initialPostCaption?.let { getTagsFromCaptionUseCase(it) }
            return captionText != captionAndTag.caption || captionTags?.map { it.userId } != captionAndTag.mentions.map { it.userId }
        }
    }

    val mentionables = observeCaptionMentionnablesUseCase()
        .flatMapLatest { mentionnables ->
            when (mentionnables) {
                is CaptionMentionnablesDomainModel.Disabled -> flowOf(null)
                is CaptionMentionnablesDomainModel.Enabled -> {
                    snapshotFlow { state.value }
                        .map { result ->
                            result?.pendingMentionable?.let { pendingMentionable ->
                                MentionablesViewState(
                                    currentPendingMention = pendingMentionable,
                                    mentionnables = mentionnables.mentionnables
                                        .filter {
                                            !result.alreadyMentioned.contains(
                                                it.userName,
                                            )
                                        }
                                        .filter {
                                            it.filterCanBeMentioned(pendingMentionable.textWithoutAnnot)
                                        }.map { user ->
                                            user.toMentionnable()
                                        }
                                        .toPersistentList(),
                                )
                            }
                        }
                }
            }
        }
}

private fun BasicUser.filterCanBeMentioned(textWithoutAnnot: String): Boolean {
    return this.userName.contains(
        textWithoutAnnot,
        ignoreCase = true,
    ) || this.userNameWithoutAccent.contains(
        textWithoutAnnot,
        ignoreCase = true,
    ) || this.fullName?.contains(
        textWithoutAnnot,
        ignoreCase = true,
    ) == true ||
        this.fullNameWithoutAccent.contains(
            textWithoutAnnot,
            ignoreCase = true,
        )
}
