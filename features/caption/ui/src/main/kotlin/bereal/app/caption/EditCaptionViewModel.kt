package bereal.app.caption

import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.text.input.TextFieldValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import bereal.app.analytics.AEvent
import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.model.AnalyticsMediaType
import bereal.app.analytics.model.AnalyticsPostType
import bereal.app.analytics.model.AnalyticsView
import bereal.app.caption.delegate.EditCaptionWithMentionTagDelegate
import bereal.app.caption.model.EditCaptionOpenedFrom
import bereal.app.caption.model.EditCaptionTextViewState
import bereal.app.caption.model.MentionablesViewState
import bereal.app.common.stateIn
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.data.post.repository.MyPostRepository
import bereal.app.design.avatar.isOfficialAccount
import bereal.app.design.berealimageview.model.BeRealViewContainerDataUiModel
import bereal.app.design.berealimageview.model.SourceTypeUiModel
import bereal.app.design.berealimageview.model.VideoSettingsUiModel
import bereal.app.design.berealimageview.model.toBeRealViewDataModel
import bereal.app.design.dialog.DialogDisplayer
import bereal.app.design.dialog.alert.model.Cancel
import bereal.app.design.dialog.alert.model.Confirm
import bereal.app.design.dialog.model.Dialog
import bereal.app.design.token.controller.model.ClickedMention
import bereal.app.entities.Caption
import bereal.app.entities.CorePost
import bereal.app.entities.PostMedia
import bereal.app.entities.PostOriginTypeDomainModel
import bereal.app.entities.PostVisibility
import bereal.app.entities.higherVisibility
import bereal.app.entities.isMediaFromGallery
import bereal.app.entities.primaryContent
import bereal.app.entities.secondaryContent
import bereal.app.entities.toPostId
import bereal.app.features.caption.domain.model.EditCaptionAnalyticsDomainModel
import bereal.app.features.caption.domain.model.PostAnalytics
import bereal.app.features.caption.domain.usecases.EditCaptionAnalyticsUseCase
import bereal.app.platform.data.core.storage.domain.post.helpers.hasVideoContent
import bereal.app.platform.data.core.storage.domain.post.helpers.toTagDomainModel
import bereal.app.platform.data.core.storage.domain.post.usecase.GetCorePostByIdUseCase
import bereal.app.platform.data.core.storage.domain.post.usecase.ObserveCorePostByIdUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import bereal.app.translations.R.string as translations

@KoinViewModel
internal class EditCaptionViewModel(
    private val params: EditCaptionParams,
    private val myPostRepository: MyPostRepository,
    private val analyticsManager: AnalyticsManager,
    private val scope: CoroutineScope,
    private val stringProvider: StringProvider,
    private val dispatcherProvider: DispatcherProvider,
    private val dialogDisplayer: DialogDisplayer,
    private val editCaptionWithMentionTagDelegate: EditCaptionWithMentionTagDelegate,
    private val editCaptionAnalyticsUseCase: EditCaptionAnalyticsUseCase,
    private val getCorePostByPostIdUseCase: GetCorePostByIdUseCase,
    private val observeCorePostByIdUseCase: ObserveCorePostByIdUseCase,
) : ViewModel() {

    private suspend fun getPost() = getCorePostByPostIdUseCase(params.postId.toPostId(), lite = true)

    init {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            val postCaption = getPost()
                ?.let { post ->
                    if (post.caption.isNullOrBlank()) {
                        Caption.NotSet
                    } else {
                        val mentions = post.tags.filter { it.type == CorePost.Tag.Type.Mention }.map { it.toTagDomainModel() }
                        Caption.Set(text = post.caption.orEmpty(), tags = mentions)
                    }
                } ?: Caption.NotSet
            withContext(dispatcherProvider.ui) {
                editCaptionWithMentionTagDelegate.initialize(postCaption)
            }
        }
    }

    val viewState: StateFlow<EditCaptionViewState?> = observeCorePostByIdUseCase(params.postId.toPostId(), lite = true)
        .map { myPost ->
            myPost?.let {
                val beRealViewContainerData = BeRealViewContainerDataUiModel.PostContainer(
                    postId = it.id.postId,
                    momentId = it.moment?.id.orEmpty(),
                    userId = it.owner.uid,
                    isMain = it.isMain,
                    isLate = it.isLate,
                    view = AnalyticsView.CaptionEditor,
                    isMediaTypeVideo = it.primary is PostMedia.Video || it.primary is PostMedia.Bts,
                    hasBtsContent = (it.primary as? PostMedia.Bts)?.btsMedia != null,
                    isOfficialUser = it.owner.isOfficialAccount,
                    isReshare = it.origin == PostOriginTypeDomainModel.Reshare,
                    isMediaFromGallery = it.isMediaFromGallery(),
                )

                val primaryPlaceHolder = (it.primary as? PostMedia.Bts)?.picture ?: (it.primary as? PostMedia.Video)?.picture

                EditCaptionViewState(
                    primaryImage = it.primary.primaryContent().toBeRealViewDataModel(
                        containerData = beRealViewContainerData,
                        thumbnail = primaryPlaceHolder?.toBeRealViewDataModel(containerData = beRealViewContainerData),
                        videoSettings = VideoSettingsUiModel(
                            isPlayable = false,
                            shouldShowPlayButton = false,
                        ),
                        sourceType = SourceTypeUiModel.MY_POSTS,
                    ),
                    secondaryImage = it.secondary?.secondaryContent()?.toBeRealViewDataModel(
                        beRealViewContainerData,
                    ),
                    placeholderText = stringProvider[translations.timeline_addACaption],
                )
            }
        }
        .flowOn(dispatcherProvider.viewmodel)
        .stateIn(viewModelScope, null)

    val captionViewState: State<EditCaptionTextViewState> by lazy {
        derivedStateOf {
            editCaptionWithMentionTagDelegate.captionState.value
        }
    }

    val mentionnables: StateFlow<MentionablesViewState?> by lazy {
        snapshotFlow { editCaptionWithMentionTagDelegate }
            .filterNotNull()
            .flatMapLatest {
                it.mentionables
            }
            .stateIn(viewModelScope, null)
    }

    init {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            mentionnables.map { it?.currentPendingMention != null }
                .distinctUntilChanged()
                .filter { it } // each time it becomes true
                .collect {
                    getAnalyticsPostInfos()?.let { analyticsPostInfos ->
                        editCaptionAnalyticsUseCase(
                            EditCaptionAnalyticsDomainModel.ActionAutocompleteMenuOpen(
                                postAnalytics = analyticsPostInfos,
                                view = when (params.openedFrom) {
                                    EditCaptionOpenedFrom.FriendsTimelineMyPost -> AnalyticsView.CaptionEditor
                                    EditCaptionOpenedFrom.ReactionView,
                                    EditCaptionOpenedFrom.ReactionViewDotMenu,
                                    -> AnalyticsView.Reactions
                                    EditCaptionOpenedFrom.MyProfile -> AnalyticsView.MyProfile
                                },
                            ),
                        )
                    }
                }
        }
    }

    fun sendCaption() {
        scope.launch(dispatcherProvider.viewmodel) {
            val captionAndTags = editCaptionWithMentionTagDelegate.getCaptionAndTagsMention()

            val newCaption = captionAndTags.caption

            getPost()?.let { post ->
                val action = when (post.caption) {
                    null -> AEvent.SetCaption.Action.add
                    else -> {
                        if (newCaption.isBlank()) {
                            AEvent.SetCaption.Action.remove
                        } else {
                            AEvent.SetCaption.Action.edit
                        }
                    }
                }

                if (editCaptionWithMentionTagDelegate.hasTextChanged()) {
                    analyticsManager.logEvent(
                        AEvent.SetCaption(
                            action = action,
                            momentId = post.moment?.id.orEmpty(),
                            postId = post.id.postId,
                            length = newCaption.length,
                            view = when (params.openedFrom) {
                                EditCaptionOpenedFrom.FriendsTimelineMyPost -> AnalyticsView.MyBeReal
                                EditCaptionOpenedFrom.ReactionView -> AnalyticsView.Reactions
                                EditCaptionOpenedFrom.ReactionViewDotMenu -> AnalyticsView.ReactionsDotMenu
                                EditCaptionOpenedFrom.MyProfile -> AnalyticsView.MyProfile
                            },
                            mentions = captionAndTags.mentions.map { it.userId },
                            postType = AnalyticsPostType.toPostType(
                                isMain = post.isMain,
                                isLate = post.isLate,
                            ),
                            visibility = when (post.visibility.higherVisibility()) {
                                PostVisibility.Friends -> AEvent.ActionChangeVisibility.Visibility.Friends
                                PostVisibility.FriendOfFriends -> AEvent.ActionChangeVisibility.Visibility.Fof
                                PostVisibility.Global -> AEvent.ActionChangeVisibility.Visibility.Public
                                else -> null
                            },
                            mediaType = AnalyticsMediaType.toMediaType(post.hasVideoContent()),
                            originalPostUserId = post.parentPostInfo?.parentPostOwner?.uid,
                        ),
                    )
                    myPostRepository.updateCaption(
                        postId = post.id,
                        caption = newCaption,
                        tags = captionAndTags.mentions,
                    )
                }
            }
        }
    }

    fun onTextChange(newTextFieldValue: TextFieldValue) {
        editCaptionWithMentionTagDelegate.onTextChange(newTextFieldValue)
    }

    fun onCancel(onDone: () -> Unit) {
        if (!editCaptionWithMentionTagDelegate.hasTextChanged()) {
            onDone()
        } else {
            dialogDisplayer.display(
                Dialog.Alert(
                    title = stringProvider[translations.edit_caption_discard_changes_title],
                    description = stringProvider[translations.edit_caption_discard_changes_message],
                    confirm = Confirm(
                        customText = stringProvider[translations.edit_caption_discard_changes_button],
                        isCritical = true,
                        onConfirm = {
                            onDone()
                        },
                    ),
                    cancel = Cancel(
                        cancelOnTouchOutside = true,
                        onCancel = {
                            // no-op
                        },
                    ),
                ),
            )
        }
    }

    fun onMentionSelected(clickedMention: ClickedMention) {
        editCaptionWithMentionTagDelegate.onMentionSelected(clickedMention)
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            getAnalyticsPostInfos()?.let { analyticsPostInfos ->
                editCaptionAnalyticsUseCase(
                    EditCaptionAnalyticsDomainModel.ActionAutocompleteMenuSelectUser(
                        postAnalytics = analyticsPostInfos,
                        view = when (params.openedFrom) {
                            EditCaptionOpenedFrom.FriendsTimelineMyPost -> AnalyticsView.CaptionEditor
                            EditCaptionOpenedFrom.ReactionView,
                            EditCaptionOpenedFrom.ReactionViewDotMenu,
                            -> AnalyticsView.Reactions
                            EditCaptionOpenedFrom.MyProfile -> AnalyticsView.MyProfile
                        },
                        selectedUserId = clickedMention.userId,
                    ),
                )
            }
        }
    }

    private suspend fun getAnalyticsPostInfos(): PostAnalytics? {
        val myPost = getPost() ?: return null

        return PostAnalytics(
            postId = myPost.id.postId,
            postOwnerId = myPost.owner.uid,
            notificationId = myPost.moment?.id.orEmpty(),
            postType = AnalyticsPostType.toPostType(
                isMain = myPost.isMain,
                isLate = myPost.isLate,
            ),
            mediaType = AnalyticsMediaType.toMediaType(isMediaTypeVideo = myPost.primary is PostMedia.Video || myPost.primary is PostMedia.Bts),
        )
    }
}

data class EditCaptionParams(
    val postId: String,
    val openedFrom: EditCaptionOpenedFrom,
)
