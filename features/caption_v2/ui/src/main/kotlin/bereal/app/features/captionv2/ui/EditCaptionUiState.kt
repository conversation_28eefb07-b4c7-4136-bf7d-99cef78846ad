package bereal.app.features.captionv2.ui

import androidx.compose.runtime.Immutable
import androidx.compose.ui.text.input.TextFieldValue
import kotlinx.collections.immutable.PersistentList

@Immutable
data class EditCaptionUiState(
    val suggestions: PersistentList<SuggestionItem>,
    val isInfoBannerVisible: <PERSON><PERSON><PERSON>,
    val isSuggestionsVisible: <PERSON>olean,
)

sealed interface EditCaptionAction {
    data class OnTextChanged(val newText: TextFieldValue) : EditCaptionAction
    data object OnTopicClicked : EditCaptionAction
    data object OnMentionClicked : EditCaptionAction
    data object OnDoneClicked : EditCaptionAction
    data class OnSuggestionClicked(val suggestion: SuggestionItem) : EditCaptionAction
}
