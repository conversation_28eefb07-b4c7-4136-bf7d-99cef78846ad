package bereal.app.features.captionv2.ui

import bereal.app.features.captionv2.domain.model.HashtagSuggestionDomainModel
import bereal.app.features.captionv2.domain.usecase.GetHashtagSuggestionsUseCase
import org.koin.core.annotation.Factory
import timber.log.Timber

@Factory
class CaptionTopicStateHolder(
    private val getTopicSuggestionUseCase: GetHashtagSuggestionsUseCase,
) {

    suspend fun parseForSuggestions(text: String): List<HashtagSuggestionDomainModel> {
        try {
            val lastHash = text.lastIndexOf('#')
            if (lastHash == -1) {
                return emptyList()
            }
            val query = text.substring(lastHash + 1)
            return getTopicSuggestionUseCase(query)
        } catch (e: Exception) {
            Timber.e(e)
            return emptyList()
        }
    }
}
