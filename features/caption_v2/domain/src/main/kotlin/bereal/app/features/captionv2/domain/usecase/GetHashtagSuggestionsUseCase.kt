@file:OptIn(FlowPreview::class)

package bereal.app.features.captionv2.domain.usecase

import bereal.app.features.captionv2.domain.SuggestionRepository
import bereal.app.features.captionv2.domain.model.HashtagSuggestionDomainModel
import kotlinx.coroutines.FlowPreview
import org.koin.core.annotation.Factory

@Factory
class GetHashtagSuggestionsUseCase(private val hashtagRepository: SuggestionRepository) {
    suspend operator fun invoke(query: String): List<HashtagSuggestionDomainModel> {
        return hashtagRepository.searchHashtags(query).fold(
            doOnFailure = { emptyList() },
            doOnSuccess = { it },
        )
    }
}
