package bereal.app.features.captionv2.navigationimpl

import bereal.app.common.Either
import bereal.app.entities.PostVisibility
import bereal.app.features.captionv2.navigation.CaptionNavigationResultManager
import bereal.app.features.captionv2.navigation.CaptionRequestResultError
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.CaptionDirection
import kotlinx.coroutines.CompletableDeferred
import org.koin.core.annotation.Single

@Single(binds = [CaptionNavigationResultManager::class])
class CaptionNavigationResultManagerImpl(
    private val navigationManager: NavigationManager,
) : CaptionNavigationResultManager {

    private var currentRequest: CompletableDeferred<Either<CaptionRequestResultError, String>>? =
        null

    override suspend fun editCaption(
        caption: String,
        postVisibility: PostVisibility,
    ): Either<CaptionRequestResultError, String> {
        currentRequest?.cancel()

        val deferred = CompletableDeferred<Either<CaptionRequestResultError, String>>()
        currentRequest = deferred

        navigationManager.navigate(
            CaptionDirection.direction(
                caption = caption,
                postVisibility = postVisibility,
            ),
        )
        return deferred.await()
    }

    override fun onEditCaptionResult(
        result: Either<CaptionRequestResultError, String>,
    ) {
        currentRequest?.let {
            it.complete(result)
            navigationManager.pop()
            currentRequest = null
        }
    }
}
