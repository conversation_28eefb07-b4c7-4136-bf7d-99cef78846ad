plugins {
    id(libs.plugins.bereal.data.impl.get().pluginId)
    id(libs.plugins.bereal.obfuscator.get().pluginId)
}

android {
    namespace = "bereal.app.features.antibot.data"
}

dependencies {
    implementation(libs.androidx.biometric)
    implementation(libs.okhttp)
    implementation(libs.retrofit)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.constraintlayout) // required by the arkose lib
    implementation("com.arkoselabs:arkose:0.0.1")
    implementation(projects.platform.analytics.analytics)
    implementation(projects.features.antibot.domain)
    implementation(projects.platform.commonAndroid)
    implementation(projects.platform.commonNetwork.commonNetwork)
    implementation(projects.platform.data.core.core)
    implementation(projects.platform.remoteLogger.remoteLogger)
}
