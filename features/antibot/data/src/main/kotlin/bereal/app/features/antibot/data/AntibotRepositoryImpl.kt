package bereal.app.features.antibot.data

import android.app.Application
import bereal.app.data.core.Config
import bereal.app.features.antibot.data.dataSources.remote.AntibotRemoteDataSource
import bereal.app.features.antibot.domain.AntibotRepository
import bereal.app.features.antibot.domain.models.AntibotStatusDomainModel
import com.arkoselabs.sdk.ArkoseConfig
import com.arkoselabs.sdk.ArkoseManager
import org.koin.core.annotation.Single

@Single
class AntibotRepositoryImpl(
    private val application: Application,
    private val remoteDataSource: AntibotRemoteDataSource,
    private val config: Config,
) : AntibotRepository {

    override fun init() {
        val arkoseConfig = getArkoseConfig().build()

        if (BuildConfig.DEBUG) {
            ArkoseManager.setLogLevel(ArkoseManager.INFO)
        }

        ArkoseManager.initialize(arkoseConfig, application)
    }

    override suspend fun getAntibotStatus(): AntibotStatusDomainModel {
        return remoteDataSource.getAntibotStatus()
    }

    override suspend fun updateDataExchange(dataExchange: String) {
        if (dataExchange.isEmpty()) return
        val arkoseConfig = getArkoseConfig()
            .blobData(dataExchange)
            .build()

        ArkoseManager.initialize(arkoseConfig, application)
    }

    private fun getArkoseConfig(): ArkoseConfig.Builder {
        return ArkoseConfig.Builder()
            .apiKey(config.arkoseApiKey)
            .enableBackButton(false)
            .loading(false)
            .setDismissChallengeOnTouchOutside(false)
    }
}
