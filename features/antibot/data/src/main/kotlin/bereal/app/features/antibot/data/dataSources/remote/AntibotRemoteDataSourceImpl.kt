package bereal.app.features.antibot.data.dataSources.remote

import bereal.app.features.antibot.domain.models.AntibotStatusDomainModel
import org.koin.core.annotation.Single
import java.lang.Exception

@Single
class AntibotRemoteDataSourceImpl(
    private val killSwitchApi: KillSwitchApi,
) : AntibotRemoteDataSource {

    override suspend fun getAntibotStatus(): AntibotStatusDomainModel {
        return try {
            val response = killSwitchApi.antibotFileExists()
            // killswitch acting like a feature flag for the antibot
            // if we receive a 404, it means that file does not exist and there is nothing preventing the antibot to be shown
            return if (response.code() == 404) {
                AntibotStatusDomainModel.MustShow
            } else {
                AntibotStatusDomainModel.Ignore
            }
        } catch (e: Exception) {
            AntibotStatusDomainModel.Ignore
        }
    }
}
