package bereal.app.features.antibot.data

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.eventFlow
import bereal.app.analytics.AEvent
import bereal.app.analytics.AnalyticsManager
import bereal.app.common.resumeSafety
import bereal.app.commonandroid.CurrentActivity
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.features.antibot.domain.AntibotChallengeProvider
import bereal.app.features.antibot.domain.models.AntibotResultDomainModel
import com.arkoselabs.sdk.ArkoseManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.invoke
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single
import kotlin.coroutines.suspendCoroutine

@Single
class ArkoseAntibotChallengeProviderImpl(
    private val activity: CurrentActivity,
    private val dispatcherProvider: DispatcherProvider,
    private val analytics: AnalyticsManager,
) : AntibotChallengeProvider {

    companion object {
        private const val ARKOSE_ANTIBOT_IDENTIFIER_KEY = "AR"
        private const val ARKOSE_ANTIBOT_PAYLOAD_TOKEN_KEY = "token"
    }

    override suspend fun showChallenge(): AntibotResultDomainModel {
        val activity = (activity.current as? FragmentActivity) ?: run {
            analytics.logEvent(AEvent.Antibot.ChallengeSkipped(cause = "no activity"))
            return AntibotResultDomainModel.Skip
        }

        return dispatcherProvider.ui {
            val arkoseChallenge = ArkoseManager.showEnforcementChallenge(activity)
            return@ui suspendCoroutine<AntibotResultDomainModel> { continuation ->
                try {
                    arkoseChallenge
                        .addOnShownListener {
                            analytics.logEvent(AEvent.Antibot.DisplayChallenge)
                            setBackgroundForArkoseChallenge(
                                this,
                                arkoseChallenge.enforcementChallengeFragment,
                            )
                        }
                        .addOnSuccessListener {
                            val token = try {
                                it.response.getString(ARKOSE_ANTIBOT_PAYLOAD_TOKEN_KEY)
                            } catch (e: Exception) {
                                null
                            }
                            analytics.logEvent(AEvent.Antibot.ChallengeSuccess)
                            continuation.resumeSafety(
                                AntibotResultDomainModel.Success(
                                    token = token,
                                    identifier = ARKOSE_ANTIBOT_IDENTIFIER_KEY,
                                ),
                            )
                        }
                        .addOnFailureListener {
                            analytics.logEvent(AEvent.Antibot.ChallengeFailure)
                            continuation.resumeSafety(AntibotResultDomainModel.Failure)
                        }
                        .addOnErrorListener {
                            analytics.logEvent(AEvent.Antibot.ChallengeSkipped(cause = "addOnErrorListener"))
                            continuation.resumeSafety(AntibotResultDomainModel.Skip)
                        }
                } catch (e: Exception) {
                    analytics.logEvent(AEvent.Antibot.ChallengeSkipped(cause = "genericError " + e.message))
                    continuation.resumeSafety(AntibotResultDomainModel.Skip)
                }
            }
        }
    }

    private fun setBackgroundForArkoseChallenge(
        scope: CoroutineScope,
        challengeFragment: Fragment,
    ) {
        scope.launch(dispatcherProvider.ui) {
            challengeFragment.lifecycle.eventFlow.collect { state ->
                if (state == Lifecycle.Event.ON_RESUME) {
                    val view = challengeFragment.view
                    (view as? ViewGroup)?.let {
                        it.addView(
                            View(it.context).apply {
                                alpha = 0.7f
                                setBackgroundColor(Color.BLACK)
                            },
                            0,
                            ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT,
                            ),
                        )
                    }
                    this.coroutineContext.job.cancel()
                }
            }
        }
    }
}
