package bereal.app.features.antibot.data.di

import bereal.app.common.network.okhttp.OkHttpClientProvider
import bereal.app.data.core.Config
import bereal.app.features.antibot.data.dataSources.remote.KillSwitchApi
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import org.koin.ksp.generated.module
import retrofit2.Retrofit
import retrofit2.create

@Module
@ComponentScan("bereal.app.features.antibot.data")
internal class AntibotDataModule {

    @Single
    fun provideKillSwitchApi(
        okHttpClientProvider: OkHttpClientProvider,
        config: Config,
        converterFactory: retrofit2.Converter.Factory,
    ): KillSwitchApi {
        val okHttpClient = okHttpClientProvider.nonAuthenticatedClient

        val retrofit = Retrofit.Builder()
            .baseUrl(config.killSwitchUrl)
            .client(okHttpClient)
            .addConverterFactory(converterFactory)
            .build()

        return retrofit.create<KillSwitchApi>()
    }
}

val antibotDataModule = AntibotDataModule().module
