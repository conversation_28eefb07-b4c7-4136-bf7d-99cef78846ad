package bereal.app.features.antibot.domain.models

sealed interface AntibotResultDomainModel {
    data class Success(val token: String?, val identifier: String) : AntibotResultDomainModel
    data object Failure : AntibotResultDomainModel
    data object Skip : AntibotResultDomainModel
}

fun AntibotResultDomainModel.getChallengeData(): AntibotChallengeInfo {
    return when (this) {
        is AntibotResultDomainModel.Success -> AntibotChallengeInfo(token = token, identifier = identifier)
        else -> AntibotChallengeInfo(token = null, identifier = null)
    }
}
