plugins {
    id(libs.plugins.bereal.data.impl.get().pluginId)
    id(libs.plugins.bereal.retrofit.get().pluginId)
    id(libs.plugins.bereal.datastore.get().pluginId)
}

android {
    namespace = "bereal.app.bts.data"
}

dependencies {
    implementation(libs.media3.transformer)
    implementation(libs.media3.common)
    implementation(libs.media3.database)
    implementation(libs.media3.datasource)
    implementation(projects.features.bts.domain)
    implementation(projects.platform.common)
    implementation(projects.platform.commonAndroid)
    implementation(projects.platform.data.core.core)
    implementation(projects.platform.datastore)
    implementation(projects.platform.entities)
    implementation(projects.platform.error)
    implementation(projects.platform.image.coreUi)
    implementation(projects.platform.videoProcessing.videoProcessingMedia3Impl)
}
