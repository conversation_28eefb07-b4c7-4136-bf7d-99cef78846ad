package bereal.app.bts.data.local

import android.content.Context
import bereal.app.bts.data.model.BtsSettingsLocalModel
import bereal.app.datastores.dataStore
import bereal.app.datastores.row
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Single

@Single
internal class BtsVideoDataStore(
    context: Context,
) : BtsVideoDataSource {

    private val dataStore = context.dataStore(BTS_VIDEO_DATASTORE)

    private val btsSettingsRow = dataStore.row<BtsSettingsLocalModel>(BTS_VIDEO_DATAROW)

    override val btsSettings: Flow<BtsSettingsLocalModel> = btsSettingsRow.value.map {
        it ?: BtsSettingsLocalModel(
            hasViewedFirstBts = false,
        )
    }

    override suspend fun setHasViewedFirstBts(value: <PERSON>olean) {
        btsSettingsRow.update {
            it?.copy(hasViewedFirstBts = value) ?: BtsSettingsLocalModel(
                hasViewedFirstBts = value,
            )
        }
    }

    companion object {
        const val BTS_VIDEO_DATASTORE = "vmjknpioHPIUP"
        const val BTS_VIDEO_DATAROW = "vmoipaoizeuruyytttazeert"
    }
}
