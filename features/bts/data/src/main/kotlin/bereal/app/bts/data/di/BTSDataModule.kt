package bereal.app.bts.data.di

import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import bereal.app.bts.data.remote.BtsApi
import bereal.app.data.core.RetrofitClientProvider
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import org.koin.ksp.generated.module
import retrofit2.create

val btsDataModule = BTSDataModule().module

@OptIn(UnstableApi::class)
@Module
@ComponentScan("bereal.app.bts.data")
internal class BTSDataModule {

    @Single
    fun provideBtsApi(retrofitClientProvider: RetrofitClientProvider): BtsApi =
        retrofitClientProvider.authenticatedClient.create<BtsApi>()
}
