package bereal.app.bts.data.local

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import bereal.app.commonandroid.cacheIn
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.commonandroid.mirrorVertically
import bereal.app.image.bereal.BeRealImageDrawer
import bereal.app.image.bereal.processor.model.BeRealImageDrawSpecs
import bereal.app.videoprocessing.media3.transformer.AnchorPoint
import bereal.app.videoprocessing.media3.transformer.VideoCharacteristics
import bereal.app.videoprocessing.media3.transformer.readVideoCharacteristics
import bereal.app.videoprocessing.media3.transformer.runTransformer
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import java.io.File
import kotlin.coroutines.suspendCoroutine
import kotlin.math.min
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

@OptIn(UnstableApi::class)
@Single
internal class BtsVideoExporterImpl(
    private val context: Context, // app context
    private val beRealImageDrawer: BeRealImageDrawer,
    private val dispatcherProvider: DispatcherProvider,
) : BtsVideoExporter {

    private val mutex = Mutex()

    override suspend fun exportForSharing(
        videoFilePath: String,
        primaryImageBitmap: Bitmap,
        secondaryImageBitmap: Bitmap,
        logoResId: Int,
        resultFileName: String,
        removeAudio: Boolean,
    ): Uri {
        mutex.withLock {
            val btsExportDirectory = initialiseExportDirectory(context)

            val logoBitmap = BitmapFactory.decodeResource(
                context.resources,
                logoResId,
            )
            val videoFileUri = Uri.parse(videoFilePath)

            return withContext(dispatcherProvider.ui) {
                val videoCharacteristics = readVideoCharacteristics(context = context, videoUri = videoFileUri)

                suspendCoroutine { continuation ->
                    val modifiedSecondary = createModifiedSecondaryImage(
                        primaryImageBitmap = primaryImageBitmap,
                        secondaryImageBitmap = secondaryImageBitmap,
                        videoCharacteristics = videoCharacteristics,
                    )
                    val closingImage = createModifiedClosingImage(
                        primaryImageBitmap = primaryImageBitmap,
                        secondaryImageBitmap = secondaryImageBitmap,
                        videoCharacteristics = videoCharacteristics,
                    )
                        .cacheIn(btsExportDirectory)
                        .path

                    val resultFilePath = "${btsExportDirectory.absolutePath}/$resultFileName.mp4"
                    runTransformer(
                        context = context,
                        resultFilePath = resultFilePath,
                        onComplete = {
                            continuation.resumeWith(Result.success(Uri.parse(resultFilePath)))
                        },
                        onError = { _, exception ->
                            continuation.resumeWith(Result.failure(exception))
                        },
                    ) {
                        sequence {
                            video(videoFileUri, videoCharacteristics.duration) {
                                removeAudio(removeAudio)
                                overlayReveal(modifiedSecondary) {
                                    appearingFrom(AnchorPoint.TOP_LEFT)
                                    duration(
                                        min(
                                            500.milliseconds.inWholeMilliseconds,
                                            videoCharacteristics.duration.inWholeMilliseconds,
                                        ).milliseconds,
                                    )
                                    startFromEndAt(
                                        min(
                                            600.milliseconds.inWholeMilliseconds,
                                            videoCharacteristics.duration.inWholeMilliseconds,
                                        ).milliseconds,
                                    )
                                }
                                overlayStatic(logoBitmap) {
                                    scaledTo(0.08f)
                                    mirroredVertically()
                                    location(AnchorPoint.BOTTOM_CENTER)
                                    withAlpha(0.4f)
                                    withMargins(0, 0, 0, 350)
                                }
                            }
                            closingImage?.let { path ->
                                stillImage(path) {
                                    withDuration(2.seconds)
                                    overlayStatic(logoBitmap) {
                                        scaledTo(0.08f)
                                        mirroredVertically()
                                        location(AnchorPoint.BOTTOM_CENTER)
                                        withAlpha(0.4f)
                                        withMargins(0, 0, 0, 350)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Create a modified secondary image that is resized to the video size and flipped vertically
    // Use the primary image as a background to get the correct size as we are reusing the same
    // code we have from the beRealImageDrawer
    private fun createModifiedSecondaryImage(
        primaryImageBitmap: Bitmap,
        secondaryImageBitmap: Bitmap,
        videoCharacteristics: VideoCharacteristics,
    ): Bitmap {
        val backgroundBitmap = Bitmap.createBitmap(
            primaryImageBitmap.width,
            primaryImageBitmap.height,
            Bitmap.Config.ARGB_8888,
        )

        val modifiedSecondary = beRealImageDrawer.drawBerealOnBitmap(
            backgroundImage = backgroundBitmap,
            frontImage = secondaryImageBitmap,
            clipBackground = true,
            imageDrawSpecs = BeRealImageDrawSpecs,
        ).mirrorVertically() // Required because the transformation coordinates are flipped

        val resizedToVideoSize = Bitmap.createScaledBitmap(
            modifiedSecondary,
            videoCharacteristics.width,
            videoCharacteristics.height,
            true,
        )

        backgroundBitmap.recycle()
        return resizedToVideoSize
    }

    private fun createModifiedClosingImage(
        primaryImageBitmap: Bitmap,
        secondaryImageBitmap: Bitmap,
        videoCharacteristics: VideoCharacteristics,
    ): Bitmap {
        val modifiedSecondary = beRealImageDrawer.drawBerealOnBitmap(
            backgroundImage = primaryImageBitmap,
            frontImage = secondaryImageBitmap,
            clipBackground = false,
            imageDrawSpecs = BeRealImageDrawSpecs,
        )

        return Bitmap.createScaledBitmap(
            modifiedSecondary,
            videoCharacteristics.width,
            videoCharacteristics.height,
            true,
        )
    }

    private fun initialiseExportDirectory(context: Context): File {
        val btsExportDirectory = File("${context.cacheDir}/bts_export")
        if (!btsExportDirectory.exists()) {
            btsExportDirectory.mkdirs()
        } else {
            btsExportDirectory.listFiles()?.forEach { it.delete() }
        }
        return btsExportDirectory
    }
}
