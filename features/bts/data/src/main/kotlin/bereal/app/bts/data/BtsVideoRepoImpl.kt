package bereal.app.bts.data

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import bereal.app.bts.data.local.BtsVideoDataSource
import bereal.app.bts.data.local.BtsVideoExporter
import bereal.app.bts.data.remote.BtsRemoteDataSource
import bereal.app.bts.domain.repo.BtsVideoRepo
import bereal.app.common.Either
import bereal.app.common.failure
import bereal.app.common.success
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.error.GenericError
import bereal.app.videoprocessing.media3.transformer.readVideoCharacteristics
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import kotlin.time.Duration

@Single
@OptIn(UnstableApi::class)
class BtsVideoRepoImpl(
    private val btsRemoteDataSource: BtsRemoteDataSource,
    private val btsVideoDataSource: BtsVideoDataSource,
    private val context: Context,
    private val dispatcherProvider: DispatcherProvider,
    private val btsVideoExporter: BtsVideoExporter,
) : BtsVideoRepo {
    override suspend fun deleteBtsContent(postId: String): Either<GenericError, Unit> =
        btsRemoteDataSource.deleteBtsContent(postId)

    override fun observeHasViewedFirstBts(): Flow<Boolean> = btsVideoDataSource.btsSettings.map {
        it.hasViewedFirstBts
    }

    override suspend fun setHasViewedFirstBts(value: Boolean) =
        btsVideoDataSource.setHasViewedFirstBts(value)

    override suspend fun measureVideoLength(uri: String): Either<GenericError, Duration> =
        withContext(dispatcherProvider.ui) {
            try {
                readVideoCharacteristics(context, Uri.parse(uri)).duration.success()
            } catch (e: Exception) {
                GenericError.Unhandled(e).failure()
            }
        }

    override suspend fun exportForSharing(
        videoFilePath: String,
        primaryImageBitmap: Bitmap,
        secondaryImageBitmap: Bitmap,
        logoResId: Int,
        resultFileName: String,
        removeAudio: Boolean,
    ): Uri {
        return btsVideoExporter.exportForSharing(
            videoFilePath = videoFilePath,
            primaryImageBitmap = primaryImageBitmap,
            secondaryImageBitmap = secondaryImageBitmap,
            logoResId = logoResId,
            resultFileName = resultFileName,
            removeAudio = removeAudio,
        )
    }
}
