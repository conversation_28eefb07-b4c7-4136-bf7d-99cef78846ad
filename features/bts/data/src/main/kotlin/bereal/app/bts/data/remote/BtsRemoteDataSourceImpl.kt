package bereal.app.bts.data.remote

import bereal.app.common.Either
import bereal.app.data.core.request
import bereal.app.design.error.mapper.GenericErrorMapper
import bereal.app.entities.error.GenericError
import org.koin.core.annotation.Single

@Single
class BtsRemoteDataSourceImpl(
    private val api: BtsApi,
    private val genericErrorMapper: GenericErrorMapper,
) : BtsRemoteDataSource {
    override suspend fun deleteBtsContent(postId: String): Either<GenericError, Unit> =
        request {
            api.deleteBtsContent(postId)
        }.mapFailure(genericErrorMapper::mapToGeneric)
}
