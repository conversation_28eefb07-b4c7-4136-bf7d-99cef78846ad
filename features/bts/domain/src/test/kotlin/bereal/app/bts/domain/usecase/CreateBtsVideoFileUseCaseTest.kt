package bereal.app.bts.domain.usecase

import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.store.filestore.model.FileExtension
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.Test

class CreateBtsVideoFileUseCaseTest {

    private val fileStore: FileStore = mockk {
        every { <EMAIL>(any(), any<FileExtension>(), any(), any()) } returns mockk()
    }

    @Test
    fun `Should create the file with the correct parameters`() {
        // Given
        val timeStamp = System.currentTimeMillis()
        val useCase = CreateBtsVideoFileUseCase(fileStore)

        // When
        useCase(timeStamp)

        // Then
        verify {
            fileStore.file(
                fileName = "BtsVideo$timeStamp",
                fileExtension = FileExtension.MP4,
                destinationDir = FileCacheDir.BeRealMyUserLocalVideo,
                rootDir = FileStore.RootDirectory.FilesDirectory,
            )
        }
    }
}
