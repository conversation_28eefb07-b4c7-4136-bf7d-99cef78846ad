package bereal.app.bts.domain.usecase

import bereal.app.entities.settings.BerealSettingsEntity
import bereal.app.entities.settings.BtsSettingsEntity
import bereal.app.settings.repositories.SettingsRepository
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test

class GetBtsVideoMaxLengthUseCaseTest {

    private val settingsRepository: SettingsRepository = mockk()

    @Test
    fun `Should get the default video max length value when the setting is not defined`() = runTest {
        // Given
        every { settingsRepository.settings } returns flowOf(settingsFortBtsVideoMaxLength(null))

        // When
        val useCase = GetBtsVideoMaxLengthUseCase(settingsRepository)

        // Then
        useCase() shouldBe BTS_DEFAULT_MAX_VIDEO_LENGTH
    }

    @Test
    fun `Should get the settings video length value`() = runTest {
        // Given
        val value: Double = (0..10).toList().random().toDouble()
        every { settingsRepository.settings } returns flowOf(settingsFortBtsVideoMaxLength(value))

        // When
        val useCase = GetBtsVideoMaxLengthUseCase(settingsRepository)

        // Then
        useCase() shouldBe value
    }

    private fun settingsFortBtsVideoMaxLength(btsMaxLength: Double?) = mockk<BerealSettingsEntity>() {
        every { bts } returns BtsSettingsEntity(btsMaxLength)
    }
}
