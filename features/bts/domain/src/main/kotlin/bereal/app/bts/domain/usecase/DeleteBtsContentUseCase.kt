package bereal.app.bts.domain.usecase

import bereal.app.bts.domain.model.BtsEventAnalyticsDomainModel
import bereal.app.bts.domain.repo.BtsVideoRepo
import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import org.koin.core.annotation.Factory

@Factory
class DeleteBtsContentUseCase constructor(
    private val repository: BtsVideoRepo,
    private val analyticsUseCase: BtsAnalyticsUseCase,
) {
    suspend operator fun invoke(postId: String, analyticEvent: BtsEventAnalyticsDomainModel.RemoveBtsContent): Either<GenericError, Unit> =
        repository.deleteBtsContent(postId).also {
            analyticsUseCase(analyticEvent)
        }
}
