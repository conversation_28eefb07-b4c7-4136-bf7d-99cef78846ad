package bereal.app.bts.domain.usecase

import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.store.filestore.model.FileExtension
import org.koin.core.annotation.Factory
import java.io.File

@Factory
class CreateBtsVideoFileUseCase(
    private val fileStore: FileStore,
) {

    operator fun invoke(timeStampMs: Long): File = fileStore.file(
        fileName = "$BTS_VIDEO_FILE_PREFIX$timeStampMs",
        fileExtension = FileExtension.MP4,
        destinationDir = FileCacheDir.BeRealMyUserLocalVideo,
        rootDir = FileStore.RootDirectory.FilesDirectory,
    )
}
