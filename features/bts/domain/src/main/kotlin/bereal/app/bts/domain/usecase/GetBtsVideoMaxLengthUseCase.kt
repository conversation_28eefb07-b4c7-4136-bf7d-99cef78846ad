package bereal.app.bts.domain.usecase

import bereal.app.settings.repositories.SettingsRepository
import kotlinx.coroutines.flow.firstOrNull
import org.koin.core.annotation.Factory

@Factory
class GetBtsVideoMaxLengthUseCase(
    private val settingsRepository: SettingsRepository,
) {
    suspend operator fun invoke(): Double =
        settingsRepository.settings.firstOrNull()?.bts?.maxLength ?: BTS_DEFAULT_MAX_VIDEO_LENGTH
}
