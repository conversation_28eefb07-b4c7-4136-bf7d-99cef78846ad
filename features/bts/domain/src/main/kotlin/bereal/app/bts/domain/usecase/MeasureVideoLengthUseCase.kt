package bereal.app.bts.domain.usecase

import bereal.app.bts.domain.repo.BtsVideoRepo
import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import org.koin.core.annotation.Factory
import kotlin.time.Duration

@Factory
class MeasureVideoLengthUseCase(
    private val btsVideoRepo: BtsVideoRepo,
) {
    suspend operator fun invoke(videoFilePath: String): Either<GenericError, Duration> {
        return btsVideoRepo.measureVideoLength(videoFilePath)
    }
}
