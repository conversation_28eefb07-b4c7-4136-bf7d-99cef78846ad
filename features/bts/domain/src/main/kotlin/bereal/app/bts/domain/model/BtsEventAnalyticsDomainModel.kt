package bereal.app.bts.domain.model

import bereal.app.analytics.AEvent.Companion.KEY_BTS_ENABLED
import bereal.app.analytics.AEvent.Companion.KEY_IS_OFFICIAL_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_MEDIA_TYPE_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_MOMENT_ID_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_POST_ID_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_POST_TYPE_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_STATUS
import bereal.app.analytics.AEvent.Companion.KEY_USER_ID_VALUE
import bereal.app.analytics.AEvent.Companion.KEY_VIEW_VALUE
import bereal.app.analytics.model.AnalyticsEvent
import bereal.app.analytics.model.AnalyticsMediaType
import bereal.app.analytics.model.AnalyticsParam
import bereal.app.analytics.model.AnalyticsPostType
import bereal.app.analytics.model.AnalyticsView

sealed class BtsEventAnalyticsDomainModel(
    override val name: String,
    override val params: List<AnalyticsParam?> = emptyList(),
) : AnalyticsEvent {

    data class ActionToggleBTS(
        val status: Boolean,
        val view: AnalyticsView,
    ) : BtsEventAnalyticsDomainModel(
        name = "actionToggleBTS",
        params = listOf(
            AnalyticsParam(KEY_VIEW_VALUE, view.value),
            AnalyticsParam(KEY_STATUS, status),
        ),
    )

    data class ActionToggleBTSSound(
        val status: Boolean,
        val view: AnalyticsView,
    ) : BtsEventAnalyticsDomainModel(
        name = "actionToggleBTSSound",
        params = listOf(
            AnalyticsParam(KEY_VIEW_VALUE, view.value),
            AnalyticsParam(KEY_STATUS, status),
        ),
    )

    data class PlayVideo(
        val momentId: String,
        val userId: String,
        val postId: String,
        val isMain: Boolean,
        val isLate: Boolean,
        val duration: Float?,
        val view: AnalyticsView,
        val isOfficial: Boolean,
        val isReshare: Boolean,
    ) : BtsEventAnalyticsDomainModel(
        name = "actionPostPlayBTS",
        params = listOf(
            AnalyticsParam(KEY_MOMENT_ID_VALUE, momentId),
            AnalyticsParam(KEY_USER_ID_VALUE, userId),
            AnalyticsParam(KEY_VIEW_VALUE, view.value),
            AnalyticsParam(KEY_POST_ID_VALUE, postId),
            AnalyticsParam(
                KEY_POST_TYPE_VALUE,
                AnalyticsPostType.toPostType(isMain = isMain, isLate = isLate).value,
            ),
            AnalyticsParam(KEY_MEDIA_TYPE_VALUE, AnalyticsMediaType.Photo.value), // For now BTS are only associated with photo
            duration?.let { AnalyticsParam("duration", it) },
            AnalyticsParam(KEY_IS_OFFICIAL_VALUE, isOfficial),
        ),
    )

    data class RemoveBtsContent(
        val momentId: String,
        val postId: String,
        val isMain: Boolean,
        val isLate: Boolean,
        val view: AnalyticsView,
        val isReshare: Boolean,
    ) : BtsEventAnalyticsDomainModel(
        name = "actionRemoveBTS",
        params = listOf(
            AnalyticsParam(KEY_MOMENT_ID_VALUE, momentId),
            AnalyticsParam(KEY_VIEW_VALUE, view.value),
            AnalyticsParam(KEY_POST_ID_VALUE, postId),
            AnalyticsParam(
                KEY_POST_TYPE_VALUE,
                AnalyticsPostType.toPostType(isMain = isMain, isLate = isLate).value,
            ),
            AnalyticsParam(KEY_MEDIA_TYPE_VALUE, AnalyticsMediaType.Photo.value), // For now BTS are only associated with photo
        ),
    )

    data class MuteToggle(
        val isSoundOn: Boolean,
        val momentId: String,
        val userId: String,
        val postId: String,
        val isMain: Boolean,
        val isLate: Boolean,
        val duration: Float?,
        val view: AnalyticsView,
        val isReshare: Boolean,
    ) : BtsEventAnalyticsDomainModel(
        name = "actionTogglePostSound",
        params = listOf(
            AnalyticsParam(KEY_MOMENT_ID_VALUE, momentId),
            AnalyticsParam(KEY_USER_ID_VALUE, userId),
            AnalyticsParam(KEY_VIEW_VALUE, view.value),
            AnalyticsParam(KEY_POST_ID_VALUE, postId),
            AnalyticsParam(
                KEY_POST_TYPE_VALUE,
                AnalyticsPostType.toPostType(isMain = isMain, isLate = isLate).value,
            ),
            AnalyticsParam(KEY_MEDIA_TYPE_VALUE, AnalyticsMediaType.Photo.value), // For now BTS are only associated with photo
            AnalyticsParam("status", isSoundOn),
            duration?.let { AnalyticsParam("duration", it) },
            AnalyticsParam(KEY_BTS_ENABLED, true),

        ),
    )
}
