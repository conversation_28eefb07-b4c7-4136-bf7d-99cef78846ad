package bereal.app.bts.domain.repo

import android.graphics.Bitmap
import android.net.Uri
import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import kotlinx.coroutines.flow.Flow
import kotlin.time.Duration

interface BtsVideoRepo {
    suspend fun deleteBtsContent(postId: String): Either<GenericError, Unit>
    fun observeHasViewedFirstBts(): Flow<Boolean>
    suspend fun setHasViewedFirstBts(value: Boolean)
    suspend fun measureVideoLength(uri: String): Either<GenericError, Duration>
    suspend fun exportForSharing(
        videoFilePath: String,
        primaryImageBitmap: Bitmap,
        secondaryImageBitmap: Bitmap,
        logoResId: Int,
        resultFileName: String,
        removeAudio: Boolean,
    ): Uri
}
