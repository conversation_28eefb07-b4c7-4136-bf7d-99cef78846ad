package bereal.app.bts.domain.model

import kotlin.time.Duration

sealed interface VideoDurationDomainModel {

    data object Unknown : VideoDurationDomainModel

    data class Fetched(
        val duration: Duration,
    ) : VideoDurationDomainModel

    fun getDurationMs(): Long? {
        return when (this) {
            is Fetched -> duration.inWholeMilliseconds
            is Unknown -> null
        }
    }
}
