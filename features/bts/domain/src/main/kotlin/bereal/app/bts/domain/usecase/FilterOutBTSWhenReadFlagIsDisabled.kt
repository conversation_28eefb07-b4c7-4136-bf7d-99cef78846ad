package bereal.app.bts.domain.usecase

import bereal.app.entities.UserPosts
import bereal.app.settings.usecases.ObserveBTSFlagsUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
class FilterOutBTSWhenReadFlagIsDisabled(
    private val observeBTSFlagsUseCase: ObserveBTSFlagsUseCase,
) {
    operator fun invoke(usersPosts: List<UserPosts>): Flow<List<UserPosts>> =
        observeBTSFlagsUseCase().map { flags ->
            if (flags.readEnabled) {
                usersPosts
            } else {
                usersPosts.map { userPosts ->
                    userPosts.copy(
                        posts = userPosts.posts.map { post -> post.copy(btsContent = null) },
                    )
                }
            }
        }
}
