package bereal.app.bts.domain.player.all

import bereal.app.bts.domain.player.base.SimpleVideoPlayer
import org.koin.core.annotation.Single
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * Keeps a (weak) reference to all simple players with their video URL
 */
@Single
class AllSimpleVideoPlayers {
    private val allPlayers = ConcurrentHashMap<String /* video url*/, WeakReference<SimpleVideoPlayer>>()

    fun registerPlayer(videoUrl: String, player: SimpleVideoPlayer) {
        allPlayers[videoUrl] = WeakReference(player)
    }

    fun getPlayerForUrl(videoUrl: String): SimpleVideoPlayer? {
        return allPlayers[videoUrl]?.get()
    }

    fun unregisterPlayer(videoUrl: String) {
        allPlayers.remove(videoUrl)
    }
}
