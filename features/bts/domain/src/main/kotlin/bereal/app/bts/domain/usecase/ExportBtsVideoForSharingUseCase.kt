package bereal.app.bts.domain.usecase

import android.graphics.Bitmap
import android.net.Uri
import bereal.app.bts.domain.repo.BtsVideoRepo
import org.koin.core.annotation.Factory

@Factory
class ExportBtsVideoForSharingUseCase(
    private val btsVideoRepo: BtsVideoRepo,
) {
    suspend operator fun invoke(
        videoFilePath: String,
        primaryImageBitmap: Bitmap,
        secondaryImageBitmap: Bitmap,
        logoResId: Int,
        resultFileName: String,
        removeAudio: Boolean,
    ): Uri {
        return btsVideoRepo.exportForSharing(
            videoFilePath = videoFilePath,
            primaryImageBitmap = primaryImageBitmap,
            secondaryImageBitmap = secondaryImageBitmap,
            logoResId = logoResId,
            resultFileName = resultFileName,
            removeAudio = removeAudio,
        )
    }
}
