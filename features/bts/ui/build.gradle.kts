plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.compose.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
}

android {
    namespace = "bereal.app.bts.ui"
}

dependencies {
    implementation(libs.androidx.camera.video)
    implementation(libs.exoplayer)
    implementation(libs.media3.ui)
    implementation(libs.media3.common)
    implementation(libs.media3.transformer)
    implementation(libs.media3.effect)
    implementation(libs.media3.muxer)

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.common)
    implementation(projects.platform.haptic.core)
    implementation(projects.platform.remoteLogger.remoteLogger)
    implementation(projects.platform.store.filestore)

    implementation(projects.features.bts.domain)
    implementation(projects.platform.design.core)

    implementation(projects.features.video.ui)
    implementation(projects.features.video.domain)
    implementation(projects.features.video.data)

    testImplementation(libs.junit4)
}
