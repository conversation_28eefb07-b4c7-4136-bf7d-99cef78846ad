package bereal.app.bts.ui.player

import android.content.Context
import android.net.Uri
import androidx.compose.runtime.Immutable
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadRequest
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import bereal.app.analytics.ext.formatVideoDuration
import bereal.app.bts.domain.model.VideoDurationDomainModel
import bereal.app.bts.domain.player.all.AllSimpleVideoPlayers
import bereal.app.bts.domain.player.base.SimpleVideoPlayer
import bereal.app.bts.ui.model.DownloadState
import bereal.app.bts.ui.model.DownloaderAndMedia
import bereal.app.bts.ui.model.VideoAnalyticsParams
import bereal.app.bts.ui.model.mergeWith
import bereal.app.bts.ui.player.analytics.SimplePlayerAnalytics
import bereal.app.bts.ui.player.mute.PlayersMuteSingleState
import bereal.app.common.combines
import bereal.app.common.stateIn
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.video.domain.usecase.GetCacheVideoUseCase
import bereal.app.video.ui.ext.toDomain
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.SimpleVideoUiModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory
import timber.log.Timber
import kotlin.math.max
import kotlin.time.Duration.Companion.milliseconds

@Factory
@Immutable
@UnstableApi
class SimpleBeRealVideoPlayer(
    private val appContext: Context,
    private val dispatcherProvider: DispatcherProvider,
    private val simplePlayerAnalytics: SimplePlayerAnalytics,
    private val playersMuteSingleState: PlayersMuteSingleState,
    private val getCacheVideoUseCase: GetCacheVideoUseCase,
    private val allSimpleVideoPlayers: AllSimpleVideoPlayers,
    private val videosDownloadManager: VideosDownloadManager,
) : SimpleVideoPlayer {

    private val scope = CoroutineScope(dispatcherProvider.ui + SupervisorJob())
    private val downloaders = mutableListOf<DownloaderAndMedia>()

    private var currentPrimaryVideo: SimpleVideoUiModel? = null
    private var isPrimaryDownloaded = MutableStateFlow<DownloadState>(DownloadState.Idle)
    private var primaryDownloadProgressJob: Job? = null
    private var currentSecondaryVideo: SimpleVideoUiModel? = null
    private var secondaryDownloadProgressJob: Job? = null
    private var isSecondaryDownloaded = MutableStateFlow<DownloadState>(DownloadState.Idle)
    private var currentVideoAnalyticsParams: VideoAnalyticsParams? = null

    val downloadState: StateFlow<DownloadState> =
        combines(isPrimaryDownloaded, isSecondaryDownloaded)
            .map { (primary, secondary) -> computeIsLoading(primary, secondary) }
            .stateIn(
                scope,
                computeIsLoading(isPrimaryDownloaded.value, isSecondaryDownloaded.value),
            )

    private fun computeIsLoading(primary: DownloadState, secondary: DownloadState): DownloadState {
        return primary.mergeWith(secondary)
    }

    val primaryPlayer: ExoPlayer by lazy {
        buildExoPlayerInstance()
    }
    val secondaryPlayer: ExoPlayer by lazy {
        buildExoPlayerInstance()
    }

    private var isStopped: Boolean = false

    private val isPrimaryReady = MutableStateFlow<Boolean>(false)
    private val isSecondaryReady = MutableStateFlow<Boolean>(false)
    private val playWhenReady = MutableStateFlow<Boolean>(false)
    private val videoSync = MutableStateFlow<VideoSync?>(null)

    private val primaryEventListener: Player.Listener by lazy {
        object : Player.Listener {
            override fun onEvents(player: Player, events: Player.Events) {
                super.onEvents(player, events)
                if (events.contains(Player.EVENT_PLAYBACK_STATE_CHANGED) && player.playbackState == Player.STATE_READY) {
                    isPrimaryReady.value = true
                }
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                if (isPlaying) {
                    notifyPrimaryPlayerState(
                        state = BeRealVideoPlayerState.Playing,
                    )

                    if (primaryPlayer.repeatMode != Player.REPEAT_MODE_ONE) {
                        postMessageForNearStoppedState()
                    }
                } else {
                    if (primaryPlayer.playbackState == Player.STATE_ENDED) {
                        // loop manually the video, if I'm using the exoplayer.loopOnce it allocates TOOOOO many memory to be used in a feed with 2 players per page
                        if (!isStopped) {
                            primaryPlayer.seekTo(videoSync.value?.primaryOffset ?: 0)
                            primaryPlayer.play()
                        }

                        notifyPrimaryPlayerState(
                            state = BeRealVideoPlayerState.Stopped,
                        )
                    }
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                notifyPrimaryPlayerState(
                    state = BeRealVideoPlayerState.Stopped,
                )
                Timber.d("BeRealVideo onPlayerError: $error")
                Timber.tag("BeRealVideo").e(
                    "An error occured while playing video of post: $currentPrimaryVideo Error: $error",
                )
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_BUFFERING -> {
                        notifyPrimaryPlayerState(
                            state = BeRealVideoPlayerState.Loading,
                        )
                    }

                    Player.STATE_READY -> {
                        trackPlayVideo()
                    }
                }
            }
        }
    }

    private fun postMessageForNearStoppedState() {
        val duration = primaryPlayer.duration
        val position = duration - NEAR_STOP_LENGHT_IN_MS

        primaryPlayer.createMessage { _: Int, payload: Any? ->
            if (CUSTOM_EVENT_NEAR_STOP_PAYLOAD == payload.toString()) {
                notifyPrimaryPlayerState(
                    state = BeRealVideoPlayerState.NearStopped,
                )
            }
        }
            .setPosition(0, position)
            .setPayload(CUSTOM_EVENT_NEAR_STOP_PAYLOAD)
            .setDeleteAfterDelivery(false)
            .send()
    }

    private val secondaryEventListener: Player.Listener by lazy {
        object : Player.Listener {
            override fun onEvents(player: Player, events: Player.Events) {
                super.onEvents(player, events)
                if (events.contains(Player.EVENT_PLAYBACK_STATE_CHANGED) && player.playbackState == Player.STATE_READY) {
                    isSecondaryReady.value = true
                }
            }

            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                if (isPlaying) {
                    notifySecondaryPlayerState(
                        state = BeRealVideoPlayerState.Playing,
                    )
                } else {
                    if (secondaryPlayer.playbackState == Player.STATE_ENDED) {
                        if (!isStopped) {
                            secondaryPlayer.seekTo(videoSync.value?.secondaryOffset ?: 0)
                            secondaryPlayer.play()
                        }
                        notifySecondaryPlayerState(
                            state = BeRealVideoPlayerState.Stopped,
                        )
                    }
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                notifySecondaryPlayerState(
                    state = BeRealVideoPlayerState.Stopped,
                )
                Timber.d("BeRealVideo onPlayerError: $error")
                Timber.tag("BeRealVideo").e(
                    "An error occured while playing video of post: $currentSecondaryVideo Error: $error",
                )
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_BUFFERING -> {
                        notifySecondaryPlayerState(
                            state = BeRealVideoPlayerState.Loading,
                        )
                    }

                    Player.STATE_READY -> {
                        trackPlayVideo()
                    }

                    Player.STATE_ENDED -> {
                        secondaryPlayer.seekTo(0)
                        secondaryPlayer.play()
                    }
                }
            }
        }
    }

    private fun syncPlayersWhileReady(): VideoSync {
        val primaryVideoLenght = primaryPlayer.duration
        val secondaryVideoLenght = secondaryPlayer.duration
        if (primaryVideoLenght != secondaryVideoLenght) {
            val offset = primaryVideoLenght - secondaryVideoLenght
            when {
                offset == 0L -> {
                    // no-op
                }

                offset > 0L -> {
                    // primary content longer than secondary
                    return VideoSync(
                        primaryOffset = offset,
                        secondaryOffset = null,
                    )
                }

                else -> {
                    // secondary content longer than primary
                    return VideoSync(
                        primaryOffset = null,
                        secondaryOffset = -offset,
                    )
                }
            }
        }

        return VideoSync(
            primaryOffset = null,
            secondaryOffset = null,
        )
    }

    private data class VideoSync(
        val primaryOffset: Long?,
        val secondaryOffset: Long?,
    )

    private fun onMediaDownloaded(download: Download) {
        val requestUri = download.request.uri
        Timber.d("BeRealVideo DOWNLOAD_DOWNLOAD onMediaDownloaded($requestUri}")

        if (download.isPrimaryMedia()) {
            Timber.d("BeRealVideo DOWNLOAD_DOWNLOAD onMediaDownloaded($requestUri} <--- its the primary")

            isPrimaryDownloaded.value = DownloadState.Downloaded
            primaryDownloadProgressJob?.cancel()
            primaryDownloadProgressJob = null
        } else if (download.isSecondaryMedia()) {
            Timber.d("BeRealVideo DOWNLOAD_DOWNLOAD onMediaDownloaded($requestUri} <--- its the secondary")

            isSecondaryDownloaded.value = DownloadState.Downloaded
            secondaryDownloadProgressJob?.cancel()
            secondaryDownloadProgressJob = null
        }
    }

    private fun listenPrimaryProgress(download: Download, withStartDelay: Long? = null) {
        primaryDownloadProgressJob?.cancel()
        primaryDownloadProgressJob = scope.launch {
            withStartDelay?.let {
                delay(it)
            }
            var progress = download.percentDownloaded.coerceIn(minimumValue = 0f, maximumValue = 1f)
            while (progress < 1f - 0.01f) {
                isPrimaryDownloaded.update {
                    DownloadState.fromFloat(max(it.percent, progress))
                }
                delay(200)
                progress = download.percentDownloaded.coerceIn(minimumValue = 0f, maximumValue = 1f)
            }
            isPrimaryDownloaded.update { DownloadState.Downloaded }
        }
    }

    private fun listenSecondaryProgress(download: Download, withStartDelay: Long? = null) {
        secondaryDownloadProgressJob?.cancel()
        secondaryDownloadProgressJob = scope.launch {
            withStartDelay?.let {
                delay(it)
            }
            var progress = download.percentDownloaded.coerceIn(minimumValue = 0f, maximumValue = 1f)
            while (progress < 1f - 0.01f) {
                isSecondaryDownloaded.update {
                    DownloadState.fromFloat(max(it.percent, progress))
                }
                delay(200)
                progress = download.percentDownloaded.coerceIn(minimumValue = 0f, maximumValue = 1f)
            }
            isSecondaryDownloaded.update { DownloadState.Downloaded }
        }
    }

    private fun Download.isPrimaryMedia(): Boolean {
        return this.request.uri.toString() == currentPrimaryVideo?.uri
    }

    private fun Download.isSecondaryMedia(): Boolean {
        return this.request.uri.toString() == currentSecondaryVideo?.uri
    }

    private val downloadListener = object : DownloadManager.Listener {
        override fun onDownloadChanged(
            downloadManager: DownloadManager,
            download: Download,
            finalException: java.lang.Exception?,
        ) {
            Timber.d("BeRealVideo DOWNLOAD_DOWNLOAD ${download.state} error: ${finalException?.message}")
            if (download.state == Download.STATE_COMPLETED) {
                onMediaDownloaded(download)
            } else if (download.state == Download.STATE_QUEUED || download.state == Download.STATE_RESTARTING) {
                // adds a small delay to not display it directly
                if (download.isPrimaryMedia()) {
                    listenPrimaryProgress(download, withStartDelay = 500)
                } else if (download.isSecondaryMedia()) {
                    listenSecondaryProgress(download, withStartDelay = 500)
                }
            } else if (download.state == Download.STATE_DOWNLOADING) {
                if (download.isPrimaryMedia()) {
                    listenPrimaryProgress(download)
                } else if (download.isSecondaryMedia()) {
                    listenSecondaryProgress(download)
                }
            }
        }
    }

    init {
        scope.launch {
            combines(
                isPrimaryReady,
                isSecondaryReady,
                playWhenReady,
                videoSync,
                isPrimaryDownloaded,
                isSecondaryDownloaded,
            )
                .collect { (primaryReady, secondaryReady, playWhenReady, sync, isPrimaryDownloaded, isSecondaryDownloaded) ->
                    if (primaryReady && secondaryReady && isPrimaryDownloaded.isDownloaded() && isSecondaryDownloaded.isDownloaded()) {
                        if (sync == null) {
                            videoSync.value = syncPlayersWhileReady()
                        } else {
                            if (playWhenReady) {
                                sync.primaryOffset?.let {
                                    primaryPlayer.seekTo(it)
                                }
                                sync.secondaryOffset?.let {
                                    secondaryPlayer.seekTo(it)
                                }
                                primaryPlayer.playWhenReady = true
                                secondaryPlayer.playWhenReady = true
                            } else {
                                primaryPlayer.playWhenReady = false
                                secondaryPlayer.playWhenReady = false
                            }
                        }
                    }
                }
        }

        setVolumeDependingOnMute(playersMuteSingleState.playerMuteState.value)
        scope.launch {
            playersMuteSingleState.playerMuteState
                .collectLatest {
                    setVolumeDependingOnMute(it)
                }
        }
    }

    private fun setVolume(volume: Float) {
        primaryPlayer.volume = volume
        secondaryPlayer.volume = volume
    }

    private fun setVolumeDependingOnMute(muteValue: BeRealVideoPlayerMuteState) {
        when (muteValue) {
            is BeRealVideoPlayerMuteState.Muted -> {
                setVolume(0f)
            }

            is BeRealVideoPlayerMuteState.Unmuted -> {
                setVolume(1f)
            }
        }
    }

    val primaryPlayerState =
        MutableStateFlow<BeRealVideoPlayerState>(BeRealVideoPlayerState.Stopped)

    val playerMuteState: StateFlow<BeRealVideoPlayerMuteState> =
        playersMuteSingleState.playerMuteState

    suspend fun prepare(
        primary: SimpleVideoUiModel,
        secondary: SimpleVideoUiModel,
        videoAnalyticsParams: VideoAnalyticsParams?,
    ) {
        preparePrimary(data = primary, startAt = null)
        prepareSecondary(data = secondary, startAt = null)
        currentVideoAnalyticsParams = videoAnalyticsParams
    }

    fun play() {
        playWhenReady.value = true
    }

    fun resume() {
        primaryPlayer.play()
        secondaryPlayer.play()
    }

    private suspend fun preparePrimary(data: SimpleVideoUiModel, startAt: Long?) {
        currentPrimaryVideo = data
        isPrimaryDownloaded.value = DownloadState.Idle

        allSimpleVideoPlayers.registerPlayer(data.uri, this)

        primaryPlayer.playWhenReady = false
        primaryPlayer.addListener(primaryEventListener)

        val primaryDataSource = getDataSource(data, isPrimary = true)
        primaryPlayer.setMediaSource(primaryDataSource)
        primaryPlayer.prepare()
        if (startAt != null) {
            primaryPlayer.seekTo(startAt)
        }
    }

    fun pause() {
        notifyPrimaryPlayerState(state = BeRealVideoPlayerState.Paused)
        notifySecondaryPlayerState(state = BeRealVideoPlayerState.Paused)

        primaryPlayer.pause()
        secondaryPlayer.pause()

        playWhenReady.value = false
    }

    private fun stop() {
        notifyPrimaryPlayerState(state = BeRealVideoPlayerState.Stopped)
        notifySecondaryPlayerState(state = BeRealVideoPlayerState.Stopped)
        if ((primaryPlayer.isPlaying || secondaryPlayer.isPlaying)) {
            isStopped = true
            primaryPlayer.playWhenReady = false
            primaryPlayer.stop()
            secondaryPlayer.playWhenReady = false
            secondaryPlayer.stop()
        }
    }

    fun release() {
        stop()

        downloaders.forEach {
            it.downloadManager.removeListener(downloadListener)
        }
        downloaders.clear()

        primaryPlayer.release()
        secondaryPlayer.release()
        primaryDownloadProgressJob?.cancel()
        secondaryDownloadProgressJob?.cancel()

        currentPrimaryVideo?.uri?.let {
            allSimpleVideoPlayers.unregisterPlayer(it)
        }
        currentSecondaryVideo?.uri?.let {
            allSimpleVideoPlayers.unregisterPlayer(it)
        }

        scope.cancel()
    }

    fun toggleMute() {
        val shouldMute = primaryPlayer.volume == 1f

        playersMuteSingleState.toggleMute(shouldMute)

        simplePlayerAnalytics.trackMuteToggle(
            currentVideoAnalyticsParams = currentVideoAnalyticsParams,
            isSoundOn = !shouldMute,
            formatedDuration = getFormatedDuration(primaryPlayer.duration),
        )
    }

    override val isMuted: Boolean
        get() = playersMuteSingleState.playerMuteState.value == BeRealVideoPlayerMuteState.Muted

    private fun notifyPrimaryPlayerState(state: BeRealVideoPlayerState) {
        primaryPlayerState.value = state
    }

    private fun notifySecondaryPlayerState(state: BeRealVideoPlayerState) {
        secondaryPlayerState.update {
            state
        }
    }

    private fun getFormatedDuration(durationMs: Long): Float =
        formatVideoDuration(durationMs)
//endregion

    //region secondary player
    val secondaryPlayerState =
        MutableStateFlow<BeRealVideoPlayerState>(BeRealVideoPlayerState.Stopped)

    private suspend fun prepareSecondary(data: SimpleVideoUiModel, startAt: Long?) {
        currentSecondaryVideo = data
        isSecondaryDownloaded.value = DownloadState.Idle

        allSimpleVideoPlayers.registerPlayer(data.uri, this)

        secondaryPlayer.playWhenReady = false
        secondaryPlayer.addListener(secondaryEventListener)

        val dataSource = getDataSource(data, isPrimary = false)
        secondaryPlayer.setMediaSource(dataSource)
        secondaryPlayer.prepare()
        if (startAt != null) {
            secondaryPlayer.seekTo(startAt)
        }
    }

//endregion

    private fun trackPlayVideo() {
        simplePlayerAnalytics.trackPlayVideo(
            currentVideoAnalyticsParams = currentVideoAnalyticsParams,
            formatedDuration = getFormatedDuration(primaryPlayer.duration),
        )
    }

    val primaryDuration: StateFlow<VideoDurationDomainModel> = primaryPlayerState.map {
        when (it) {
            BeRealVideoPlayerState.Loading -> {
                VideoDurationDomainModel.Unknown
            }
            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Playing,
            BeRealVideoPlayerState.Stopped,
            BeRealVideoPlayerState.NearStopped,
            -> {
                primaryPlayer.duration
                    .takeIf { it != C.TIME_UNSET && it != Long.MAX_VALUE }
                    ?.let {
                        VideoDurationDomainModel.Fetched(it.milliseconds)
                    } ?: VideoDurationDomainModel.Unknown
            }
        }
    }.stateIn(scope, VideoDurationDomainModel.Unknown)

    val secondaryDuration: StateFlow<VideoDurationDomainModel> = secondaryPlayerState.map {
        when (it) {
            BeRealVideoPlayerState.Loading -> {
                VideoDurationDomainModel.Unknown
            }
            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Playing,
            BeRealVideoPlayerState.Stopped,
            BeRealVideoPlayerState.NearStopped,
            -> {
                secondaryPlayer.duration
                    .takeIf { it != C.TIME_UNSET && it != Long.MAX_VALUE }
                    ?.let {
                        VideoDurationDomainModel.Fetched(it.milliseconds)
                    } ?: VideoDurationDomainModel.Unknown
            }
        }
    }.stateIn(scope, VideoDurationDomainModel.Unknown)

    override fun observeVideoDuration(videoUrl: String): Flow<VideoDurationDomainModel> {
        return if (currentPrimaryVideo?.uri == videoUrl) {
            primaryDuration
        } else if (currentSecondaryVideo?.uri == videoUrl) {
            secondaryDuration
        } else {
            flowOf(VideoDurationDomainModel.Unknown)
        }
    }

    //region exo player
    private fun buildExoPlayerInstance(): ExoPlayer {
        return ExoPlayer.Builder(appContext)
            .incrementBufferSize()
            .build()
    }

    private suspend fun getDataSource(
        contentData: SimpleVideoUiModel,
        isPrimary: Boolean,
    ): MediaSource {
        val uri = Uri.parse(contentData.uri)
        val mediaItem = MediaItem.fromUri(uri)

        val cacheInstance = getCacheVideoUseCase(contentData.sourceType.toDomain())
        val upstreamFactory = DefaultDataSource.Factory(appContext)

        // completely download the videos before playing them
        downloadTheVideo(
            contentData = contentData,
            cache = cacheInstance.realCache,
            isPrimary = isPrimary,
        )

        val cacheFactory = CacheDataSource.Factory().apply {
            setCache(cacheInstance.realCache)
            setUpstreamDataSourceFactory(upstreamFactory)
            setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            /*
             * https://developer.android.com/media/media3/exoplayer/downloading-media
             * If the same player instance will also be used to play non-downloaded content
             * then the CacheDataSource.Factory should be configured as read-only to avoid downloading that content as well during playback.
             */
            setCacheWriteDataSinkFactory(null) // Disable writing
        }
        return ProgressiveMediaSource.Factory(
            cacheFactory,
        ).createMediaSource(mediaItem)
    }

    private suspend fun downloadTheVideo(
        contentData: SimpleVideoUiModel,
        cache: SimpleCache,
        isPrimary: Boolean,
    ) {
        val downloadManager = videosDownloadManager.getDownloadManager(cache = cache)

        val alreadyDownloadingMedia = downloadManager.currentDownloads.filter {
            if (isPrimary) it.isPrimaryMedia() else it.isSecondaryMedia()
        }.firstOrNull()

        val downloadRequest = DownloadRequest.Builder(contentData.uri, Uri.parse(contentData.uri))
            .build()

        downloadManager.addListener(downloadListener)
        downloadManager.addDownload(downloadRequest)
        downloadManager.resumeDownloads()

        if (isPrimary) {
            if (alreadyDownloadingMedia != null) {
                isPrimaryDownloaded.value = DownloadState.fromFloat(
                    alreadyDownloadingMedia.percentDownloaded.coerceIn(
                        minimumValue = 0f,
                        maximumValue = 1f,
                    ),
                )
            } else {
                primaryDownloadProgressJob?.cancel()
                primaryDownloadProgressJob = scope.launch {
                    delay(500)
                    isPrimaryDownloaded.value =
                        bereal.app.bts.ui.model.DownloadState.Downloading(0.001f)
                }
            }
        } else {
            if (alreadyDownloadingMedia != null) {
                isSecondaryDownloaded.value = DownloadState.fromFloat(
                    alreadyDownloadingMedia.percentDownloaded.coerceIn(
                        minimumValue = 0f,
                        maximumValue = 1f,
                    ),
                )
            } else {
                secondaryDownloadProgressJob?.cancel()
                secondaryDownloadProgressJob = scope.launch {
                    delay(500)
                    isSecondaryDownloaded.value =
                        bereal.app.bts.ui.model.DownloadState.Downloading(0.001f)
                }
            }
        }

        downloaders.add(
            DownloaderAndMedia(
                downloadManager = downloadManager,
                downloadRequest = downloadRequest,
            ),
        )
    }

//endregion

    companion object {
        private const val CUSTOM_EVENT_NEAR_STOP_PAYLOAD = "near_stop_event"
        private const val NEAR_STOP_LENGHT_IN_MS = 200
    }
}
