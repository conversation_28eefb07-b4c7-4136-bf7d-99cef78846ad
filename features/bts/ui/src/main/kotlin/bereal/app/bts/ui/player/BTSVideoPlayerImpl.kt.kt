package bereal.app.bts.ui.player

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.ColorSpace
import android.media.MediaPlayer
import android.net.Uri
import androidx.compose.runtime.Immutable
import androidx.media3.common.C.TRACK_TYPE_VIDEO
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.Clock
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.datasource.DataSourceBitmapLoader
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.effect.ScaleAndRotateTransformation
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.MetadataRetriever
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.transformer.Composition
import androidx.media3.transformer.DefaultAssetLoaderFactory
import androidx.media3.transformer.DefaultDecoderFactory
import androidx.media3.transformer.EditedMediaItem
import androidx.media3.transformer.Effects
import androidx.media3.transformer.ExportException
import androidx.media3.transformer.ExportResult
import androidx.media3.transformer.Transformer
import bereal.app.analytics.ext.formatVideoDuration
import bereal.app.analytics.model.AnalyticsView
import bereal.app.bts.domain.model.BtsEventAnalyticsDomainModel
import bereal.app.bts.domain.usecase.BtsAnalyticsUseCase
import bereal.app.common.await
import bereal.app.common.getOrNull
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.haptic.BeRealHaptics
import bereal.app.haptic.model.HapticType
import bereal.app.remote.logger.RemoteLogger
import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.store.filestore.model.FileExtension
import bereal.app.video.data.local.BeRealHeadersMedia3CacheFactoryProvider
import bereal.app.video.domain.usecase.GetCacheVideoUseCase
import bereal.app.video.ui.ext.toDomain
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.VideoUiModel
import bereal.app.video.ui.model.VideoUiModelContainerDataUiModel
import com.google.common.util.concurrent.MoreExecutors
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import timber.log.Timber
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.coroutines.suspendCoroutine
import kotlin.math.max

@Single
@Immutable
@UnstableApi
class BTSVideoPlayerImpl(
    private val appContext: Context,
    private val analyticsUseCase: BtsAnalyticsUseCase,
    private val remoteLogger: RemoteLogger,
    private val haptics: BeRealHaptics,
    private val getCacheBtsVideoUseCase: GetCacheVideoUseCase,
    private val dispatcherProvider: DispatcherProvider,
    private val fileStore: FileStore,
    private val beRealHeadersMedia3CacheFactoryProvider: BeRealHeadersMedia3CacheFactoryProvider,
) : BTSVideoPlayer {

    private val playerEventListener: Player.Listener by lazy {
        object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                if (isPlaying) {
                    notifyPlayerState(
                        state = BeRealVideoPlayerState.Playing,
                    )

                    postMessageForNearStoppedState()
                } else {
                    if (player.playbackState == Player.STATE_ENDED) {
                        notifyPlayerState(
                            state = BeRealVideoPlayerState.Stopped,
                        )
                    }
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                notifyPlayerState(
                    state = BeRealVideoPlayerState.Stopped,
                )
                remoteLogger.logUserError(
                    "BeRealVideo",
                    "An error occured while playing video of post: ${currentDataContainer?.id} Error: $error",
                )
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_BUFFERING -> {
                        notifyPlayerState(
                            state = BeRealVideoPlayerState.Loading,
                        )
                    }

                    Player.STATE_READY -> {
                        trackPlayVideo(currentDataContainer)
                    }
                }
            }
        }
    }

    private fun postMessageForNearStoppedState() {
        val duration = player.duration
        val position = duration - NEAR_STOP_LENGTH_IN_MS

        player.createMessage { _: Int, payload: Any? ->
            if (CUSTOM_EVENT_NEAR_STOP_PAYLOAD == payload.toString()) {
                notifyPlayerState(
                    state = BeRealVideoPlayerState.NearStopped,
                )
            }
        }
            .setPosition(0, position)
            .setPayload(CUSTOM_EVENT_NEAR_STOP_PAYLOAD)
            .setDeleteAfterDelivery(false)
            .send()
    }

    private var currentDataContainer: VideoUiModelContainerDataUiModel? = null
    private var currentDataContent: String? = null

    override val player: ExoPlayer by lazy {
        buildExoPlayerInstance().also {
            it.addListener(playerEventListener)
        }
    }

    private val playerState = MutableStateFlow<BeRealVideoPlayerState>(BeRealVideoPlayerState.Stopped)
    private val playerMuteState =
        MutableStateFlow<BeRealVideoPlayerMuteState>(BeRealVideoPlayerMuteState.Unmuted)

    //region primary player
    override fun observePlayerState(containerId: String): Flow<BeRealVideoPlayerState> = playerState
        .filter {
            currentDataContainer?.id == containerId
        }

    override fun observePlayerMuteState(): Flow<BeRealVideoPlayerMuteState> = playerMuteState

    override suspend fun play(data: VideoUiModel) {
        haptics.perform(HapticType.Click)
        val content = data.uri
        if (currentDataContainer?.id != data.containerData.id || currentDataContent != content) {
            currentDataContainer = data.containerData
            currentDataContent = content

            player.playWhenReady = true

            val measuredMediaSource = getDataSource(data)
            player.setMediaSource(measuredMediaSource.mediaSource)
            player.prepare()
            seekToBtsStartPosition(data.maxBtsLength, measuredMediaSource.duration)
        } else {
            player.prepare()
            seekToBtsStartPosition(data.maxBtsLength, player.duration)
        }
    }

    /**
     * TODO alexandre D.
     * This solution is NOT PERMANENT !!!
     * This should be moved in an use case and be done before caching
     * Though hin order to focus on other blockers and critical bugs I let that here for now!
     */
    private suspend fun getMediaItem(
        uri: Uri,
        cache: SimpleCache,
        mediaSourceFactory: MediaSource.Factory,
    ): MediaItem {
        val mediaItem = MediaItem.fromUri(uri)

        var forceRotation = false

        // Get rotation information through the MetadataRetriever
        // Use our own mediaSourceFactory to be sure the MetadataRetriever check our cache.
        // If not cache it will download and cache itself in our cache.
        val trackGroupsFuture = MetadataRetriever.retrieveMetadata(mediaSourceFactory, mediaItem)
        val result = trackGroupsFuture.await(timeout = 1000, unit = TimeUnit.MILLISECONDS)
        result.getOrNull()?.let { trackGroupArray ->
            for (trackIndex in 0 until trackGroupArray.length) {
                val trackGroup = trackGroupArray.get(trackIndex)
                if (trackGroup.type == TRACK_TYPE_VIDEO) {
                    for (formatIndex in 0 until trackGroup.length) {
                        val format = trackGroup.getFormat(formatIndex)
                        forceRotation = format.rotationDegrees == 0 && format.height < format.width
                    }
                }
            }
        }

        if (!forceRotation) {
            return mediaItem
        } else {
            // if rotation is needed:
            // prepare a local file to store the transformed video.
            val videoName = FileStore.createLocalPath(uri.toString())
            val transformedOuputFilePath = fileStore.file(
                "${videoName}_rotated",
                FileExtension.MP4,
                FileCacheDir.BeRealUserVideoCache,
            ).absolutePath

            // Check if the video has already be transformed and cached.
            return if (cache.isCached(/* key = */ transformedOuputFilePath, /* position */ 0, /* lenght */ 0)) {
                MediaItem.fromUri(transformedOuputFilePath)
            } else {
                // If video is not cached:
                // Apply rotation effect and use a transformer to apply this effect
                val newMediaItem = suspendCoroutine { continuation ->
                    try {
                        val editedMediaItem = EditedMediaItem.Builder(mediaItem)
                            .setEffects(
                                Effects(
                                    /* audioProcessors= */ listOf(),
                                    /* videoEffects= */ listOf(
                                        ScaleAndRotateTransformation.Builder()
                                            .setRotationDegrees(-90f)
                                            .build(),
                                    ),
                                ),
                            ).build()

                        // copied from DefaultAssetLoaderFactory constructor
                        var options: BitmapFactory.Options? = null
                        if (Util.SDK_INT >= 26) {
                            options = BitmapFactory.Options()
                            options.inPreferredColorSpace = ColorSpace.get(ColorSpace.Named.SRGB)
                        }
                        val bitmapLoader = DataSourceBitmapLoader(
                            MoreExecutors.listeningDecorator(
                                Executors.newSingleThreadExecutor(),
                            ),
                            DefaultDataSource.Factory(appContext), options,
                        )

                        // Provide a DefaultAssetLoaderFactory with our own mediaSourceFactory
                        // to be sure the transformer use the cache data if it exists
                        val assetLoaderFactor = DefaultAssetLoaderFactory(
                            /* context */ appContext,
                            /* decoderFactory */ DefaultDecoderFactory(appContext),
                            /* clock */ Clock.DEFAULT,
                            /* mediaSourceFactory */ mediaSourceFactory,
                            bitmapLoader,
                        )

                        val transformer = Transformer.Builder(appContext)
                            .setAssetLoaderFactory(assetLoaderFactor)
                            .addListener(object : Transformer.Listener {
                                override fun onCompleted(
                                    composition: Composition,
                                    exportResult: ExportResult,
                                ) {
                                    super.onCompleted(composition, exportResult)
                                    continuation.resumeWith(Result.success(MediaItem.fromUri(transformedOuputFilePath)))
                                }

                                override fun onError(
                                    composition: Composition,
                                    exportResult: ExportResult,
                                    exportException: ExportException,
                                ) {
                                    super.onError(composition, exportResult, exportException)
                                    continuation.resumeWith(Result.failure(exportException))
                                }
                            })
                            .build()

                        transformer.start(editedMediaItem, transformedOuputFilePath)
                    } catch (e: Exception) {
                        Timber.e(e)
                        continuation.resumeWith(Result.failure(e))
                    }
                }
                return newMediaItem
            }
        }
    }

    private fun seekToBtsStartPosition(maxBtsLength: Long, videoDuration: Long) {
        val seekTo = if (maxBtsLength > 0) {
            max(0, videoDuration - maxBtsLength)
        } else {
            0
        }
        player.seekTo(seekTo)
    }

    override fun pause() {
        notifyPlayerState(state = BeRealVideoPlayerState.Paused)
        player.pause()
    }

    override fun stop() {
        notifyPlayerState(state = BeRealVideoPlayerState.Stopped)
        player.stop()
    }

    override suspend fun setMute(muteState: BeRealVideoPlayerMuteState) {
        withContext(dispatcherProvider.ui) {
            player.volume = when (muteState) {
                BeRealVideoPlayerMuteState.Muted -> 0f
                BeRealVideoPlayerMuteState.Unmuted -> 1f
            }
        }

        playerMuteState.update {
            muteState
        }
    }
    override suspend fun toggleMute(containerData: VideoUiModelContainerDataUiModel?) {
        val shouldMute = withContext(dispatcherProvider.ui) {
            val shouldMute = player.volume == 1f
            player.volume = if (shouldMute) 0f else 1f
            return@withContext shouldMute
        }

        playerMuteState.update {
            if (shouldMute) {
                BeRealVideoPlayerMuteState.Muted
            } else {
                BeRealVideoPlayerMuteState.Unmuted
            }
        }

        trackMuteToggle(!shouldMute, containerData)
    }

    private fun notifyPlayerState(state: BeRealVideoPlayerState) {
        playerState.update {
            state
        }
    }

    private fun getFormatedDuration(durationMs: Long): Float =
        formatVideoDuration(durationMs)
    //endregion

    private fun trackPlayVideo(containerData: VideoUiModelContainerDataUiModel?) {
        when (containerData) {
            is VideoUiModelContainerDataUiModel.PostContainer -> {
                if (containerData.view == AnalyticsView.CameraCapture) return
                val event = BtsEventAnalyticsDomainModel.PlayVideo(
                    momentId = containerData.momentId,
                    userId = containerData.userId,
                    postId = containerData.postId,
                    isMain = containerData.isMain,
                    isLate = containerData.isLate,
                    isOfficial = containerData.isOfficialUser,
                    view = containerData.view,
                    duration = getFormatedDuration(player.duration),
                    isReshare = containerData.isReshare,
                )

                analyticsUseCase(event)
            }

            else -> {
                // no other kind of container for now
            }
        }
    }

    private fun trackMuteToggle(
        isSoundOn: Boolean,
        containerData: VideoUiModelContainerDataUiModel?,
    ) {
        when (containerData) {
            is VideoUiModelContainerDataUiModel.PostContainer -> {
                val event =
                    if (containerData.view == AnalyticsView.CameraSendBereal || containerData.view == AnalyticsView.CameraCapture) {
                        BtsEventAnalyticsDomainModel.ActionToggleBTSSound(
                            view = containerData.view,
                            status = isSoundOn,
                        )
                    } else {
                        BtsEventAnalyticsDomainModel.MuteToggle(
                            isSoundOn = isSoundOn,
                            momentId = containerData.momentId,
                            userId = containerData.userId,
                            postId = containerData.postId,
                            isMain = containerData.isMain,
                            isLate = containerData.isLate,
                            view = containerData.view,
                            duration = getFormatedDuration(player.duration),
                            isReshare = containerData.isReshare,
                        )
                    }

                analyticsUseCase(event)
            }

            else -> {
                // no other kind of container for now
            }
        }
    }

    //region exo player
    private fun buildExoPlayerInstance(): ExoPlayer {
        return ExoPlayer.Builder(appContext).build()
        // TODO Check LATER if it improves video consumption
        // val renderersFactory = DefaultRenderersFactory(context)
        //     .forceEnableMediaCodecAsynchronousQueueing()
        // val exoPlayer = ExoPlayer.Builder(context, renderersFactory).build()
    }

    private suspend fun getDataSource(contentData: VideoUiModel): MeasuredMediaSource {
        val uri = Uri.parse(contentData.uri)
        val videoDurationMs = if (uri.isRemote()) {
            0L
        } else {
            val player: MediaPlayer? = MediaPlayer.create(appContext, uri)
            player?.duration?.toLong()?.also {
                player.release()
            } ?: 0L
        }

        val cacheInstance = getCacheBtsVideoUseCase(contentData.sourceType.toDomain())
        val upstreamFactory = DefaultDataSource.Factory(
            appContext,
            beRealHeadersMedia3CacheFactoryProvider.provideCacheDataSourceFactory(),
        )
        val cacheFactory = CacheDataSource.Factory().apply {
            setCache(cacheInstance.realCache)
            // upstreamFactory is needed to either download video that has not been cached (should not happen)
            // and get video that has been transformed locally (rotation issue)
            setUpstreamDataSourceFactory(upstreamFactory)
            setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
        }

        val mediaSourceFactory = ProgressiveMediaSource.Factory(
            cacheFactory,
        )

        val mediaItem = runCatching {
            getMediaItem(
                uri = uri,
                cache = cacheInstance.realCache,
                mediaSourceFactory = mediaSourceFactory,
            )
        }
            .getOrNull()
            ?: run {
                Timber.e(RotateVideoException())
                MediaItem.fromUri(uri)
            }

        val mediaSource = mediaSourceFactory.createMediaSource(mediaItem)
        return MeasuredMediaSource(
            mediaSource = mediaSource,
            duration = videoDurationMs,
        )
    }
    //endregion

    private data class MeasuredMediaSource(
        val mediaSource: MediaSource,
        val duration: Long,
    )

    private fun Uri.isRemote(): Boolean {
        return scheme?.startsWith("http") == true
    }

    companion object {
        private const val CUSTOM_EVENT_NEAR_STOP_PAYLOAD = "near_stop_event"
        private const val NEAR_STOP_LENGTH_IN_MS = 200
    }

    class RotateVideoException : Exception()
}
