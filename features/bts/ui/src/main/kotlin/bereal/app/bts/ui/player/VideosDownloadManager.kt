@file:OptIn(UnstableApi::class)

package bereal.app.bts.ui.player

import android.content.Context
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.offline.DownloadManager
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.koin.core.annotation.Single
import java.util.concurrent.Executor

@Single
class VideosDownloadManager(
    private val appContext: Context,
    private val standaloneDatabaseProvider: StandaloneDatabaseProvider,
) {
    private val downloadManager = mutableMapOf<SimpleCache, DownloadManager>()

    private val mutex = Mutex() // to be sure there's only 1 instance

    suspend fun getDownloadManager(cache: SimpleCache): DownloadManager {
        return mutex.withLock {
            downloadManager.getOrPut(cache) {
                DownloadManager(
                    appContext,
                    standaloneDatabaseProvider,
                    cache,
                    DefaultHttpDataSource.Factory(),
                    Executor {
                        try {
                            it.run()
                        } catch (t: Throwable) {
                            t.printStackTrace()
                        }
                    },
                )
            }
        }
    }
}
