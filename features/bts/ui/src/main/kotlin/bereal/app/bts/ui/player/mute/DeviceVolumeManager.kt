package bereal.app.bts.ui.player.mute

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import androidx.compose.runtime.Immutable
import bereal.app.common.withPrevious
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Single
import timber.log.Timber

private const val VOLUME_CHANGED_ACTION = "android.media.VOLUME_CHANGED_ACTION"

@Immutable
@Single
class DeviceVolumeManager(
    private val applicationContext: Context,
) {
    private val streamType = AudioManager.STREAM_MUSIC

    private val audioManager by lazy {
        applicationContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    val systemVolume by lazy {
        MutableStateFlow(audioManager.getStreamVolume(streamType))
    }

    val isIncrementingVolume: Flow<Boolean>
        get() = systemVolume.withPrevious()
            .map { (previous, actual) ->
                if (previous == null) false
                else previous < actual
            }

    private val intentFilter: IntentFilter = IntentFilter(VOLUME_CHANGED_ACTION)

    private val volumeChangeListener = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == VOLUME_CHANGED_ACTION) {
                systemVolume.value = audioManager.getStreamVolume(streamType)
            }
        }
    }

    fun start() {
        try {
            applicationContext.registerReceiver(volumeChangeListener, intentFilter)
        } catch (t: Throwable) {
            Timber.e(t)
        }
    }

    fun stop() {
        try {
            applicationContext.unregisterReceiver(volumeChangeListener)
        } catch (t: Throwable) {
            Timber.e(t)
        }
    }
}
