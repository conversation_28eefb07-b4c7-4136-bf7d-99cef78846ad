package bereal.app.bts.ui.player

import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.ExoPlayer

@UnstableApi
fun ExoPlayer.Builder.incrementBufferSize(): ExoPlayer.Builder {
    /* Instantiate a DefaultLoadControl.Builder. */
    val loadControlBuilder = DefaultLoadControl.Builder()

    /*How many milliseconds of media data to buffer at any time. */
    val loadControlBufferMs =
        DefaultLoadControl.DEFAULT_MAX_BUFFER_MS /* This is 50000 milliseconds in ExoPlayer 2.9.6 */

    /* Configure the DefaultLoadControl to use the same value for */
    loadControlBuilder.setBufferDurationsMs(
        loadControlBufferMs,
        loadControlBufferMs,
        DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_MS,
        DefaultLoadControl.DEFAULT_BUFFER_FOR_PLAYBACK_AFTER_REBUFFER_MS,
    )

    return this.setLoadControl(loadControlBuilder.build())
}
