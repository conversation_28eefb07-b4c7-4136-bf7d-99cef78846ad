package bereal.app.bts.ui.view

import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.video.domain.models.VideoCacheDomainModel
import bereal.app.video.domain.usecase.CacheVideoUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory
import kotlin.math.min

interface CachePreloaderDelegate {
    fun cacheVideoTimelineContent(
        scope: CoroutineScope,
        postIndex: Int,
        timelineVideoContent: TimelineVideoContent,
    )

    fun cacheAllVideoTimelineContent(scope: CoroutineScope, videos: List<CacheableVideoContent>)
}

typealias TimelineVideoContent = List<List<List<CacheableVideoContent>>>

typealias VideoUrl = String

data class CacheableVideoContent(
    val url: VideoUrl,
    val videoSourceType: VideoCacheDomainModel,
)

/**
 * TimelineVideoContent is a mapping 1 -> 1 of a "list of post of a user" to a "list of video url"
 * It a list of list because posts can be (were?) grouped by user posts
 * Now it is only 1 post as we don't group post per user, though we still have the structure like that so list of list.
 * UserPost
 *    Post
 *       Bts
 *       primaryMedia
 *       seocndaryMedia
 *  List of userPosts > List of Post (but only one item now) > List of video urls (bts, primary, secondary) empty if no videos.
 *  It is important to keep a 1-1 mapping to keep the index/offset logic.
 */
@Factory
internal class CachePreloaderDelegateImpl(
    private val cacheVideoUseCase: CacheVideoUseCase,
    private val dispatcherProvider: DispatcherProvider,
) : CachePreloaderDelegate {

    private var cacheNext: Job? = null
    override fun cacheVideoTimelineContent(
        scope: CoroutineScope,
        postIndex: Int,
        timelineVideoContent: TimelineVideoContent,
    ) {
        cacheNext?.cancel()
        cacheNext = scope.launch(dispatcherProvider.data) {
            delay(200)
            ensureActive()
            for (i in postIndex..min(
                postIndex + OFFSET_POSTS_TO_CACHE,
                timelineVideoContent.lastIndex,
            )) {
                timelineVideoContent[i].forEach { videoPostsRow ->
                    videoPostsRow.forEach { videoPost ->
                        cacheVideoUseCase(
                            videoPost.url,
                            videoPost.videoSourceType,
                        )
                    }
                }
            }
        }
    }

    override fun cacheAllVideoTimelineContent(
        scope: CoroutineScope,
        videos: List<CacheableVideoContent>,
    ) {
        cacheNext?.cancel()
        cacheNext = scope.launch(dispatcherProvider.data) {
            delay(200)
            ensureActive()
            videos.forEach { videoPost ->
                cacheVideoUseCase(
                    videoPost.url,
                    videoPost.videoSourceType,
                )
            }
        }
    }

    companion object {
        private const val OFFSET_POSTS_TO_CACHE = 3
    }
}
