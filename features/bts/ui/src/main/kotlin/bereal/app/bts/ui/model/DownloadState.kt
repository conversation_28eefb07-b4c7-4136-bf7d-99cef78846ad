package bereal.app.bts.ui.model

import androidx.compose.runtime.Immutable

@Immutable
sealed interface DownloadState {
    val percent: Float

    @Immutable
    data object Idle : DownloadState {
        override val percent: Float = 0f
    }

    @Immutable
    data class Downloading(override val percent: Float) : DownloadState

    @Immutable
    data object Downloaded : DownloadState {
        override val percent: Float = 1f
    }

    companion object {
        fun fromFloat(percent: Float): DownloadState {
            return when (percent) {
                0f -> DownloadState.Idle
                1f -> DownloadState.Downloaded
                else -> DownloadState.Downloading(percent)
            }
        }
    }

    fun isDownloaded() = this is Downloaded
}

fun DownloadState.mergeWith(other: DownloadState): DownloadState {
    return when (val value = (this.percent + other.percent) / 2f) {
        0f -> DownloadState.Idle
        1f -> DownloadState.Downloaded
        else -> DownloadState.Downloading(value)
    }
}
