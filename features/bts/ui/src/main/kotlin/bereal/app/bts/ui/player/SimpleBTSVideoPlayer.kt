package bereal.app.bts.ui.player

import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.ColorSpace
import android.media.MediaPlayer
import android.net.Uri
import androidx.compose.runtime.Immutable
import androidx.media3.common.C
import androidx.media3.common.C.TRACK_TYPE_VIDEO
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.Clock
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.datasource.DataSourceBitmapLoader
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.effect.ScaleAndRotateTransformation
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.MetadataRetriever
import androidx.media3.exoplayer.offline.Download
import androidx.media3.exoplayer.offline.DownloadManager
import androidx.media3.exoplayer.offline.DownloadRequest
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.transformer.Composition
import androidx.media3.transformer.DefaultAssetLoaderFactory
import androidx.media3.transformer.DefaultDecoderFactory
import androidx.media3.transformer.EditedMediaItem
import androidx.media3.transformer.Effects
import androidx.media3.transformer.ExportException
import androidx.media3.transformer.ExportResult
import androidx.media3.transformer.Transformer
import bereal.app.analytics.ext.formatVideoDuration
import bereal.app.bts.domain.model.VideoDurationDomainModel
import bereal.app.bts.domain.player.all.AllSimpleVideoPlayers
import bereal.app.bts.domain.player.base.SimpleVideoPlayer
import bereal.app.bts.ui.model.DownloadState
import bereal.app.bts.ui.model.DownloaderAndMedia
import bereal.app.bts.ui.model.VideoAnalyticsParams
import bereal.app.bts.ui.player.analytics.SimplePlayerAnalytics
import bereal.app.bts.ui.player.mute.PlayersMuteSingleState
import bereal.app.common.await
import bereal.app.common.getOrNull
import bereal.app.common.stateIn
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.remote.logger.RemoteLogger
import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.store.filestore.model.FileExtension
import bereal.app.video.data.local.BeRealHeadersMedia3CacheFactoryProvider
import bereal.app.video.domain.usecase.GetCacheVideoUseCase
import bereal.app.video.ui.ext.toDomain
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.SimpleVideoUiModel
import com.google.common.util.concurrent.MoreExecutors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory
import timber.log.Timber
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlin.coroutines.suspendCoroutine
import kotlin.math.max
import kotlin.time.Duration.Companion.milliseconds

/*
    Copy of BTSVideoPlayerImpl.kt (which is single) but here with a @Factory
    removed the parent interface BTSVideoPlayer
    removed - VideoUiModelContainerDataUiModel
 */
@Factory
@Immutable
@UnstableApi
class SimpleBTSVideoPlayer(
    private val appContext: Context,
    private val remoteLogger: RemoteLogger,
    private val simplePlayerAnalytics: SimplePlayerAnalytics,
    private val getCacheBtsVideoUseCase: GetCacheVideoUseCase,
    private val dispatcherProvider: DispatcherProvider,
    private val fileStore: FileStore,
    private val allSimpleVideoPlayers: AllSimpleVideoPlayers,
    private val playersMuteSingleState: PlayersMuteSingleState,
    private val beRealHeadersMedia3CacheFactoryProvider: BeRealHeadersMedia3CacheFactoryProvider,
    private val videosDownloadManager: VideosDownloadManager,
) : SimpleVideoPlayer {

    private val downloaders = mutableListOf<DownloaderAndMedia>()

    private val scope = CoroutineScope(dispatcherProvider.ui + SupervisorJob())

    private var currentVideoAnalyticsParams: VideoAnalyticsParams? = null
    private var currentData: SimpleVideoUiModel? = null

    private val _playerState =
        MutableStateFlow<BeRealVideoPlayerState>(BeRealVideoPlayerState.Stopped)

    //region primary player
    val playerState: StateFlow<BeRealVideoPlayerState> = _playerState.asStateFlow()
    val playerMuteState: StateFlow<BeRealVideoPlayerMuteState> =
        playersMuteSingleState.playerMuteState

    private var primaryDownloadProgressJob: Job? = null
    var isPrimaryDownloaded = MutableStateFlow<DownloadState>(DownloadState.Idle)

    override val isMuted: Boolean
        get() = playersMuteSingleState.playerMuteState.value == BeRealVideoPlayerMuteState.Muted

    val player: ExoPlayer by lazy {
        buildExoPlayerInstance().also {
            it.addListener(playerEventListener)
        }
    }

    val duration: StateFlow<VideoDurationDomainModel> = playerState.map {
        when (it) {
            BeRealVideoPlayerState.Loading -> {
                VideoDurationDomainModel.Unknown
            }

            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Playing,
            BeRealVideoPlayerState.Stopped,
            BeRealVideoPlayerState.NearStopped,
            -> {
                player.duration
                    .takeIf { it != C.TIME_UNSET && it != Long.MAX_VALUE }
                    ?.let {
                        VideoDurationDomainModel.Fetched(it.milliseconds)
                    } ?: VideoDurationDomainModel.Unknown
            }
        }
    }.stateIn(scope, VideoDurationDomainModel.Unknown)

    private val playerEventListener: Player.Listener by lazy {
        object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                if (isPlaying) {
                    notifyPlayerState(
                        state = BeRealVideoPlayerState.Playing,
                    )

                    postMessageForNearStoppedState()
                } else {
                    if (player.playbackState == Player.STATE_ENDED) {
                        notifyPlayerState(
                            state = BeRealVideoPlayerState.Stopped,
                        )
                    }
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                notifyPlayerState(
                    state = BeRealVideoPlayerState.Stopped,
                )
                remoteLogger.logUserError(
                    "BeRealVideo",
                    "An error occured while playing video : ${currentData?.uri} Error: $error",
                )
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_BUFFERING -> {
                        notifyPlayerState(
                            state = BeRealVideoPlayerState.Loading,
                        )
                    }

                    Player.STATE_READY -> {
                        simplePlayerAnalytics.trackPlayBtsVideo(
                            currentVideoAnalyticsParams = currentVideoAnalyticsParams,
                            formatedDuration = getFormatedDuration(player.duration),
                        )
                    }
                }
            }
        }
    }

    private fun Download.isPrimaryMedia(): Boolean {
        return this.request.uri.toString() == currentData?.uri
    }

    private val downloadListener = object : DownloadManager.Listener {
        override fun onDownloadChanged(
            downloadManager: DownloadManager,
            download: Download,
            finalException: java.lang.Exception?,
        ) {
            Timber.d("BeRealVideo DOWNLOAD_DOWNLOAD ${download.state} error: ${finalException?.message}")
            if (download.state == Download.STATE_COMPLETED) {
                onMediaDownloaded(download)
            } else if (download.state == Download.STATE_QUEUED || download.state == Download.STATE_RESTARTING) {
                // adds a small delay to not display it directly
                if (download.isPrimaryMedia()) {
                    listenPrimaryProgress(download, withStartDelay = 500)
                }
            } else if (download.state == Download.STATE_DOWNLOADING) {
                if (download.isPrimaryMedia()) {
                    listenPrimaryProgress(download)
                }
            }
        }
    }

    private fun onMediaDownloaded(download: Download) {
        if (download.isPrimaryMedia()) {
            isPrimaryDownloaded.value = DownloadState.Downloaded
            primaryDownloadProgressJob?.cancel()
            primaryDownloadProgressJob = null
        }
    }

    private fun listenPrimaryProgress(download: Download, withStartDelay: Long? = null) {
        primaryDownloadProgressJob?.cancel()
        primaryDownloadProgressJob = scope.launch {
            withStartDelay?.let {
                delay(it)
            }
            var progress = download.percentDownloaded.coerceIn(minimumValue = 0f, maximumValue = 1f)
            while (progress < 1f - 0.01f) {
                isPrimaryDownloaded.update {
                    DownloadState.fromFloat(max(it.percent, progress))
                }
                delay(200)
                progress = download.percentDownloaded.coerceIn(minimumValue = 0f, maximumValue = 1f)
            }
            isPrimaryDownloaded.update { DownloadState.Downloaded }
        }
    }

    init {
        setVolumeDependingOnMute(playersMuteSingleState.playerMuteState.value)
        scope.launch {
            playersMuteSingleState.playerMuteState
                .collectLatest {
                    setVolumeDependingOnMute(it)
                }
        }
    }

    override fun observeVideoDuration(videoUrl: String): Flow<VideoDurationDomainModel> {
        return if (currentData?.uri == videoUrl) {
            duration
        } else {
            flowOf(VideoDurationDomainModel.Unknown)
        }
    }

    private fun setVolume(volume: Float) {
        player.volume = volume
    }

    private fun setVolumeDependingOnMute(muteValue: BeRealVideoPlayerMuteState) {
        when (muteValue) {
            is BeRealVideoPlayerMuteState.Muted -> {
                setVolume(0f)
            }

            is BeRealVideoPlayerMuteState.Unmuted -> {
                setVolume(1f)
            }
        }
    }

    private fun postMessageForNearStoppedState() {
        val duration = player.duration
        val position = duration - NEAR_STOP_LENGTH_IN_MS

        player.createMessage { _: Int, payload: Any? ->
            if (CUSTOM_EVENT_NEAR_STOP_PAYLOAD == payload.toString()) {
                notifyPlayerState(
                    state = BeRealVideoPlayerState.NearStopped,
                )
            }
        }
            .setPosition(0, position)
            .setPayload(CUSTOM_EVENT_NEAR_STOP_PAYLOAD)
            .setDeleteAfterDelivery(false)
            .send()
    }

    suspend fun prepare(data: SimpleVideoUiModel, videoAnalyticsParams: VideoAnalyticsParams?) {
        currentData = data
        currentVideoAnalyticsParams = videoAnalyticsParams

        allSimpleVideoPlayers.registerPlayer(data.uri, this)

        val measuredMediaSource = getDataSource(data)
        player.setMediaSource(measuredMediaSource.mediaSource)
        player.prepare()
        seekToBtsStartPosition(data.maxBtsLength, measuredMediaSource.duration)
    }

    fun play() {
        player.seekTo(0)
        player.playWhenReady = true
    }

    fun resume() {
        player.playWhenReady = true
    }

    /**
     * TODO alexandre D.
     * This solution is NOT PERMANENT !!!
     * This should be moved in an use case and be done before caching
     * Though hin order to focus on other blockers and critical bugs I let that here for now!
     */
    private suspend fun getMediaItem(
        uri: Uri,
        cache: SimpleCache,
        mediaSourceFactory: MediaSource.Factory,
    ): MediaItem {
        val mediaItem = MediaItem.fromUri(uri)

        var forceRotation = false

        // Get rotation information through the MetadataRetriever
        // Use our own mediaSourceFactory to be sure the MetadataRetriever check our cache.
        // If not cache it will download and cache itself in our cache.
        val trackGroupsFuture = MetadataRetriever.retrieveMetadata(mediaSourceFactory, mediaItem)
        val result = trackGroupsFuture.await(timeout = 1000, unit = TimeUnit.MILLISECONDS)
        result.getOrNull()?.let { trackGroupArray ->
            for (trackIndex in 0 until trackGroupArray.length) {
                val trackGroup = trackGroupArray.get(trackIndex)
                if (trackGroup.type == TRACK_TYPE_VIDEO) {
                    for (formatIndex in 0 until trackGroup.length) {
                        val format = trackGroup.getFormat(formatIndex)
                        forceRotation = format.rotationDegrees == 0 && format.height < format.width
                    }
                }
            }
        }

        if (!forceRotation) {
            return mediaItem
        } else {
            // if rotation is needed:
            // prepare a local file to store the transformed video.
            val videoName = FileStore.createLocalPath(uri.toString())
            val transformedOuputFilePath = fileStore.file(
                "${videoName}_rotated",
                FileExtension.MP4,
                FileCacheDir.BeRealUserVideoCache,
            ).absolutePath

            // Check if the video has already be transformed and cached.
            return if (cache.isCached(
                    /* key = */ transformedOuputFilePath, /* position */
                    0, /* lenght */
                    0,
                )
            ) {
                MediaItem.fromUri(transformedOuputFilePath)
            } else {
                // If video is not cached:
                // Apply rotation effect and use a transformer to apply this effect
                val newMediaItem = suspendCoroutine { continuation ->
                    try {
                        val editedMediaItem = EditedMediaItem.Builder(mediaItem)
                            .setEffects(
                                Effects(
                                    /* audioProcessors= */ listOf(),
                                    /* videoEffects= */
                                    listOf(
                                        ScaleAndRotateTransformation.Builder()
                                            .setRotationDegrees(-90f)
                                            .build(),
                                    ),
                                ),
                            ).build()

                        // copied from DefaultAssetLoaderFactory constructor
                        var options: BitmapFactory.Options? = null
                        if (Util.SDK_INT >= 26) {
                            options = BitmapFactory.Options()
                            options.inPreferredColorSpace = ColorSpace.get(ColorSpace.Named.SRGB)
                        }
                        val bitmapLoader = DataSourceBitmapLoader(
                            MoreExecutors.listeningDecorator(
                                Executors.newSingleThreadExecutor(),
                            ),
                            DefaultDataSource.Factory(appContext), options,
                        )

                        // Provide a DefaultAssetLoaderFactory with our own mediaSourceFactory
                        // to be sure the transformer use the cache data if it exists
                        val assetLoaderFactor = DefaultAssetLoaderFactory(
                            /* context */ appContext,
                            /* decoderFactory */ DefaultDecoderFactory(appContext),
                            /* clock */ Clock.DEFAULT,
                            /* mediaSourceFactory */ mediaSourceFactory,
                            bitmapLoader,
                        )

                        val transformer = Transformer.Builder(appContext)
                            .setAssetLoaderFactory(assetLoaderFactor)
                            .addListener(object : Transformer.Listener {
                                override fun onCompleted(
                                    composition: Composition,
                                    exportResult: ExportResult,
                                ) {
                                    super.onCompleted(composition, exportResult)
                                    continuation.resumeWith(
                                        Result.success(
                                            MediaItem.fromUri(
                                                transformedOuputFilePath,
                                            ),
                                        ),
                                    )
                                }

                                override fun onError(
                                    composition: Composition,
                                    exportResult: ExportResult,
                                    exportException: ExportException,
                                ) {
                                    super.onError(composition, exportResult, exportException)
                                    continuation.resumeWith(Result.failure(exportException))
                                }
                            })
                            .build()

                        transformer.start(editedMediaItem, transformedOuputFilePath)
                    } catch (e: Exception) {
                        Timber.e(e)
                        continuation.resumeWith(Result.failure(e))
                    }
                }
                return newMediaItem
            }
        }
    }

    private fun seekToBtsStartPosition(maxBtsLength: Long, videoDuration: Long) {
        val seekTo = if (maxBtsLength > 0) {
            max(0, videoDuration - maxBtsLength)
        } else {
            0
        }
        player.seekTo(seekTo)
    }

    fun pause() {
        notifyPlayerState(state = BeRealVideoPlayerState.Paused)
        player.pause()
    }

    fun stop() {
        notifyPlayerState(state = BeRealVideoPlayerState.Stopped)
        player.stop()
    }

    fun release() {
        player.release()

        downloaders.forEach {
            it.downloadManager.removeListener(downloadListener)
        }
        downloaders.clear()

        primaryDownloadProgressJob?.cancel()
        primaryDownloadProgressJob = null
        scope.cancel()

        currentData?.uri?.let {
            allSimpleVideoPlayers.unregisterPlayer(it)
        }
    }

    fun toggleMute() {
        val shouldMute = player.volume == 1f

        playersMuteSingleState.toggleMute(shouldMute)

        simplePlayerAnalytics.trackMuteToggle(
            isSoundOn = !shouldMute,
            currentVideoAnalyticsParams = currentVideoAnalyticsParams,
            formatedDuration = getFormatedDuration(player.duration),
        )
    }

    private fun notifyPlayerState(state: BeRealVideoPlayerState) {
        _playerState.update {
            state
        }
    }

    private fun getFormatedDuration(durationMs: Long): Float =
        formatVideoDuration(durationMs)
    //endregion

    //region exo player
    private fun buildExoPlayerInstance(): ExoPlayer {
        return ExoPlayer.Builder(appContext).build()
        // TODO Check LATER if it improves video consumption
        // val renderersFactory = DefaultRenderersFactory(context)
        //     .forceEnableMediaCodecAsynchronousQueueing()
        // val exoPlayer = ExoPlayer.Builder(context, renderersFactory).build()
    }

    private suspend fun getDataSource(contentData: SimpleVideoUiModel): MeasuredMediaSource {
        val uri = Uri.parse(contentData.uri)
        val videoDurationMs = if (uri.isRemote()) {
            0L
        } else {
            val player: MediaPlayer? = MediaPlayer.create(appContext, uri)
            player?.duration?.toLong()?.also {
                player.release()
            } ?: 0L
        }

        val cacheInstance = getCacheBtsVideoUseCase(contentData.sourceType.toDomain())
        val upstreamFactory = DefaultDataSource.Factory(
            appContext,
            beRealHeadersMedia3CacheFactoryProvider.provideCacheDataSourceFactory(),
        )

        downloadTheVideo(
            contentData = contentData,
            cache = cacheInstance.realCache,
        )

        val cacheFactory = CacheDataSource.Factory().apply {
            setCache(cacheInstance.realCache)
            // upstreamFactory is needed to either download video that has not been cached (should not happen)
            // and get video that has been transformed locally (rotation issue)
            setUpstreamDataSourceFactory(upstreamFactory)
            setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)

            /*
             * https://developer.android.com/media/media3/exoplayer/downloading-media
             * If the same player instance will also be used to play non-downloaded content
             * then the CacheDataSource.Factory should be configured as read-only to avoid downloading that content as well during playback.
             */
            setCacheWriteDataSinkFactory(null) // Disable writing
        }

        val mediaSourceFactory = ProgressiveMediaSource.Factory(
            cacheFactory,
        )

        val mediaItem = runCatching {
            getMediaItem(
                uri = uri,
                cache = cacheInstance.realCache,
                mediaSourceFactory = mediaSourceFactory,
            )
        }
            .getOrNull()
            ?: run {
                Timber.e(RotateVideoException())
                MediaItem.fromUri(uri)
            }

        val mediaSource = mediaSourceFactory.createMediaSource(mediaItem)
        return MeasuredMediaSource(
            mediaSource = mediaSource,
            duration = videoDurationMs,
        )
    }

    private suspend fun downloadTheVideo(
        contentData: SimpleVideoUiModel,
        cache: SimpleCache,
    ) {
        val downloadManager = videosDownloadManager.getDownloadManager(cache = cache)
        val alreadyDownloadingMedia = downloadManager.currentDownloads.firstOrNull {
            it.isPrimaryMedia()
        }

        val downloadRequest = DownloadRequest.Builder(contentData.uri, Uri.parse(contentData.uri))
            .build()

        downloadManager.addListener(downloadListener)
        downloadManager.addDownload(downloadRequest)
        downloadManager.resumeDownloads()

        if (alreadyDownloadingMedia != null) {
            isPrimaryDownloaded.value = DownloadState.fromFloat(
                alreadyDownloadingMedia.percentDownloaded.coerceIn(
                    minimumValue = 0f,
                    maximumValue = 1f,
                ),
            )
        } else {
            primaryDownloadProgressJob?.cancel()
            primaryDownloadProgressJob = scope.launch {
                delay(500)
                isPrimaryDownloaded.value =
                    bereal.app.bts.ui.model.DownloadState.Downloading(0.001f)
            }
        }

        downloaders.add(
            DownloaderAndMedia(
                downloadManager = downloadManager,
                downloadRequest = downloadRequest,
            ),
        )
    }
    //endregion

    private data class MeasuredMediaSource(
        val mediaSource: MediaSource,
        val duration: Long,
    )

    private fun Uri.isRemote(): Boolean {
        return scheme?.startsWith("http") == true
    }

    companion object {
        private const val CUSTOM_EVENT_NEAR_STOP_PAYLOAD = "near_stop_event"
        private const val NEAR_STOP_LENGTH_IN_MS = 200
    }

    class RotateVideoException : Exception()
}
