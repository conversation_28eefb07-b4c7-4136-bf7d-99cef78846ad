package bereal.app.bts.ui.view

import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.Dp
import androidx.media3.common.util.UnstableApi
import bereal.app.video.ui.VideoView
import bereal.app.video.ui.model.StablePlayer

@UnstableApi
@Composable
fun BtsView(
    containerId: String,
    aspectRatio: Float,
    cornerRadiusInDp: Dp,
    player: StablePlayer,
) {
    VideoView(
        modifier = Modifier
            .aspectRatio(aspectRatio)
            .clip(RoundedCornerShape(cornerRadiusInDp)),
        playerVideo = player,
        tag = containerId,
    )
}
