package bereal.app.bts.ui.model

import androidx.compose.runtime.Immutable
import bereal.app.bts.domain.model.VideoDurationDomainModel
import kotlin.time.Duration

@Immutable
sealed interface VideoDurationUiModel {
    @Immutable
    data object Unknown : VideoDurationUiModel

    @Immutable
    data class Fetched(
        val duration: Duration,
    ) : VideoDurationUiModel

    fun getDurationMs(): Long? {
        return when (this) {
            is Fetched -> duration.inWholeMilliseconds
            is Unknown -> null
        }
    }
}

fun toUi(domain: VideoDurationDomainModel): VideoDurationUiModel {
    return when (domain) {
        is VideoDurationDomainModel.Fetched -> VideoDurationUiModel.Fetched(
            duration = domain.duration,
        )
        is VideoDurationDomainModel.Unknown -> VideoDurationUiModel.Unknown
    }
}
