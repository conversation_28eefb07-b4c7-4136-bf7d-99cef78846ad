package bereal.app.bts.ui.player.analytics

import bereal.app.analytics.model.AnalyticsView
import bereal.app.bts.domain.model.BtsEventAnalyticsDomainModel
import bereal.app.bts.domain.usecase.BtsAnalyticsUseCase
import bereal.app.bts.ui.model.VideoAnalyticsParams
import bereal.app.video.domain.models.VideoEventAnalyticsDomainModel
import bereal.app.video.domain.usecase.VideoAnalyticsUseCase
import org.koin.core.annotation.Single

@Single
class SimplePlayerAnalytics(
    private val videoAnalyticsUseCase: VideoAnalyticsUseCase,
    private val btsAnalyticsUseCase: BtsAnalyticsUseCase,
) {

    fun trackPlayVideo(
        currentVideoAnalyticsParams: VideoAnalyticsParams?,
        formatedDuration: Float,
    ) {
        currentVideoAnalyticsParams?.let {
            videoAnalyticsUseCase(
                VideoEventAnalyticsDomainModel.PlayVideo(
                    momentId = it.momentId,
                    userId = it.userId,
                    postId = it.postId,
                    isMain = it.isMain,
                    isLate = it.isLate,
                    view = it.view,
                    duration = formatedDuration,
                    isReshare = it.isReshare,
                ),
            )
        }
    }

    fun trackMuteToggle(
        isSoundOn: Boolean,
        currentVideoAnalyticsParams: VideoAnalyticsParams?,
        formatedDuration: Float,
    ) {
        currentVideoAnalyticsParams?.let {
            videoAnalyticsUseCase(
                VideoEventAnalyticsDomainModel.MuteToggle(
                    momentId = it.momentId,
                    userId = it.userId,
                    postId = it.postId,
                    isMain = it.isMain,
                    isLate = it.isLate,
                    view = it.view,
                    duration = formatedDuration,
                    isReshare = it.isReshare,
                    isSoundOn = isSoundOn,
                ),
            )
        }
    }

    fun trackBtsMute(
        isSoundOn: Boolean,
        currentVideoAnalyticsParams: VideoAnalyticsParams?,
        formatedDuration: Float,
    ) {
        currentVideoAnalyticsParams?.let {
            val event =
                if (it.view == AnalyticsView.CameraSendBereal || it.view == AnalyticsView.CameraCapture) {
                    BtsEventAnalyticsDomainModel.ActionToggleBTSSound(
                        view = it.view,
                        status = isSoundOn,
                    )
                } else {
                    BtsEventAnalyticsDomainModel.MuteToggle(
                        isSoundOn = isSoundOn,
                        momentId = it.momentId,
                        userId = it.userId,
                        postId = it.postId,
                        isMain = it.isMain,
                        isLate = it.isLate,
                        view = it.view,
                        duration = formatedDuration,
                        isReshare = it.isReshare,
                    )
                }

            btsAnalyticsUseCase(event)
        }
    }

    fun trackPlayBtsVideo(
        currentVideoAnalyticsParams: VideoAnalyticsParams?,
        formatedDuration: Float,
    ) {
        currentVideoAnalyticsParams?.let {
            if (it.view == AnalyticsView.CameraCapture) return
            val event = BtsEventAnalyticsDomainModel.PlayVideo(
                momentId = it.momentId,
                userId = it.userId,
                postId = it.postId,
                isMain = it.isMain,
                isLate = it.isLate,
                isOfficial = it.isOfficialUser,
                view = it.view,
                duration = formatedDuration,
                isReshare = it.isReshare,
            )

            btsAnalyticsUseCase(event)
        }
    }
}
