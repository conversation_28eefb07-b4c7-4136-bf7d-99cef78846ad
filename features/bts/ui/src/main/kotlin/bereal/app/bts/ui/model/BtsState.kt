package bereal.app.bts.ui.model

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import bereal.app.bts.ui.player.BTSVideoPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerState
import kotlinx.coroutines.flow.Flow

data class BtsState(
    val player: BTSVideoPlayer,
    val containerId: String,
    var isPlayable: Boolean,
    var isLocked: Boolean,
) {
    val playerState: Flow<BeRealVideoPlayerState> = player.observePlayerState(containerId)

    private val _shouldPlay = mutableStateOf(false)
    val shouldPlay: State<Boolean> = _shouldPlay

    fun triggerPlay(value: Boolean) {
        _shouldPlay.value = isPlayable && !isLocked && value
    }
}
