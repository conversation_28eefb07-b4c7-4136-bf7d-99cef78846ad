package bereal.app.bts.ui.model

import androidx.compose.runtime.Immutable
import bereal.app.analytics.model.AnalyticsView

@Immutable
data class VideoAnalyticsParams(
    val momentId: String,
    val userId: String,
    val postId: String,
    val isMain: <PERSON><PERSON>an,
    val isLate: <PERSON><PERSON><PERSON>,
    val isReshare: <PERSON><PERSON>an,
    val isOfficialUser: <PERSON>olean,
    val view: AnalyticsView,
) {
    companion object {
        fun preview() = VideoAnalyticsParams(
            momentId = "",
            userId = "",
            postId = "",
            isMain = false,
            isLate = false,
            isReshare = false,
            isOfficialUser = false,
            view = AnalyticsView.TimelineFriends,
        )
    }
}
