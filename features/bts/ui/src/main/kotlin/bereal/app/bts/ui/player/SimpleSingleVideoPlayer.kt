package bereal.app.bts.ui.player

import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import androidx.compose.runtime.Immutable
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import bereal.app.analytics.ext.formatVideoDuration
import bereal.app.bts.domain.model.VideoDurationDomainModel
import bereal.app.bts.domain.player.all.AllSimpleVideoPlayers
import bereal.app.bts.domain.player.base.SimpleVideoPlayer
import bereal.app.bts.ui.model.VideoAnalyticsParams
import bereal.app.bts.ui.player.analytics.SimplePlayerAnalytics
import bereal.app.bts.ui.player.mute.PlayersMuteSingleState
import bereal.app.common.stateIn
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.remote.logger.RemoteLogger
import bereal.app.video.data.local.BeRealHeadersMedia3CacheFactoryProvider
import bereal.app.video.domain.usecase.GetCacheVideoUseCase
import bereal.app.video.ui.ext.toDomain
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import bereal.app.video.ui.model.BeRealVideoPlayerState
import bereal.app.video.ui.model.SimpleVideoUiModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory
import kotlin.math.max
import kotlin.time.Duration.Companion.milliseconds

/*
    Copy of BTSVideoPlayerImpl.kt (which is single) but here with a @Factory
    removed the parent interface BTSVideoPlayer
    removed - VideoUiModelContainerDataUiModel
 */
@Factory
@Immutable
@UnstableApi
class SimpleSingleVideoPlayer(
    private val appContext: Context,
    private val remoteLogger: RemoteLogger,
    private val simplePlayerAnalytics: SimplePlayerAnalytics,
    private val getCacheBtsVideoUseCase: GetCacheVideoUseCase,
    private val dispatcherProvider: DispatcherProvider,
    private val allSimpleVideoPlayers: AllSimpleVideoPlayers,
    private val playersMuteSingleState: PlayersMuteSingleState,
    private val beRealHeadersMedia3CacheFactoryProvider: BeRealHeadersMedia3CacheFactoryProvider,
) : SimpleVideoPlayer {

    private val scope = CoroutineScope(dispatcherProvider.ui + SupervisorJob())

    private var currentVideoAnalyticsParams: VideoAnalyticsParams? = null
    private var currentData: SimpleVideoUiModel? = null

    private val _playerState =
        MutableStateFlow<BeRealVideoPlayerState>(BeRealVideoPlayerState.Stopped)

    //region primary player
    val playerState: StateFlow<BeRealVideoPlayerState> = _playerState.asStateFlow()
    val playerMuteState: StateFlow<BeRealVideoPlayerMuteState> =
        playersMuteSingleState.playerMuteState

    override val isMuted: Boolean
        get() = playersMuteSingleState.playerMuteState.value == BeRealVideoPlayerMuteState.Muted

    val player: ExoPlayer by lazy {
        buildExoPlayerInstance().also {
            it.addListener(playerEventListener)
        }
    }

    val duration: StateFlow<VideoDurationDomainModel> = playerState.map {
        when (it) {
            BeRealVideoPlayerState.Loading -> {
                VideoDurationDomainModel.Unknown
            }
            BeRealVideoPlayerState.Paused,
            BeRealVideoPlayerState.Playing,
            BeRealVideoPlayerState.Stopped,
            BeRealVideoPlayerState.NearStopped,
            -> {
                player.duration
                    .takeIf { it != C.TIME_UNSET && it != Long.MAX_VALUE }
                    ?.let {
                        VideoDurationDomainModel.Fetched(it.milliseconds)
                    } ?: VideoDurationDomainModel.Unknown
            }
        }
    }.stateIn(scope, VideoDurationDomainModel.Unknown)

    private val playerEventListener: Player.Listener by lazy {
        object : Player.Listener {
            override fun onIsPlayingChanged(isPlaying: Boolean) {
                super.onIsPlayingChanged(isPlaying)
                if (isPlaying) {
                    notifyPlayerState(
                        state = BeRealVideoPlayerState.Playing,
                    )

                    postMessageForNearStoppedState()
                } else {
                    if (player.playbackState == Player.STATE_ENDED) {
                        notifyPlayerState(
                            state = BeRealVideoPlayerState.Stopped,
                        )
                    }
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                notifyPlayerState(
                    state = BeRealVideoPlayerState.Stopped,
                )
                remoteLogger.logUserError(
                    "BeRealVideo",
                    "An error occured while playing video : ${currentData?.uri} Error: $error",
                )
            }

            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_BUFFERING -> {
                        notifyPlayerState(
                            state = BeRealVideoPlayerState.Loading,
                        )
                    }

                    Player.STATE_READY -> {
                        simplePlayerAnalytics.trackPlayBtsVideo(
                            currentVideoAnalyticsParams = currentVideoAnalyticsParams,
                            formatedDuration = getFormatedDuration(player.duration),
                        )
                    }
                }
            }
        }
    }

    init {
        setVolumeDependingOnMute(playersMuteSingleState.playerMuteState.value)
        scope.launch {
            playersMuteSingleState.playerMuteState
                .collectLatest {
                    setVolumeDependingOnMute(it)
                }
        }
    }

    override fun observeVideoDuration(videoUrl: String): Flow<VideoDurationDomainModel> {
        return if (currentData?.uri == videoUrl) {
            duration
        } else {
            flowOf(VideoDurationDomainModel.Unknown)
        }
    }

    private fun setVolume(volume: Float) {
        player.volume = volume
    }

    private fun setVolumeDependingOnMute(muteValue: BeRealVideoPlayerMuteState) {
        when (muteValue) {
            is BeRealVideoPlayerMuteState.Muted -> {
                setVolume(0f)
            }

            is BeRealVideoPlayerMuteState.Unmuted -> {
                setVolume(1f)
            }
        }
    }

    private fun postMessageForNearStoppedState() {
        val duration = player.duration
        val position = duration - NEAR_STOP_LENGTH_IN_MS

        player.createMessage { _: Int, payload: Any? ->
            if (CUSTOM_EVENT_NEAR_STOP_PAYLOAD == payload.toString()) {
                notifyPlayerState(
                    state = BeRealVideoPlayerState.NearStopped,
                )
            }
        }
            .setPosition(0, position)
            .setPayload(CUSTOM_EVENT_NEAR_STOP_PAYLOAD)
            .setDeleteAfterDelivery(false)
            .send()
    }

    suspend fun prepare(data: SimpleVideoUiModel, videoAnalyticsParams: VideoAnalyticsParams?) {
        currentData = data
        currentVideoAnalyticsParams = videoAnalyticsParams

        allSimpleVideoPlayers.registerPlayer(data.uri, this)

        val measuredMediaSource = getDataSource(data)
        player.setMediaSource(measuredMediaSource.mediaSource)
        player.prepare()
        seekToBtsStartPosition(data.maxBtsLength, measuredMediaSource.duration)
    }

    fun play() {
        player.seekTo(0)
        player.playWhenReady = true
    }

    fun resume() {
        player.playWhenReady = true
    }

    private fun seekToBtsStartPosition(maxBtsLength: Long, videoDuration: Long) {
        val seekTo = if (maxBtsLength > 0) {
            max(0, videoDuration - maxBtsLength)
        } else {
            0
        }
        player.seekTo(seekTo)
    }

    fun pause() {
        notifyPlayerState(state = BeRealVideoPlayerState.Paused)
        player.pause()
    }

    fun stop() {
        notifyPlayerState(state = BeRealVideoPlayerState.Stopped)
        player.stop()
    }

    fun release() {
        player.release()

        scope.cancel()

        currentData?.uri?.let {
            allSimpleVideoPlayers.unregisterPlayer(it)
        }
    }

    fun toggleMute() {
        val shouldMute = player.volume == 1f

        playersMuteSingleState.toggleMute(shouldMute)

        simplePlayerAnalytics.trackMuteToggle(
            isSoundOn = !shouldMute,
            currentVideoAnalyticsParams = currentVideoAnalyticsParams,
            formatedDuration = getFormatedDuration(player.duration),
        )
    }

    private fun notifyPlayerState(state: BeRealVideoPlayerState) {
        _playerState.update {
            state
        }
    }

    private fun getFormatedDuration(durationMs: Long): Float =
        formatVideoDuration(durationMs)
    //endregion

    //region exo player
    private fun buildExoPlayerInstance(): ExoPlayer {
        return ExoPlayer.Builder(appContext).build()
        // TODO Check LATER if it improves video consumption
        // val renderersFactory = DefaultRenderersFactory(context)
        //     .forceEnableMediaCodecAsynchronousQueueing()
        // val exoPlayer = ExoPlayer.Builder(context, renderersFactory).build()
    }

    private suspend fun getDataSource(contentData: SimpleVideoUiModel): MeasuredMediaSource {
        val uri = Uri.parse(contentData.uri)
        val videoDurationMs = if (uri.isRemote()) {
            0L
        } else {
            val player: MediaPlayer? = MediaPlayer.create(appContext, uri)
            player?.duration?.toLong()?.also {
                player.release()
            } ?: 0L
        }

        val sourceTypeDomainModel = contentData.sourceType.toDomain()
        val cacheInstance = getCacheBtsVideoUseCase(sourceTypeDomainModel)
        val upstreamFactory = DefaultDataSource.Factory(
            appContext,
            beRealHeadersMedia3CacheFactoryProvider.provideCacheDataSourceFactory(),
        )

        val cacheFactory = CacheDataSource.Factory().apply {
            setCache(cacheInstance.realCache)
            // upstreamFactory is needed to either download video that has not been cached (should not happen)
            // and get video that has been transformed locally (rotation issue)
            setUpstreamDataSourceFactory(upstreamFactory)
            setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)

            /*
             * https://developer.android.com/media/media3/exoplayer/downloading-media
             * If the same player instance will also be used to play non-downloaded content
             * then the CacheDataSource.Factory should be configured as read-only to avoid downloading that content as well during playback.
             */
            setCacheWriteDataSinkFactory(null) // Disable writing
        }

        val mediaSourceFactory = ProgressiveMediaSource.Factory(
            cacheFactory,
        )

        val mediaItem = MediaItem.fromUri(uri)

        val mediaSource = mediaSourceFactory.createMediaSource(mediaItem)
        return MeasuredMediaSource(
            mediaSource = mediaSource,
            duration = videoDurationMs,
        )
    }
    //endregion

    private data class MeasuredMediaSource(
        val mediaSource: MediaSource,
        val duration: Long,
    )

    private fun Uri.isRemote(): Boolean {
        return scheme?.startsWith("http") == true
    }

    companion object {
        private const val CUSTOM_EVENT_NEAR_STOP_PAYLOAD = "near_stop_event"
        private const val NEAR_STOP_LENGTH_IN_MS = 200
    }
}
