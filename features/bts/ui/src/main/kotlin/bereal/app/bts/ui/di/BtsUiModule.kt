package bereal.app.bts.ui.di

import android.content.Context
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import org.koin.ksp.generated.module

val btsUiModule = BtsUiModule().module

@Module
@ComponentScan("bereal.app.bts.ui")
internal class BtsUiModule {
    @OptIn(UnstableApi::class)
    @Single
    fun provideStandaloneDatabaseProvider(appContext: Context): StandaloneDatabaseProvider {
        return StandaloneDatabaseProvider(appContext)
    }
}
