package bereal.app.bts.ui.player.mute

import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.video.ui.model.BeRealVideoPlayerMuteState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

/**
 * Single instance that retains & share the mute state of all our players
 */
@Single
class PlayersMuteSingleState(
    private val deviceVolumeListener: DeviceVolumeManager,
    private val applicationScope: CoroutineScope,
    private val dispatcherProvider: DispatcherProvider,
) {

    private val _playerMuteState =
        MutableStateFlow<BeRealVideoPlayerMuteState>(
            // init depending on the device volume
            computeInitialMute(deviceVolumeListener.systemVolume.value),
        )

    init {
        // if the system volumes goes to 0 : we mute the video
        applicationScope.launch(dispatcherProvider.data) {
            deviceVolumeListener.systemVolume.collect {
                if (it == 0) {
                    _playerMuteState.value = BeRealVideoPlayerMuteState.Muted
                }
            }
        }

        // if we increment the volume : it un-mutes the video
        applicationScope.launch(dispatcherProvider.data) {
            deviceVolumeListener.isIncrementingVolume.collect {
                if (it) {
                    _playerMuteState.value = BeRealVideoPlayerMuteState.Unmuted
                }
            }
        }
    }

    val playerMuteState = _playerMuteState

    // user can mute the video
    fun toggleMute(shouldMute: Boolean) {
        toggleMute(
            if (shouldMute) {
                BeRealVideoPlayerMuteState.Muted
            } else {
                BeRealVideoPlayerMuteState.Unmuted
            },
        )
    }

    fun toggleMute(newState: BeRealVideoPlayerMuteState) {
        _playerMuteState.update {
            newState
        }
    }
}

private fun computeInitialMute(systemVolume: Int): BeRealVideoPlayerMuteState {
    return if (systemVolume == 0) {
        BeRealVideoPlayerMuteState.Muted
    } else {
        BeRealVideoPlayerMuteState.Unmuted
    }
}
