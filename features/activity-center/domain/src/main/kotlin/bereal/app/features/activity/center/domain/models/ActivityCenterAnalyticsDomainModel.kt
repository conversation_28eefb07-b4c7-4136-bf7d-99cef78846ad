package bereal.app.features.activity.center.domain.models

import bereal.app.analytics.model.AnalyticsEvent
import bereal.app.analytics.model.AnalyticsParam

sealed interface ActivityCenterAnalyticsDomainModel : AnalyticsEvent {

    data class ViewedActivityCenter(
        val hadRedDot: Boolean,
    ) : ActivityCenterAnalyticsDomainModel {
        override val name = "viewedActivityCenter"
        override val params = listOf(
            AnalyticsParam(name = "hadRedDot", value = hadRedDot),
        )
    }

    data class OpenedActivityCenter(
        val activityType: ActivityCenterItemType,
        val sectionType: ActivityCenterSectionType,
        val isUnavailable: Boolean,
    ) : ActivityCenterAnalyticsDomainModel {
        override val name = "activityOpened"
        override val params = listOf(
            AnalyticsParam(name = "activityType", value = activityType.toAnalyticsValue()),
            AnalyticsParam(name = "activitySection", value = sectionType.toAnalyticsValue()),
            AnalyticsParam(name = "isUnavailable", value = isUnavailable),
        )
    }
}

private fun ActivityCenterItemType.toAnalyticsValue(): String {
    return when (this) {
        ActivityCenterItemType.Post.Comment -> "ownPostComment"
        ActivityCenterItemType.Post.RealMoji -> "ownPostRealmoji"
        ActivityCenterItemType.Post.CommentFollowUp -> "followUpComment"
        ActivityCenterItemType.Post.FirstPostInAWhile -> "firstTimePostInAWhile"
        ActivityCenterItemType.Post.Tag -> "postTag"
        ActivityCenterItemType.Post.Repost -> "postRepost"
        ActivityCenterItemType.Post.CommentMention -> "postCommentMention"
        ActivityCenterItemType.Post.CaptionMention -> "postMention"
        ActivityCenterItemType.Post.FriendCommentedOtherFriendPost -> "friendPostPopular"
        ActivityCenterItemType.Post.FriendPostGettingPopular -> "friendPostComment"
        ActivityCenterItemType.Post.MemoryFound -> "memoryWithFriend"
        ActivityCenterItemType.User.FriendFollow -> "userFriendFollow"
        ActivityCenterItemType.User.ContactJoined -> "userContactJoined"
        ActivityCenterItemType.User.FriendRequestAccepted -> "userFriendRequestAccepted"
    }
}

private fun ActivityCenterSectionType.toAnalyticsValue(): String {
    return when (this) {
        ActivityCenterSectionType.New -> "new"
        ActivityCenterSectionType.Last7Days -> "last7days"
        ActivityCenterSectionType.Last30Days -> "last30days"
    }
}
