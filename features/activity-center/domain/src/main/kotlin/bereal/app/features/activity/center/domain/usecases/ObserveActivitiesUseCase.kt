package bereal.app.features.activity.center.domain.usecases

import bereal.app.entities.toUserId
import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.features.activity.center.domain.models.CoreActivityCenterItem
import bereal.app.user.usecases.ObserveMyUserUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
class ObserveActivitiesUseCase(
    private val activityCenterRepository: ActivityCenterRepository,
    private val observeMyUserUseCase: ObserveMyUserUseCase,
) {
    operator fun invoke(): Flow<List<CoreActivityCenterItem>> {
        return observeMyUserUseCase().map { it.uid }
            .distinctUntilChanged()
            .flatMapLatest {
                activityCenterRepository.observeActivities(it.toUserId())
            }
    }
}
