package bereal.app.features.activity.center.domain

import bereal.app.common.Either
import bereal.app.entities.UserId
import bereal.app.entities.error.GenericError
import bereal.app.features.activity.center.domain.models.CoreActivityCenterItem
import kotlinx.coroutines.flow.Flow

interface ActivityCenterRepository {
    suspend fun getActivityCenter(withHydration: <PERSON><PERSON><PERSON>, accountOwnerUid: UserId): Either<GenericError, Long>
    fun observeUnseenActivitiesCount(accountOwnerUid: UserId): Flow<Int>
    fun observeActivities(accountOwnerUid: UserId): Flow<List<CoreActivityCenterItem>>
    suspend fun markAllActivitiesAsSeen(accountOwnerUid: UserId): Either<GenericError, Unit>
    suspend fun markActivityAsSeenForEntity(accountOwnerUid: UserId, mainElementId: String): Either<GenericError, Unit>
    suspend fun markUserActivitiesAsSeen(accountOwnerUid: UserId, subElementUserId: String): Either<GenericError, Unit>
}
