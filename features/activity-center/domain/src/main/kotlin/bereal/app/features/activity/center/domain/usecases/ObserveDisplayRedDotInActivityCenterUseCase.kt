package bereal.app.features.activity.center.domain.usecases

import bereal.app.common.combines
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.mapLatest
import org.koin.core.annotation.Factory

@Factory
class ObserveDisplayRedDotInActivityCenterUseCase(
    val observeHasUnSeenActivitiesUseCase: ObserveHasUnSeenActivitiesUseCase,
    val observeForceDisplayRedDotForFriendRequestInActivityCenterUseCase: ObserveForceDisplayRedDotForFriendRequestInActivityCenterUseCase,
) {
    operator fun invoke(): Flow<Boolean> = combines(
        observeHasUnSeenActivitiesUseCase(),
        observeForceDisplayRedDotForFriendRequestInActivityCenterUseCase(),
    ).mapLatest { (hasUnSeenActivities, forceDisplayRedDot) ->
        hasUnSeenActivities || forceDisplayRedDot
    }
}
