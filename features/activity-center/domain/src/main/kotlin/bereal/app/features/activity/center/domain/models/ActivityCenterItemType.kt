package bereal.app.features.activity.center.domain.models

sealed interface ActivityCenterItemType {
    // to save string in local storage (for backward compatibility)
    val typeName: TypeName

    sealed class Post(override val typeName: TypeName) : ActivityCenterItemType {
        data object Comment : Post(TypeName.PostComment)
        data object RealMoji : Post(TypeName.PostRealMoji)
        data object Tag : Post(TypeName.PostTag)
        data object Repost : Post(TypeName.PostRepost)
        data object CommentMention : Post(TypeName.PostCommentMention)
        data object CaptionMention : Post(TypeName.PostCaptionMention)
        data object CommentFollowUp : Post(TypeName.PostCommentFollowUp)
        data object FirstPostInAWhile : Post(TypeName.FirstPostInAWhile)
        data object FriendCommentedOtherFriendPost : Post(TypeName.FriendCommentedOtherFriendPost)
        data object FriendPostGettingPopular : Post(TypeName.FriendPostGettingPopular)
        data object MemoryFound : Post(TypeName.MemoryFound)
    }

    sealed class User(override val typeName: TypeName) : ActivityCenterItemType {
        data object FriendFollow : User(TypeName.UserFriendFollow)
        data object ContactJoined : User(TypeName.UserContactJoined)
        data object FriendRequestAccepted : User(TypeName.UserFriendRequestAccepted)
    }

    enum class TypeName {
        PostComment,
        PostRealMoji,
        PostTag,
        PostRepost,
        PostCommentMention,
        PostCaptionMention,
        PostCommentFollowUp,
        FirstPostInAWhile,
        FriendCommentedOtherFriendPost,
        FriendPostGettingPopular,
        MemoryFound,
        UserFriendFollow,
        UserContactJoined,
        UserFriendRequestAccepted, ;

        companion object {
            fun fromName(name: String): ActivityCenterItemType? {
                return try {
                    val typeName = TypeName.valueOf(name)
                    return when (typeName) {
                        PostComment -> Post.Comment
                        PostRealMoji -> Post.RealMoji
                        PostTag -> Post.Tag
                        PostRepost -> Post.Repost
                        PostCommentMention -> Post.CommentMention
                        PostCaptionMention -> Post.CaptionMention
                        PostCommentFollowUp -> Post.CommentFollowUp
                        FirstPostInAWhile -> Post.FirstPostInAWhile
                        FriendCommentedOtherFriendPost -> Post.FriendCommentedOtherFriendPost
                        FriendPostGettingPopular -> Post.FriendPostGettingPopular
                        MemoryFound -> Post.MemoryFound
                        UserFriendFollow -> User.FriendFollow
                        UserContactJoined -> User.ContactJoined
                        UserFriendRequestAccepted -> User.FriendRequestAccepted
                    }
                } catch (e: IllegalArgumentException) {
                    null
                }
            }
        }
    }
}
