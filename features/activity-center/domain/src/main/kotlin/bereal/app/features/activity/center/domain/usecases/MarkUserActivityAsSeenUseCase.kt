package bereal.app.features.activity.center.domain.usecases

import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.user.usecases.GetMyUserIdUseCase
import org.koin.core.annotation.Factory

/**
 * To mark user activity items as seen (Accepted Friend request, Contact joined, ..)
 */
@Factory
class MarkUserActivityAsSeenUseCase(
    private val activityCenterRepository: ActivityCenterRepository,
    private val getMyUserIdUseCase: GetMyUserIdUseCase,
) {
    suspend operator fun invoke(userId: String) {
        val myUserUid = getMyUserIdUseCase()
        activityCenterRepository.markUserActivitiesAsSeen(
            accountOwnerUid = myUserUid,
            subElementUserId = userId,
        )
    }
}
