package bereal.app.features.activity.center.domain.usecases

import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.user.usecases.GetMyUserIdUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory

@Factory
class MarkAllActivitiesAsSeenUseCase(
    private val activityCenterRepository: ActivityCenterRepository,
    private val scope: CoroutineScope,
    private val dispatcherProvider: DispatcherProvider,
    private val getMyUserIdUseCase: GetMyUserIdUseCase,
) {

    operator fun invoke() {
        // here we use the application scope to make sure it is not cancelled when the viewmodel is cleared
        scope.launch(dispatcherProvider.domain) {
            val myUserUid = getMyUserIdUseCase()
            activityCenterRepository.markAllActivitiesAsSeen(myUserUid)
        }
    }
}
