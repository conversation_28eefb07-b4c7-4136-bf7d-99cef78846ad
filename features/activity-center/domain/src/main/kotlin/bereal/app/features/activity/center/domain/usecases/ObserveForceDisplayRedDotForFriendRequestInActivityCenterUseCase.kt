package bereal.app.features.activity.center.domain.usecases

import bereal.app.common.combines
import bereal.app.features.relationship.domain.usecases.ObserveReceivedPendingFriendRequestsUseCase
import bereal.app.settings.usecases.ObserveRepetitiveHighlightInActivityCenterLastSeenUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.mapLatest
import kotlinx.datetime.Clock
import org.koin.core.annotation.Factory
import kotlin.time.Duration.Companion.days

@Factory
class ObserveForceDisplayRedDotForFriendRequestInActivityCenterUseCase(
    val observeReceivedPendingFriendRequestsUseCase: ObserveReceivedPendingFriendRequestsUseCase,
    val observeRepetitiveHighlightInActivityCenterLastSeenUseCase: ObserveRepetitiveHighlightInActivityCenterLastSeenUseCase,
) {
    operator fun invoke(): Flow<Boolean> =
        combines(
            observeReceivedPendingFriendRequestsUseCase()
                .mapLatest { requests ->
                    requests.isNotEmpty()
                },
            observeRepetitiveHighlightInActivityCenterLastSeenUseCase(),
        )
            .mapLatest { (hasPendingRequest, lastSeen) ->
                if (!hasPendingRequest) return@mapLatest false
                lastSeen?.let {
                    val nowMs = Clock.System.now().toEpochMilliseconds()
                    val intervalRepMs = lastSeen.toEpochMilliseconds() + REPETITIVE_HIGHLIGHT_IN_ACTIVITY_CENTER_DURATION.inWholeMilliseconds
                    intervalRepMs < nowMs
                } ?: true
            }.distinctUntilChanged()

    companion object {
        val REPETITIVE_HIGHLIGHT_IN_ACTIVITY_CENTER_DURATION = 7.days
    }
}
