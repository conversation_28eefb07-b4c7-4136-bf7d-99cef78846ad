package bereal.app.features.activity.center.domain.usecases

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
class ObserveHasUnSeenActivitiesUseCase(
    private val observeUnSeenActivitiesCountUseCase: ObserveUnSeenActivitiesCountUseCase,
) {
    operator fun invoke(): Flow<Boolean> {
        return observeUnSeenActivitiesCountUseCase().map { it > 0 }.distinctUntilChanged()
    }
}
