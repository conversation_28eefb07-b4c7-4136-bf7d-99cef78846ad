package bereal.app.features.activity.center.domain.usecases

import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.user.usecases.GetMyUserIdUseCase
import org.koin.core.annotation.Factory

@Factory
class MarkActivityAsSeenForEntityUseCase(
    private val activityCenterRepository: ActivityCenterRepository,
    private val getMyUserIdUseCase: GetMyUserIdUseCase,
) {

    suspend operator fun invoke(mainElementId: String) {
        val myUserUid = getMyUserIdUseCase()
        activityCenterRepository.markActivityAsSeenForEntity(accountOwnerUid = myUserUid, mainElementId = mainElementId)
    }
}
