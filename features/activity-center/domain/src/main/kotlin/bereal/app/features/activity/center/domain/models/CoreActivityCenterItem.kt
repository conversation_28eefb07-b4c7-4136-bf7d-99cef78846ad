package bereal.app.features.activity.center.domain.models

import bereal.app.entities.BasicUser
import bereal.app.entities.BeRealMedia
import bereal.app.entities.PostId

data class CoreActivityCenterItem(
    val activityId: String,
    val seen: Boolean,
    val extraCounterInfo: Int,
    val lastUpdatedAt: Long,
    val state: ActivityCenterItemState,
)

sealed interface ActivityCenterItemState {
    val type: ActivityCenterItemType

    data class Post(
        val postId: PostId,
        val postOwner: BasicUser,
        val thumbnail: BeRealMedia?,
        val reactions: List<CoreActivityCenterElement.Reaction>,
        val postFound: Boolean,
        override val type: ActivityCenterItemType.Post,
    ) : ActivityCenterItemState

    data class User(
        val owner: BasicUser,
        val otherUsers: List<BasicUser>,
        override val type: ActivityCenterItemType.User,
    ) : ActivityCenterItemState
}
