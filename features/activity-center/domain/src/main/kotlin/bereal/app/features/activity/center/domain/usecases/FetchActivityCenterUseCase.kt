package bereal.app.features.activity.center.domain.usecases

import bereal.app.common.Either
import bereal.app.entities.error.GenericError
import bereal.app.entities.toUserId
import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.user.usecases.GetMyUserUseCase
import org.koin.core.annotation.Factory

@Factory
class FetchActivityCenterUseCase(
    private val activityCenterRepository: ActivityCenterRepository,
    private val getMyUserUseCase: GetMyUserUseCase,
) {
    suspend operator fun invoke(withHydration: Boolean): Either<GenericError, Long> {
        val myUserId = getMyUserUseCase().uid.toUserId()

        return activityCenterRepository.getActivityCenter(
            withHydration = withHydration,
            accountOwnerUid = myUserId,
        )
    }

    companion object {
        const val INITIAL_COOLDOWN_MS = 10_000L
    }
}
