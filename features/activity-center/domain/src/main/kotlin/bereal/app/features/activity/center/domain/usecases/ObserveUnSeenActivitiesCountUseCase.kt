package bereal.app.features.activity.center.domain.usecases

import bereal.app.common.combines
import bereal.app.entities.toUserId
import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.features.relationship.domain.usecases.ObserveUnseenPendingFriendRequestsCount
import bereal.app.user.usecases.ObserveMyUserUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import org.koin.core.annotation.Factory

@Factory
class ObserveUnSeenActivitiesCountUseCase(
    private val activityCenterRepository: ActivityCenterRepository,
    private val observeMyUserUseCase: ObserveMyUserUseCase,
    private val observeUnseenPendingFriendRequestsCount: ObserveUnseenPendingFriendRequestsCount,
) {
    operator fun invoke(): Flow<Int> {
        return observeMyUserUseCase().map { it.uid }.distinctUntilChanged()
            .flatMapLatest {
                combines(
                    activityCenterRepository.observeUnseenActivitiesCount(it.toUserId()),
                    observeUnseenPendingFriendRequestsCount(),
                ).map { (unseenActivitiesCount, unseenPendingFriendRequestsCount) ->
                    unseenActivitiesCount + unseenPendingFriendRequestsCount
                }
            }
            .distinctUntilChanged()
    }
}
