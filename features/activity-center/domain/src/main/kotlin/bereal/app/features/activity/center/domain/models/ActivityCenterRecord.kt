package bereal.app.features.activity.center.domain.models

data class ActivityCenterRecord(
    val activityId: String,
    val seen: Boolean,
    val activityType: ActivityCenterItemType,
    val counterInfo: Int,
    val mainElement: Element,
    val subElements: List<Element>,
    val lastUpdatedAt: Long,
) {
    data class Element(
        val id: String,
        val ownerId: String,
        val type: ElementType,
    )

    enum class ElementType {
        Post,
        User,
        Comment,
        RealMoji,
        Tag,
    }
}
