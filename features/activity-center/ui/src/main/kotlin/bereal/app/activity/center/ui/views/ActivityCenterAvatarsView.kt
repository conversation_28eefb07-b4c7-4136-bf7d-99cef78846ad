package bereal.app.activity.center.ui.views

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.center
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.CompositingStrategy
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.activity.center.ui.models.ActivityCenterAvatarsUiModel
import bereal.app.activity.center.ui.models.previewUserAvatar
import bereal.app.design.Avatar
import bereal.app.design.AvatarSize
import bereal.app.design.debounceClickable
import bereal.app.design.model.BasicUserUi
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders

@Composable
fun ActivityCenterAvatarsView(
    modifier: Modifier = Modifier,
    avatars: ActivityCenterAvatarsUiModel,
    onProfileClicked: (BasicUserUi) -> Unit,
) {
    val imageLoader = LocalBeRealImageLoaders.current.profilePicture

    val avatarSize by remember(avatars) {
        mutableIntStateOf(if (avatars.otherUserAvatar != null) 40 else 60)
    }

    Box(
        modifier = modifier.size(60.dp)
            .graphicsLayer {
                compositingStrategy = CompositingStrategy.Offscreen
            },
        contentAlignment = Alignment.BottomStart,
    ) {
        ActivityCenterAvatar(
            modifier = Modifier.size(avatarSize.dp).debounceClickable {
                onProfileClicked(avatars.mainAvatar.user)
            },
        ) {
            Avatar(
                avatarInfo = avatars.mainAvatar.avatarInfo,
                size = if (avatars.otherUserAvatar != null) AvatarSize.Small else AvatarSize.Medium,
                imageLoader = imageLoader,
            )
        }

        avatars.otherUserAvatar?.let {
            ActivityCenterAvatar(
                modifier = Modifier
                    .offset(x = (avatarSize / 2).dp, y = (-avatarSize / 2).dp)
                    .size(avatarSize.dp)
                    .debounceClickable {
                        onProfileClicked(it.user)
                    },
            ) {
                Avatar(
                    avatarInfo = it.avatarInfo,
                    size = AvatarSize.Small,
                    imageLoader = imageLoader,
                )
            }
        }
    }
}

@Composable
private fun ActivityCenterAvatar(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
) {
    val stroke = remember {
        Stroke(width = 18f)
    }

    Box(
        modifier = modifier
            .drawWithContent {
                drawContent()

                drawCircle(
                    color = Color.White.copy(alpha = 0.2f),
                    radius = (size.minDimension - 12f) / 2f,
                    center = size.center,
                    style = stroke,
                )

                drawCircle(
                    color = Color.Black,
                    radius = size.minDimension / 2f,
                    center = size.center,
                    style = stroke,
                    blendMode = BlendMode.Clear,
                )
            }
            .clip(shape = CircleShape),
    ) {
        content()
    }
}

@Preview
@Composable
private fun ActivityCenterAvatars_bothAvatars_preview() {
    BeRealTheme {
        Box {
            ActivityCenterAvatarsView(
                avatars = ActivityCenterAvatarsUiModel(
                    mainAvatar = previewUserAvatar(),
                    otherUserAvatar = previewUserAvatar(),
                ),
                onProfileClicked = {},
            )
        }
    }
}

@Preview
@Composable
private fun ActivityCenterAvatars_singleAvatar_preview() {
    BeRealTheme {
        Box {
            ActivityCenterAvatarsView(
                avatars = ActivityCenterAvatarsUiModel(
                    mainAvatar = previewUserAvatar(),
                    otherUserAvatar = null,
                ),
                onProfileClicked = {},
            )
        }
    }
}
