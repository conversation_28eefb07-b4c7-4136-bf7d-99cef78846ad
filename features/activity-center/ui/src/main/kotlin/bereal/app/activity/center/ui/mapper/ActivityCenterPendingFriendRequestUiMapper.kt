package bereal.app.activity.center.ui.mapper

import androidx.compose.ui.text.AnnotatedString
import bereal.app.activity.center.ui.models.ActivityCenterPendingFriendRequestState
import bereal.app.commonandroid.BeRealListFormatter
import bereal.app.commonandroid.StringProvider
import bereal.app.entities.BasicUser
import bereal.app.time.formatter.BeRealTimeFormatter
import kotlinx.collections.immutable.persistentListOf

internal fun mapToPendingFriendRequestsState(
    pendingFriendRequests: List<BasicUser>, // in the title we show the list of usernames (regarding or not they are already seen)
    unseenFriendRequestsCount: Int, // in the badge we show only the total number of unseen friend requests or just a red dot if we force it (pending request experiment)
    forceDisplayRedDotForFriendRequestInActivityCenter: Boolean,
    stringProvider: StringProvider,
    listFormatter: BeRealListFormatter,
    timeFormatter: BeRealTimeFormatter,
): ActivityCenterPendingFriendRequestState {
    return if (pendingFriendRequests.isEmpty()) {
        ActivityCenterPendingFriendRequestState(
            title = stringProvider[bereal.app.translations.R.string.relationship_friendRequests],
            subtitle = AnnotatedString(stringProvider[bereal.app.translations.R.string.activity_center_friend_request_received_empty]),
            clickableUserRanges = persistentListOf(),
            badge = ActivityCenterPendingFriendRequestState.Badge.Hidden,
        )
    } else {
        // the names we want to display in the subtitle
        val firstUser = pendingFriendRequests.first()
        val secondUser = if (pendingFriendRequests.size <= 3) pendingFriendRequests.getOrNull(1) else null
        val thirdUser = if (pendingFriendRequests.size <= 3) pendingFriendRequests.getOrNull(2) else null

        val numberOfUserDisplayed = listOfNotNull(firstUser, secondUser, thirdUser).size

        val (subtitle, clickableRanges) = buildTitle(
            titleFormat = TitleFormat.Default(bereal.app.translations.R.plurals.activity_center_friend_request_received),
            firstUser = firstUser,
            secondUser = secondUser,
            thirdUser = thirdUser,
            counter = pendingFriendRequests.size - numberOfUserDisplayed,
            lastUpdatedAt = null,
            listFormatter = listFormatter,
            timeFormatter = timeFormatter,
            stringProvider = stringProvider,
        )

        ActivityCenterPendingFriendRequestState(
            title = stringProvider[bereal.app.translations.R.string.relationship_friendRequests],
            subtitle = subtitle,
            clickableUserRanges = clickableRanges,
            badge = if (unseenFriendRequestsCount > 0) {
                ActivityCenterPendingFriendRequestState.Badge.Visible.WithNumber(
                    numberOfPendingFriendRequest = unseenFriendRequestsCount,
                )
            } else if (forceDisplayRedDotForFriendRequestInActivityCenter) {
                ActivityCenterPendingFriendRequestState.Badge.Visible.NoNumber
            } else {
                ActivityCenterPendingFriendRequestState.Badge.Hidden
            },
        )
    }
}
