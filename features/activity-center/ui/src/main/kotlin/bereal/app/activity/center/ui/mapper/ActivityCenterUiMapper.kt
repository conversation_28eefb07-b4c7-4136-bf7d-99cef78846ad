package bereal.app.activity.center.ui.mapper

import androidx.annotation.PluralsRes
import androidx.annotation.StringRes
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import bereal.app.activity.center.ui.models.ActivityCenterAvatarsUiModel
import bereal.app.activity.center.ui.models.ActivityCenterClickableUserRange
import bereal.app.activity.center.ui.models.ActivityCenterItemUiModel
import bereal.app.activity.center.ui.models.ActivityCenterSectionUiModel
import bereal.app.activity.center.ui.models.ActivityCenterUiState
import bereal.app.common.NO_BREAK_SPACE
import bereal.app.commonandroid.BeRealListFormatter
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.boldInText
import bereal.app.design.avatar.avatar
import bereal.app.design.model.mapToUi
import bereal.app.design.theme.BeRealColors
import bereal.app.design.theme.BeRealTheme
import bereal.app.entities.BasicUser
import bereal.app.features.activity.center.domain.models.ActivityCenterItemState
import bereal.app.features.activity.center.domain.models.ActivityCenterItemType
import bereal.app.features.activity.center.domain.models.ActivityCenterSectionType
import bereal.app.features.activity.center.domain.models.CoreActivityCenterItem
import bereal.app.time.formatter.BeRealTimeFormatter
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.translations.R
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.toPersistentList
import java.util.concurrent.TimeUnit

fun List<CoreActivityCenterItem>.mapToState(
    pendingFriendRequests: List<BasicUser>,
    unseenFriendRequestsCount: Int,
    forceDisplayRedDotForFriendRequestInActivityCenter: Boolean,
    isLoading: Boolean,
    hasError: Boolean,
    timeProvider: BeRealTimeProvider,
    listFormatter: BeRealListFormatter,
    timeFormatter: BeRealTimeFormatter,
    stringProvider: StringProvider,
): ActivityCenterUiState {
    return when {
        isLoading && isEmpty() -> ActivityCenterUiState.Loading
        hasError && isEmpty() -> ActivityCenterUiState.Error
        isEmpty() -> ActivityCenterUiState.Empty(
            mapToPendingFriendRequestsState(
                pendingFriendRequests = pendingFriendRequests,
                unseenFriendRequestsCount = unseenFriendRequestsCount,
                forceDisplayRedDotForFriendRequestInActivityCenter = forceDisplayRedDotForFriendRequestInActivityCenter,
                stringProvider = stringProvider,
                listFormatter = listFormatter,
                timeFormatter = timeFormatter,
            ),
        )

        else -> ActivityCenterUiState.Success(
            pendingFriendRequest = mapToPendingFriendRequestsState(
                pendingFriendRequests = pendingFriendRequests,
                unseenFriendRequestsCount = unseenFriendRequestsCount,
                forceDisplayRedDotForFriendRequestInActivityCenter = forceDisplayRedDotForFriendRequestInActivityCenter,
                stringProvider = stringProvider,
                listFormatter = listFormatter,
                timeFormatter = timeFormatter,
            ),
            sections = mapToSections(
                now = timeProvider.currentTimeMs,
                listFormatter = listFormatter,
                timeFormatter = timeFormatter,
                stringProvider = stringProvider,
            ),
        )
    }
}

private fun List<CoreActivityCenterItem>.mapToSections(
    now: Long,
    listFormatter: BeRealListFormatter,
    timeFormatter: BeRealTimeFormatter,
    stringProvider: StringProvider,
): PersistentList<ActivityCenterSectionUiModel> {
    val sevenDaysMs = TimeUnit.DAYS.toMillis(7)
    val thirtyDaysMs = TimeUnit.DAYS.toMillis(30)

    return this.groupBy {
        val ageMs = now - it.lastUpdatedAt
        when {
            !it.seen -> ActivityCenterSectionType.New
            ageMs <= sevenDaysMs -> ActivityCenterSectionType.Last7Days
            ageMs <= thirtyDaysMs -> ActivityCenterSectionType.Last30Days
            else -> null
        }
    }
        .filterKeys { it != null }
        .mapNotNull { (sectionType, items) ->
            val title = when (sectionType) {
                ActivityCenterSectionType.New -> stringProvider[R.string.activity_center_new_section_title]
                ActivityCenterSectionType.Last7Days -> stringProvider.get(R.string.activity_center_days_section_title, 7)
                ActivityCenterSectionType.Last30Days -> stringProvider.get(R.string.activity_center_days_section_title, 30)
                else -> null
            }

            if (sectionType == null) return@mapNotNull null

            ActivityCenterSectionUiModel(
                sectionId = sectionType.name,
                title = title.orEmpty(),
                items = items.mapNotNull {
                    it.mapToUiModel(
                        listFormatter = listFormatter,
                        timeFormatter = timeFormatter,
                        stringProvider = stringProvider,
                    )
                }.toPersistentList(),
            )
        }
        .sortedBy {
            when (it.sectionId) {
                ActivityCenterSectionType.New.name -> 0
                ActivityCenterSectionType.Last7Days.name -> 1
                ActivityCenterSectionType.Last30Days.name -> 2
                else -> 3
            }
        }.toPersistentList()
}

private fun CoreActivityCenterItem.mapToUiModel(
    listFormatter: BeRealListFormatter,
    timeFormatter: BeRealTimeFormatter,
    stringProvider: StringProvider,
): ActivityCenterItemUiModel? {
    return when (val state = this.state) {
        is ActivityCenterItemState.Post -> {
            val users = when (state.type) {
                ActivityCenterItemType.Post.FirstPostInAWhile,
                ActivityCenterItemType.Post.FriendPostGettingPopular,
                ActivityCenterItemType.Post.MemoryFound,
                -> listOf(state.postOwner)

                else -> state.reactions.map { it.owner }
            }

            val firstUser = users.getOrNull(0) ?: return null
            val secondUser = users.getOrNull(1)
            val thirdUser = users.getOrNull(2)

            val countUsersToDisplay = listOfNotNull(firstUser, secondUser, thirdUser).size
            val countUsersToDisplayWithCounter = listOfNotNull(firstUser, secondUser).size

            // we want to display MAXIMUM 3 fullNames in the title if not counter
            // we want to display 2 fullNames in the title if there is a counter
            val extraCounter = when {
                extraCounterInfo == 0 -> users.size - countUsersToDisplay
                users.size > countUsersToDisplayWithCounter -> extraCounterInfo + users.size - countUsersToDisplayWithCounter
                else -> extraCounterInfo
            }

            val titleFormat = when (state.type) {
                ActivityCenterItemType.Post.Comment -> TitleFormat.Default(textId = R.plurals.activity_center_post_comment)
                ActivityCenterItemType.Post.RealMoji -> TitleFormat.Default(textId = R.plurals.activity_center_post_realmoji)
                ActivityCenterItemType.Post.CommentFollowUp -> {
                    // to handle the case "Florian also commented on Florian's BeReal"
                    if (users.size == 1 && extraCounter == 0 && firstUser.uid == state.postOwner.uid) {
                        TitleFormat.SameOwner(textId = R.string.activity_center_post_comment_follow_up_own_post)
                    } else {
                        TitleFormat.WithPostOwnerPlural(
                            textId = R.plurals.activity_center_post_comment_follow_up,
                            owner = state.postOwner,
                        )
                    }
                }

                ActivityCenterItemType.Post.FirstPostInAWhile -> {
                    TitleFormat.SameOwner(textId = R.string.first_time_in_while_react_text)
                }

                ActivityCenterItemType.Post.Tag -> TitleFormat.SameOwner(textId = R.string.activity_center_post_tag)
                ActivityCenterItemType.Post.Repost -> TitleFormat.SameOwner(textId = R.string.activity_center_post_repost)
                ActivityCenterItemType.Post.CommentMention -> TitleFormat.Default(textId = R.plurals.activity_center_post_comment_mention)
                ActivityCenterItemType.Post.CaptionMention -> TitleFormat.SameOwner(textId = R.string.activity_center_post_mention)
                ActivityCenterItemType.Post.FriendCommentedOtherFriendPost -> TitleFormat.WithPostOwner(
                    textId = R.string.activity_center_post_comment_other_friend,
                    owner = state.postOwner,
                )
                ActivityCenterItemType.Post.FriendPostGettingPopular -> TitleFormat.SameOwner(textId = R.string.activity_center_post_popular)
                ActivityCenterItemType.Post.MemoryFound -> TitleFormat.DefaultNoArgs(textId = R.string.activity_center_memory_found)
            }

            val (title, clickableRanges) = buildTitle(
                titleFormat = titleFormat,
                firstUser = firstUser,
                secondUser = secondUser,
                thirdUser = thirdUser,
                counter = extraCounter,
                lastUpdatedAt = lastUpdatedAt,
                listFormatter = listFormatter,
                timeFormatter = timeFormatter,
                stringProvider = stringProvider,
            )

            ActivityCenterItemUiModel.Post(
                activityId = activityId,
                postId = state.postId.postId,
                thumbnail = state.thumbnail?.uri,
                avatars = ActivityCenterAvatarsUiModel(
                    mainAvatar = firstUser.avatar(),
                    otherUserAvatar = secondUser?.avatar(),
                ),
                title = title,
                clickableUserRanges = clickableRanges,
                postExists = state.postFound,
                newBadge = !seen,
                activityType = state.type.typeName.name,
            )
        }

        is ActivityCenterItemState.User -> {
            // since the item redirects on the profile, no grouping
            val firstUser = state.otherUsers.getOrNull(0) ?: return null

            val titleFormat = when (state.type) {
                ActivityCenterItemType.User.ContactJoined -> TitleFormat.SameOwner(textId = R.string.activity_center_contact_joined)
                ActivityCenterItemType.User.FriendFollow -> TitleFormat.Default(textId = R.plurals.activity_center_started_following)
                ActivityCenterItemType.User.FriendRequestAccepted -> TitleFormat.SameOwner(textId = R.string.activity_center_friend_request_accepted)
            }

            val (title, clickableRanges) = buildTitle(
                titleFormat = titleFormat,
                firstUser = firstUser,
                secondUser = null,
                thirdUser = null,
                counter = 0,
                lastUpdatedAt = lastUpdatedAt,
                listFormatter = listFormatter,
                timeFormatter = timeFormatter,
                stringProvider = stringProvider,
            )

            ActivityCenterItemUiModel.User(
                user = firstUser,
                activityId = activityId,
                avatars = ActivityCenterAvatarsUiModel(
                    mainAvatar = firstUser.avatar(),
                    otherUserAvatar = null,
                ),
                title = title,
                clickableUserRanges = clickableRanges,
                newBadge = !seen,
                activityType = state.type.typeName.name,
            )
        }
    }
}

internal fun buildTitle(
    titleFormat: TitleFormat,
    firstUser: BasicUser,
    secondUser: BasicUser?,
    thirdUser: BasicUser?,
    counter: Int,
    lastUpdatedAt: Long?,
    listFormatter: BeRealListFormatter,
    timeFormatter: BeRealTimeFormatter,
    stringProvider: StringProvider,
): Pair<AnnotatedString, PersistentList<ActivityCenterClickableUserRange>> {
    val color = BeRealColors.GrayScale().gray500
    val firstUserText = firstUser.fullName ?: firstUser.userName
    val clickableUsers = mutableListOf(firstUser)

    val targetPostOwnerName = (titleFormat as? TitleFormat.WithPostOwnerPlural)?.let {
        clickableUsers.add(it.owner)
        it.owner.fullName ?: it.owner.userName
    }

    val secondUserText = when {
        secondUser != null -> {
            clickableUsers.add(secondUser)
            secondUser.fullName ?: secondUser.userName
        }

        counter > 0 -> stringProvider.getQuantity(R.plurals.general_others, counter, counter)
        else -> null
    }
    val otherText = when {
        secondUser == null -> null
        thirdUser != null && counter == 0 -> {
            clickableUsers.add(thirdUser)
            thirdUser.fullName ?: thirdUser.userName
        }

        counter > 0 -> stringProvider.getQuantity(R.plurals.general_others, counter, counter)
        else -> null
    }
    val usersTextItems = listOfNotNull(firstUserText, secondUserText, otherText)

    val formattedUserList = listFormatter.format(usersTextItems)

    val finalText = when (titleFormat) {
        is TitleFormat.DefaultNoArgs -> stringProvider[titleFormat.textId]
        is TitleFormat.Default -> stringProvider.getQuantity(titleFormat.textId, usersTextItems.size, formattedUserList)
        is TitleFormat.WithPostOwner -> stringProvider.get(titleFormat.textId, formattedUserList, titleFormat.owner.fullName ?: titleFormat.owner.userName)
        is TitleFormat.WithPostOwnerPlural -> stringProvider.getQuantity(
            titleFormat.textId,
            usersTextItems.size,
            formattedUserList,
            titleFormat.owner.fullName ?: titleFormat.owner.userName,
        )
        is TitleFormat.SameOwner -> stringProvider.get(titleFormat.textId, formattedUserList)
    }

    val clickableRanges = buildClickableUserRanges(
        users = clickableUsers,
        text = finalText,
    )

    val title = buildAnnotatedString {
        append(
            boldInText(
                text = finalText,
                textsToFind = usersTextItems + listOfNotNull(targetPostOwnerName),
            ),
        )

        lastUpdatedAt?.let {
            val formattedTime = timeFormatter.formatPerTimeUnits(it).replace(" ", NO_BREAK_SPACE)
            withStyle(
                style = BeRealTheme.typography.subHeadline.default.toSpanStyle().copy(color = color),
            ) {
                append(" $formattedTime")
            }
        }
    }

    return title to clickableRanges
}

private fun buildClickableUserRanges(
    users: List<BasicUser>,
    text: String,
): PersistentList<ActivityCenterClickableUserRange> {
    val names = users.map { it.fullName ?: it.userName }

    val clickableRanges = names.mapNotNull { name ->
        val startIndex = text.indexOf(name)
        val endIndex = startIndex + name.length

        ActivityCenterClickableUserRange(
            startIndex = startIndex,
            endIndex = endIndex,
            user = users.firstOrNull { it.fullName == name || it.userName == name }?.mapToUi() ?: return@mapNotNull null,
        )
    }.toPersistentList()

    return clickableRanges
}

internal sealed interface TitleFormat {
    data class Default(@PluralsRes val textId: Int) : TitleFormat
    data class DefaultNoArgs(@StringRes val textId: Int) : TitleFormat
    data class WithPostOwner(@StringRes val textId: Int, val owner: BasicUser) : TitleFormat
    data class WithPostOwnerPlural(@PluralsRes val textId: Int, val owner: BasicUser) : TitleFormat
    data class SameOwner(@StringRes val textId: Int) : TitleFormat
}
