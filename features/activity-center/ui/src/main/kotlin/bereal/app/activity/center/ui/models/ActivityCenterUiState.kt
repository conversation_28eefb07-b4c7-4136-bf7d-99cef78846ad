package bereal.app.activity.center.ui.models

import androidx.compose.runtime.Immutable
import androidx.compose.ui.text.AnnotatedString
import bereal.app.entities.BasicUser
import kotlinx.collections.immutable.PersistentList

@Immutable
sealed interface ActivityCenterUiState {
    @Immutable
    data object Initial : ActivityCenterUiState

    @Immutable
    data object Loading : ActivityCenterUiState

    @Immutable
    data class Empty(
        val pendingFriendRequest: ActivityCenterPendingFriendRequestState,
    ) : ActivityCenterUiState

    @Immutable
    data object Error : ActivityCenterUiState

    @Immutable
    data class Success(
        val pendingFriendRequest: ActivityCenterPendingFriendRequestState,
        val sections: PersistentList<ActivityCenterSectionUiModel>,
    ) : ActivityCenterUiState
}

@Immutable
data class ActivityCenterSectionUiModel(
    val sectionId: String,
    val title: String,
    val items: PersistentList<ActivityCenterItemUiModel>,
)

@Immutable
sealed interface ActivityCenterItemUiModel {

    val activityId: String
    val activityType: String
    val title: AnnotatedString
    val clickableUserRanges: PersistentList<ActivityCenterClickableUserRange>
    val newBadge: Boolean
    val avatars: ActivityCenterAvatarsUiModel

    @Immutable
    data class Post(
        val postId: String,
        val thumbnail: String?,
        val postExists: Boolean,
        override val activityId: String,
        override val activityType: String,
        override val avatars: ActivityCenterAvatarsUiModel,
        override val title: AnnotatedString,
        override val clickableUserRanges: PersistentList<ActivityCenterClickableUserRange>,
        override val newBadge: Boolean,
    ) : ActivityCenterItemUiModel

    @Immutable
    data class User(
        val user: BasicUser,
        override val activityId: String,
        override val activityType: String,
        override val avatars: ActivityCenterAvatarsUiModel,
        override val title: AnnotatedString,
        override val clickableUserRanges: PersistentList<ActivityCenterClickableUserRange>,
        override val newBadge: Boolean,
    ) : ActivityCenterItemUiModel

    // More types to come
}
