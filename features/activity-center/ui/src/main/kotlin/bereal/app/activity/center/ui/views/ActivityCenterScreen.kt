package bereal.app.activity.center.ui.views

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.PullRefreshState
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.activity.center.ui.ActivityCenterViewModel
import bereal.app.activity.center.ui.models.ActivityCenterAction
import bereal.app.activity.center.ui.models.ActivityCenterUiState
import bereal.app.design.BeRealScaffold
import bereal.app.design.EmptyView
import bereal.app.design.appbar.TransparentTopBar
import bereal.app.design.circularloader.BeRealCircularLoader
import bereal.app.design.error.ErrorView
import bereal.app.design.theme.BeRealTheme
import org.koin.androidx.compose.koinViewModel
import bereal.app.translations.R.string as translations

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ActivityCenterScreen() {
    val viewModel: ActivityCenterViewModel = koinViewModel()

    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val isUserRefreshingScreen: Boolean by viewModel.isUserRefreshingScreen.collectAsStateWithLifecycle()

    val pullRefreshState = rememberPullRefreshState(
        refreshing = isUserRefreshingScreen,
        onRefresh = viewModel::refreshActivityCenter,
    )

    BackHandler {
        viewModel.onClickBack()
    }

    BeRealScaffold(
        topBar = {
            TransparentTopBar(
                title = stringResource(id = translations.activity_center_screen_title),
                onClickBack = viewModel::onClickBack,
            )
        },
    ) {
        ActivityCenterContent(
            modifier = Modifier.fillMaxSize(),
            uiState = uiState,
            contentPadding = it,
            pullRefreshState = pullRefreshState,
            isUserRefreshingScreen = isUserRefreshingScreen,
            onAction = viewModel::onAction,
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ActivityCenterContent(
    modifier: Modifier = Modifier,
    uiState: ActivityCenterUiState,
    contentPadding: PaddingValues,
    pullRefreshState: PullRefreshState,
    isUserRefreshingScreen: Boolean,
    onAction: (ActivityCenterAction) -> Unit,
) {
    Box(modifier = Modifier.fillMaxSize().pullRefresh(pullRefreshState)) {
        when (uiState) {
            ActivityCenterUiState.Initial -> {
                // No-Op
            }
            is ActivityCenterUiState.Empty -> {
                Box(modifier = modifier, contentAlignment = Alignment.Center) {
                    ActivityCenterPendingFriendRequestView(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = contentPadding.calculateTopPadding())
                            .align(Alignment.TopCenter),
                        pendingFriendRequestState = uiState.pendingFriendRequest,
                        onAction = onAction,
                    )

                    EmptyView(
                        icon = bereal.app.design.R.drawable.notifications_off,
                        title = stringResource(id = translations.activity_center_empty_state_title),
                        text = stringResource(id = translations.activity_center_empty_state_subtitle),
                    )
                }
            }
            ActivityCenterUiState.Error -> {
                Box(modifier = modifier, contentAlignment = Alignment.Center) {
                    ErrorView(retryAction = { onAction(ActivityCenterAction.OnRetryClicked) })
                }
            }
            ActivityCenterUiState.Loading -> {
                Box(modifier = modifier, contentAlignment = Alignment.Center) {
                    BeRealCircularLoader()
                }
            }
            is ActivityCenterUiState.Success -> {
                ActivityCenterList(
                    modifier = modifier
                        .fillMaxSize()
                        .padding(top = contentPadding.calculateTopPadding()),
                    state = uiState,
                    onAction = onAction,
                )
            }
        }

        PullRefreshIndicator(
            refreshing = isUserRefreshingScreen,
            state = pullRefreshState,
            modifier = Modifier
                .padding(top = BeRealTheme.spacing.xl)
                .align(Alignment.TopCenter),
        )
    }
}
