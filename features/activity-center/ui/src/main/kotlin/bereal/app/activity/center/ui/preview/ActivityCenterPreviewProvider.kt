package bereal.app.activity.center.ui.preview

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import bereal.app.activity.center.ui.models.ActivityCenterAvatarsUiModel
import bereal.app.activity.center.ui.models.ActivityCenterItemUiModel
import bereal.app.activity.center.ui.models.ActivityCenterPendingFriendRequestState
import bereal.app.activity.center.ui.models.ActivityCenterSectionUiModel
import bereal.app.activity.center.ui.models.ActivityCenterUiState
import bereal.app.activity.center.ui.models.previewUserAvatar
import bereal.app.commonandroid.boldInText
import bereal.app.design.theme.BeRealTheme
import kotlinx.collections.immutable.persistentListOf

fun generateActivityCenterItemPreview(
    title: String = "Dipt<PERSON> Tathagata and <PERSON> commented on your BeReal. ",
    names: List<String> = listOf("Dipti Tathagata", "<PERSON>"),
    time: String = "33 min ago",
    newBadge: Boolean = true,
    withPost: Boolean = true,
    withTwoAvatars: Boolean = true,
): ActivityCenterItemUiModel {
    return ActivityCenterItemUiModel.Post(
        avatars = ActivityCenterAvatarsUiModel(
            mainAvatar = previewUserAvatar(),
            otherUserAvatar = if (withTwoAvatars) previewUserAvatar() else null,
        ),
        title = buildAnnotatedString {
            append(
                boldInText(
                    text = title,
                    textsToFind = names,
                ),
            )
            withStyle(
                style = BeRealTheme.typography.subHeadline.default.toSpanStyle()
                    .copy(color = Color(0xFF565658)),
            ) {
                append(time)
            }
        },
        postId = "postId",
        thumbnail = if (withPost) "https://picsum.photos/200/300" else null,
        newBadge = newBadge,
        activityId = "activityId",
        postExists = true,
        clickableUserRanges = persistentListOf(),
        activityType = "activityType",
    )
}

fun generatePendingFriendRequestPreview(
    title: String = "Pending friend request",
    subtitle: String = "Graham Spencer just sent you a friend request. ",
    names: List<String> = listOf("Graham Spencer"),
    time: String = "5 days ago",
    withBadgeCount: Int? = 1,
): ActivityCenterPendingFriendRequestState {
    return ActivityCenterPendingFriendRequestState(
        title = title,
        subtitle = buildAnnotatedString {
            append(
                boldInText(
                    text = subtitle,
                    textsToFind = names,
                ),
            )
            withStyle(
                style = BeRealTheme.typography.subHeadline.default.toSpanStyle()
                    .copy(color = Color(0xFF565658)),
            ) {
                append(time)
            }
        },
        clickableUserRanges = persistentListOf(),
        badge = if (withBadgeCount != null && withBadgeCount > 0) {
            ActivityCenterPendingFriendRequestState.Badge.Visible.WithNumber(
                numberOfPendingFriendRequest = withBadgeCount,
            )
        } else if (withBadgeCount != null && withBadgeCount == 0) {
            ActivityCenterPendingFriendRequestState.Badge.Visible.NoNumber
        } else {
            ActivityCenterPendingFriendRequestState.Badge.Hidden
        },
    )
}

fun generateActivityCenterPreview(): ActivityCenterUiState.Success {
    return ActivityCenterUiState.Success(
        pendingFriendRequest = generatePendingFriendRequestPreview(),
        sections = persistentListOf(
            ActivityCenterSectionUiModel(
                title = "New",
                items = persistentListOf(
                    generateActivityCenterItemPreview(withTwoAvatars = false, title = "Dipti Tathagata commented on your BeReal. ", names = listOf("Dipti Tathagata"), time = "Now"),
                    generateActivityCenterItemPreview(title = "Dipti Tathagata and Gustav Michael commented on your BeReal. ", names = listOf("Dipti Tathagata", "Gustav Michael"), time = "33 min ago"),
                ),
                sectionId = "new",
            ),
            ActivityCenterSectionUiModel(
                title = "Last 7 days",
                items = persistentListOf(
                    generateActivityCenterItemPreview(title = "Dipti Tathagata, Darren and 127 others reacted to your BeReal. ", names = listOf("Dipti Tathagata", "Darren"), time = "Yesterday", newBadge = false),
                    generateActivityCenterItemPreview(title = "Emad Yasmina, Aida Omar and 512 others started following you. ", names = listOf("Emad Yasmina", "Aida Omar"), time = "2 days ago", newBadge = false, withPost = false),
                    generateActivityCenterItemPreview(title = "One of your contacts has joined BeReal as Daph Lunise. ", names = listOf("Daph Lunise"), withTwoAvatars = false, newBadge = false, withPost = false),
                ),
                sectionId = "last7days",
            ),
            ActivityCenterSectionUiModel(
                title = "Last 30 days",
                items = persistentListOf(
                    generateActivityCenterItemPreview(title = "Eklavya Neha approved your friend request. ", names = listOf("Eklavya Neha"), time = "Last week", newBadge = false, withPost = false, withTwoAvatars = false),
                    generateActivityCenterItemPreview(title = "Florian commented on your BeReal. ", names = listOf("Florian"), time = "21 days ago", newBadge = false, withPost = false, withTwoAvatars = false),
                ),
                sectionId = "last30days",
            ),
        ),
    )
}
