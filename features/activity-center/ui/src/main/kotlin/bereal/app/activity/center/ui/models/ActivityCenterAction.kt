package bereal.app.activity.center.ui.models

import bereal.app.design.model.BasicUserUi

sealed interface ActivityCenterAction {
    data class OnProfileClicked(val user: BasicUserUi) : ActivityCenterAction
    data class OnActivityUserClicked(
        val user: BasicUserUi,
        val activityType: String,
        val sectionId: String,
    ) : ActivityCenterAction
    data class OnActivityPostClicked(
        val postId: String,
        val postExists: <PERSON><PERSON>an,
        val activityType: String,
        val sectionId: String,
    ) : ActivityCenterAction
    data object OnPendingFriendRequestClicked : ActivityCenterAction
    data object OnRetryClicked : ActivityCenterAction
}
