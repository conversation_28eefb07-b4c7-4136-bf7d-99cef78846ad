package bereal.app.activity.center.ui.views

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.activity.center.ui.models.ActivityCenterAction
import bereal.app.activity.center.ui.models.ActivityCenterItemUiModel
import bereal.app.activity.center.ui.preview.generateActivityCenterItemPreview
import bereal.app.design.debounceClickable
import bereal.app.design.image.RemoteImage
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.image.model.options.ImageOptions
import bereal.app.design.image.model.options.ImageSize
import bereal.app.design.theme.BeRealTheme
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders

@Composable
fun ActivityCenterItemView(
    modifier: Modifier = Modifier,
    item: ActivityCenterItemUiModel,
    sectionId: String,
    onAction: (ActivityCenterAction) -> Unit,
) {
    val imageLoader = LocalBeRealImageLoaders.current.friendTimeline
    val mainElementClick = remember(item) {
        {
            when (item) {
                is ActivityCenterItemUiModel.Post -> {
                    onAction(
                        ActivityCenterAction.OnActivityPostClicked(
                            postId = item.postId,
                            postExists = item.postExists,
                            activityType = item.activityType,
                            sectionId = sectionId,
                        ),
                    )
                }
                is ActivityCenterItemUiModel.User -> {
                    onAction(
                        ActivityCenterAction.OnActivityUserClicked(
                            user = item.avatars.mainAvatar.user,
                            activityType = item.activityType,
                            sectionId = sectionId,
                        ),
                    )
                }
            }
        }
    }

    Row(
        modifier = modifier
            .background(
                if (item.newBadge) {
                    BeRealTheme.colors.grayScale.gray900
                } else {
                    BeRealTheme.colors.background
                },
            )
            .padding(
                end = BeRealTheme.spacing.m,
                top = BeRealTheme.spacing.s,
                bottom = BeRealTheme.spacing.s,
            )
            .debounceClickable { mainElementClick() },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        if (item.newBadge) {
            Spacer(modifier = Modifier.width(BeRealTheme.spacing.xs))

            Box(
                modifier = Modifier
                    .size(6.dp)
                    .background(color = BeRealTheme.colors.palette.red, shape = CircleShape),
            )

            Spacer(modifier = Modifier.width(BeRealTheme.spacing.xs))
        } else {
            Spacer(modifier = Modifier.width(BeRealTheme.spacing.m))
        }

        ActivityCenterAvatarsView(
            avatars = item.avatars,
            onProfileClicked = { user ->
                onAction(ActivityCenterAction.OnProfileClicked(user))
            },
        )

        ClickableText(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = BeRealTheme.spacing.xm),
            text = item.title,
            style = BeRealTheme.typography.subHeadline.default.copy(color = BeRealTheme.colors.onBackground),
            onClick = { offset ->
                item.clickableUserRanges.firstOrNull { range ->
                    offset in range.startIndex..range.endIndex
                }?.let { range ->
                    onAction(ActivityCenterAction.OnProfileClicked(range.user))
                } ?: mainElementClick()
            },
        )

        (item as? ActivityCenterItemUiModel.Post)?.thumbnail?.let {
            RemoteImage(
                data = ImageDataModel.Url(it),
                imageLoader = imageLoader,
                modifier = Modifier
                    .size(52.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .aspectRatio(1f),
                contentScale = ContentScale.Crop,
                options = ImageOptions(
                    hardware = true,
                    size = ImageSize.Optimized,
                ),
            )
        }
    }
}

@Preview
@Composable
private fun ActivityCenterItem_NewBadge_withoutPost_Preview(modifier: Modifier = Modifier) {
    BeRealTheme {
        ActivityCenterItemView(
            modifier = modifier,
            item = generateActivityCenterItemPreview(withPost = false),
            sectionId = "sectionId",
            onAction = {},
        )
    }
}

@Preview
@Composable
private fun ActivityCenterItem_NewBadge_withPost_Preview(modifier: Modifier = Modifier) {
    BeRealTheme {
        ActivityCenterItemView(
            modifier = modifier,
            item = generateActivityCenterItemPreview(),
            sectionId = "sectionId",
            onAction = {},
        )
    }
}

@Preview
@Composable
private fun ActivityCenterItem_OneAvatar_withPost_Preview(modifier: Modifier = Modifier) {
    BeRealTheme {
        ActivityCenterItemView(
            modifier = modifier,
            item = generateActivityCenterItemPreview(withTwoAvatars = false, newBadge = false),
            sectionId = "sectionId",
            onAction = {},
        )
    }
}
