package bereal.app.activity.center.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import bereal.app.activity.center.ui.mapper.mapToState
import bereal.app.activity.center.ui.models.ActivityCenterAction
import bereal.app.activity.center.ui.models.ActivityCenterUiState
import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.model.AnalyticsView
import bereal.app.common.combines
import bereal.app.common.stateIn
import bereal.app.commonandroid.BeRealListFormatter
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.design.dialog.DialogDisplayer
import bereal.app.design.dialog.alert.model.Confirm
import bereal.app.design.dialog.model.Dialog
import bereal.app.design.model.mapToEntities
import bereal.app.features.activity.center.domain.models.ActivityCenterAnalyticsDomainModel
import bereal.app.features.activity.center.domain.models.ActivityCenterItemType
import bereal.app.features.activity.center.domain.models.ActivityCenterSectionType
import bereal.app.features.activity.center.domain.usecases.FetchActivityCenterUseCase
import bereal.app.features.activity.center.domain.usecases.MarkAllActivitiesAsSeenUseCase
import bereal.app.features.activity.center.domain.usecases.ObserveActivitiesUseCase
import bereal.app.features.activity.center.domain.usecases.ObserveDisplayRedDotInActivityCenterUseCase
import bereal.app.features.activity.center.domain.usecases.ObserveForceDisplayRedDotForFriendRequestInActivityCenterUseCase
import bereal.app.features.official.accounts.profile.ProfileNavigator
import bereal.app.features.relationship.domain.models.FriendingAnalyticsDomainModel
import bereal.app.features.relationship.domain.usecases.ObserveReceivedPendingFriendRequestsUseCase
import bereal.app.features.relationship.domain.usecases.ObserveUnseenPendingFriendRequestsCount
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.BottomSheetFriendRequestsDestination
import bereal.app.navigation.model.direction.ReactionsDirection
import bereal.app.post.usecases.GetPostPlaceholderUseCase
import bereal.app.settings.usecases.SetRepetitiveHighlightInActivityCenterLastSeenUseCase
import bereal.app.time.formatter.BeRealTimeFormatter
import bereal.app.time.provider.BeRealTimeProvider
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import bereal.app.translations.R.string as translations

@KoinViewModel
class ActivityCenterViewModel(
    observeActivitiesUseCase: ObserveActivitiesUseCase,
    observeReceivedPendingFriendRequestsUseCase: ObserveReceivedPendingFriendRequestsUseCase,
    observeUnseenPendingFriendRequestsCount: ObserveUnseenPendingFriendRequestsCount,
    private val navigationManager: NavigationManager,
    private val profileNavigator: ProfileNavigator,
    private val dispatcherProvider: DispatcherProvider,
    private val markAllActivitiesAsSeenUseCase: MarkAllActivitiesAsSeenUseCase,
    private val fetchActivityCenterUseCase: FetchActivityCenterUseCase,
    private val timeProvider: BeRealTimeProvider,
    private val timeFormatter: BeRealTimeFormatter,
    private val listFormatter: BeRealListFormatter,
    private val stringProvider: StringProvider,
    private val getPostPlaceholderUseCase: GetPostPlaceholderUseCase,
    private val dialogDisplayer: DialogDisplayer,
    private val analyticsManager: AnalyticsManager,
    private val observeDisplayRedDotInActivityCenterUseCase: ObserveDisplayRedDotInActivityCenterUseCase,
    private val observeForceDisplayRedDotForFriendRequestInActivityCenterUseCase: ObserveForceDisplayRedDotForFriendRequestInActivityCenterUseCase,
    private val setRepetitiveHighlightInActivityCenterLastSeenUseCase: SetRepetitiveHighlightInActivityCenterLastSeenUseCase,
) : ViewModel() {

    private val analyticsView = AnalyticsView.ActivityCenter
    private val isFetchingActivityCenter = MutableStateFlow(false)
    private val hasFetchingActivityFailed = MutableStateFlow(false)

    init {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            isFetchingActivityCenter.value = true
            fetchActivityCenterUseCase(withHydration = true).alsoFailure {
                hasFetchingActivityFailed.value = true
            }
            isFetchingActivityCenter.value = false
        }

        viewModelScope.launch(dispatcherProvider.viewmodel) {
            val hadRedDot = observeDisplayRedDotInActivityCenterUseCase().firstOrNull() ?: false
            analyticsManager.logEvent(
                ActivityCenterAnalyticsDomainModel.ViewedActivityCenter(hadRedDot = hadRedDot),
            )
        }
    }

    private val _isUserRefreshingScreen = MutableStateFlow(false)
    val isUserRefreshingScreen: StateFlow<Boolean> = _isUserRefreshingScreen

    val uiState: StateFlow<ActivityCenterUiState> = combines(
        observeActivitiesUseCase(),
        observeReceivedPendingFriendRequestsUseCase(),
        observeUnseenPendingFriendRequestsCount(),
        observeForceDisplayRedDotForFriendRequestInActivityCenterUseCase(),
        isFetchingActivityCenter,
        hasFetchingActivityFailed,
    )
        .map { (activities, friendRequests, unseenFriendRequestsCount, forceDisplayRedDotForFriendRequestInActivityCenter, isLoading, hasError) ->
            activities.mapToState(
                pendingFriendRequests = friendRequests,
                unseenFriendRequestsCount = unseenFriendRequestsCount,
                forceDisplayRedDotForFriendRequestInActivityCenter = forceDisplayRedDotForFriendRequestInActivityCenter,
                isLoading = isLoading,
                hasError = hasError,
                timeProvider = timeProvider,
                timeFormatter = timeFormatter,
                listFormatter = listFormatter,
                stringProvider = stringProvider,
            )
        }
        .flowOn(dispatcherProvider.viewmodel)
        .stateIn(viewModelScope, ActivityCenterUiState.Initial)

    fun onAction(action: ActivityCenterAction) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            when (action) {
                is ActivityCenterAction.OnActivityPostClicked -> {
                    val activityType = ActivityCenterItemType.TypeName.fromName(action.activityType) ?: return@launch

                    analyticsManager.logEvent(
                        ActivityCenterAnalyticsDomainModel.OpenedActivityCenter(
                            activityType = activityType,
                            sectionType = ActivityCenterSectionType.valueOf(action.sectionId),
                            isUnavailable = !action.postExists,
                        ),
                    )
                    if (action.postExists) {
                        val directionTo = when (activityType) {
                            ActivityCenterItemType.Post.FirstPostInAWhile -> ReactionsDirection.ReactionsTo.FirstPostInAWhile
                            ActivityCenterItemType.Post.CommentMention -> ReactionsDirection.ReactionsTo.SeeCommentsText

                            ActivityCenterItemType.Post.Tag,
                            ActivityCenterItemType.Post.MemoryFound,
                            -> ReactionsDirection.ReactionsTo.MyBeReal

                            ActivityCenterItemType.Post.CaptionMention,
                            ActivityCenterItemType.Post.Comment,
                            ActivityCenterItemType.Post.CommentFollowUp,
                            ActivityCenterItemType.Post.RealMoji,
                            ActivityCenterItemType.Post.Repost,
                            ActivityCenterItemType.Post.FriendCommentedOtherFriendPost,
                            ActivityCenterItemType.Post.FriendPostGettingPopular,
                            -> ReactionsDirection.ReactionsTo.RealMojis

                            ActivityCenterItemType.User.ContactJoined,
                            ActivityCenterItemType.User.FriendFollow,
                            ActivityCenterItemType.User.FriendRequestAccepted,
                            -> null
                        }
                        val directionFrom = when (activityType) {
                            ActivityCenterItemType.Post.MemoryFound -> ReactionsDirection.ReactionsFrom.ActivityCenterMemoryFound
                            else -> ReactionsDirection.ReactionsFrom.ActivityCenter
                        }
                        directionTo?.let {
                            navigationManager.navigate(
                                ReactionsDirection.direction(
                                    postId = action.postId,
                                    from = directionFrom,
                                    to = it,
                                    placeholder = getPostPlaceholderUseCase(
                                        postId = action.postId,
                                    ),
                                    fromAnalyticsView = AnalyticsView.ActivityCenter.value,
                                ),
                            )
                        }
                    } else {
                        dialogDisplayer.display(
                            dialog = Dialog.Alert(
                                title = stringProvider[translations.activity_center_post_unavailable_title],
                                description = stringProvider[translations.activity_center_post_unavailable_title],
                                cancel = null,
                                confirm = Confirm(
                                    customText = stringProvider[translations.general_yes],
                                    isCritical = false,
                                    onConfirm = {},
                                ),
                            ),
                        )
                    }
                }

                is ActivityCenterAction.OnActivityUserClicked -> {
                    val activityType = ActivityCenterItemType.TypeName.fromName(action.activityType) ?: return@launch

                    analyticsManager.logEvent(
                        ActivityCenterAnalyticsDomainModel.OpenedActivityCenter(
                            activityType = activityType,
                            sectionType = ActivityCenterSectionType.valueOf(action.sectionId),
                            isUnavailable = false,
                        ),
                    )

                    profileNavigator.navigate(
                        userId = action.user.uid,
                        user = action.user.mapToEntities(),
                        fromAnalyticsView = analyticsView,
                    )
                }

                is ActivityCenterAction.OnProfileClicked -> {
                    profileNavigator.navigate(
                        userId = action.user.uid,
                        user = action.user.mapToEntities(),
                        fromAnalyticsView = analyticsView,
                    )
                }

                is ActivityCenterAction.OnPendingFriendRequestClicked -> {
                    analyticsManager.logEvent(
                        FriendingAnalyticsDomainModel.ViewedFriendRequests(view = analyticsView),
                    )
                    observeForceDisplayRedDotForFriendRequestInActivityCenterUseCase()
                        .firstOrNull()
                        ?.let { forceDisplayRedDot ->
                            if (forceDisplayRedDot) {
                                setRepetitiveHighlightInActivityCenterLastSeenUseCase()
                            }
                        }
                    navigationManager.navigate(BottomSheetFriendRequestsDestination.route)
                }

                is ActivityCenterAction.OnRetryClicked -> {
                    refreshActivityCenter()
                }
            }
        }
    }

    fun refreshActivityCenter() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            _isUserRefreshingScreen.value = true
            fetchActivityCenterUseCase(withHydration = true)
            _isUserRefreshingScreen.value = false
        }
    }

    fun onClickBack() {
        markAllActivitiesAsSeenUseCase()
        navigationManager.pop()
    }
}
