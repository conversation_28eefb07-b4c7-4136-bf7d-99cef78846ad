package bereal.app.activity.center.ui.views

import android.graphics.Color
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import bereal.app.activity.center.ui.models.ActivityCenterAction
import bereal.app.activity.center.ui.models.ActivityCenterClickableUserRange
import bereal.app.activity.center.ui.models.ActivityCenterPendingFriendRequestState
import bereal.app.activity.center.ui.preview.generatePendingFriendRequestPreview
import bereal.app.design.debounceClickable
import bereal.app.design.theme.BeRealTheme
import kotlinx.collections.immutable.PersistentList

@Composable
fun ActivityCenterPendingFriendRequestView(
    modifier: Modifier = Modifier,
    pendingFriendRequestState: ActivityCenterPendingFriendRequestState,
    onAction: (ActivityCenterAction) -> Unit,
) {
    Row(
        modifier = modifier.debounceClickable {
            onAction(ActivityCenterAction.OnPendingFriendRequestClicked)
        },
        verticalAlignment = Alignment.CenterVertically,
    ) {
        PendingFriendRequestIcon(modifier = Modifier.size(60.dp))

        PendingFriendRequestContent(
            modifier = Modifier.weight(1f).padding(horizontal = BeRealTheme.spacing.s),
            title = pendingFriendRequestState.title,
            subtitle = pendingFriendRequestState.subtitle,
            clickableUserRanges = pendingFriendRequestState.clickableUserRanges,
            onAction = onAction,
        )

        if (pendingFriendRequestState.badge is ActivityCenterPendingFriendRequestState.Badge.Visible) {
            PendingFriendRequestBadge(
                modifier = Modifier.size(20.dp),
                badge = pendingFriendRequestState.badge,
            )
        }

        Icon(
            modifier = Modifier.size(24.dp),
            painter = painterResource(id = bereal.app.design.R.drawable.chevron_right),
            tint = BeRealTheme.colors.onBackground,
            contentDescription = null,
        )
    }
}

@Composable
private fun PendingFriendRequestIcon(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(BeRealTheme.colors.grayScale.gray700),
        contentAlignment = Alignment.Center,
    ) {
        Icon(
            modifier = Modifier.size(36.dp),
            painter = painterResource(id = bereal.app.design.R.drawable.my_friends),
            tint = BeRealTheme.colors.onBackground,
            contentDescription = null,
        )
    }
}

@Composable
private fun PendingFriendRequestContent(
    modifier: Modifier = Modifier,
    title: String,
    subtitle: AnnotatedString,
    clickableUserRanges: PersistentList<ActivityCenterClickableUserRange>,
    onAction: (ActivityCenterAction) -> Unit,
) {
    Column(
        modifier = modifier.padding(horizontal = BeRealTheme.spacing.s),
        verticalArrangement = Arrangement.spacedBy(BeRealTheme.spacing.xs),
    ) {
        Text(
            text = title,
            style = BeRealTheme.typography.subHeadline.bold,
            color = BeRealTheme.colors.onBackground,
            maxLines = 1,
        )

        ClickableText(
            maxLines = 4,
            overflow = TextOverflow.Ellipsis,
            text = subtitle,
            style = BeRealTheme.typography.subHeadline.default.copy(color = BeRealTheme.colors.onBackground),
            onClick = { offset ->
                clickableUserRanges.firstOrNull { range ->
                    offset in range.startIndex..range.endIndex
                }?.let { range ->
                    onAction(ActivityCenterAction.OnProfileClicked(range.user))
                } ?: onAction(ActivityCenterAction.OnPendingFriendRequestClicked)
            },
        )
    }
}

@Composable
private fun PendingFriendRequestBadge(
    modifier: Modifier = Modifier,
    badge: ActivityCenterPendingFriendRequestState.Badge.Visible,
) {
    Box(
        modifier = modifier
            .size(20.dp)
            .clip(CircleShape)
            .background(BeRealTheme.colors.palette.red),
        contentAlignment = Alignment.Center,
    ) {
        if (badge is ActivityCenterPendingFriendRequestState.Badge.Visible.WithNumber) {
            Text(
                text = badge.numberOfPendingFriendRequest.toString(),
                color = BeRealTheme.colors.onBackground,
                style = BeRealTheme.typography.caption1.bold,
            )
        }
    }
}

@Preview(showBackground = true, backgroundColor = Color.BLACK.toLong())
@Composable
private fun ActivityCenterPendingFriendRequestView_severalPendingRequestAllSeen_Preview() {
    BeRealTheme {
        ActivityCenterPendingFriendRequestView(
            pendingFriendRequestState = generatePendingFriendRequestPreview(
                subtitle = "Abc, and Xyz sent you a friend request",
                withBadgeCount = 0,
            ),
            onAction = {},
        )
    }
}

@Preview(showBackground = true, backgroundColor = Color.BLACK.toLong())
@Composable
private fun ActivityCenterPendingFriendRequestView_severalPending_Preview() {
    BeRealTheme {
        ActivityCenterPendingFriendRequestView(
            pendingFriendRequestState = generatePendingFriendRequestPreview(
                subtitle = "You have 3 pending friend requests. ",
                withBadgeCount = 3,
            ),
            onAction = {},
        )
    }
}

@Preview(showBackground = true, backgroundColor = Color.BLACK.toLong())
@Composable
private fun ActivityCenterPendingFriendRequestView_onePending_Preview() {
    BeRealTheme {
        ActivityCenterPendingFriendRequestView(
            pendingFriendRequestState = generatePendingFriendRequestPreview(),
            onAction = {},
        )
    }
}
