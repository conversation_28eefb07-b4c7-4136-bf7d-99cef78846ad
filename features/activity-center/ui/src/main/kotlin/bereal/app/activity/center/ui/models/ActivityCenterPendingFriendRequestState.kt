package bereal.app.activity.center.ui.models

import androidx.compose.runtime.Immutable
import androidx.compose.ui.text.AnnotatedString
import kotlinx.collections.immutable.PersistentList

@Immutable
data class ActivityCenterPendingFriendRequestState(
    val title: String,
    val subtitle: AnnotatedString,
    val badge: Badge,
    val clickableUserRanges: PersistentList<ActivityCenterClickableUserRange>,
) {
    @Immutable
    sealed interface Badge {
        @Immutable
        data object Hidden : Badge

        @Immutable
        sealed interface Visible : Badge {
            @Immutable
            data object NoNumber : Visible

            @Immutable
            data class WithNumber(
                val numberOfPendingFriendRequest: Int,
            ) : Visible
        }
    }
}
