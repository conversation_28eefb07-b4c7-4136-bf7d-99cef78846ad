package bereal.app.activity.center.ui.views

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.activity.center.ui.models.ActivityCenterAction
import bereal.app.activity.center.ui.models.ActivityCenterUiState
import bereal.app.activity.center.ui.preview.generateActivityCenterPreview
import bereal.app.design.theme.BeRealTheme

@Composable
fun ActivityCenterList(
    modifier: Modifier = Modifier,
    state: ActivityCenterUiState.Success,
    onAction: (ActivityCenterAction) -> Unit,
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(bottom = BeRealTheme.spacing.xl),
    ) {
        item {
            ActivityCenterPendingFriendRequestView(
                modifier = Modifier.fillMaxWidth().padding(horizontal = BeRealTheme.spacing.m),
                pendingFriendRequestState = state.pendingFriendRequest,
                onAction = onAction,
            )

            Spacer(modifier = Modifier.height(BeRealTheme.spacing.m))
        }

        state.sections.forEach { section ->
            item {
                Text(
                    modifier = Modifier.fillMaxWidth()
                        .padding(horizontal = BeRealTheme.spacing.m, vertical = BeRealTheme.spacing.s),
                    text = section.title.uppercase(),
                    style = BeRealTheme.typography.footnote.semibold,
                    color = BeRealTheme.colors.grayScale.gray100,
                )
            }

            itemsIndexed(section.items) { index, item ->
                ActivityCenterItemView(
                    modifier = Modifier.fillMaxWidth(),
                    item = item,
                    onAction = onAction,
                    sectionId = section.sectionId,
                )
            }
        }
    }
}

@Preview
@Composable
private fun ActivityCenterListPreview() {
    BeRealTheme {
        ActivityCenterList(state = generateActivityCenterPreview(), onAction = {})
    }
}
