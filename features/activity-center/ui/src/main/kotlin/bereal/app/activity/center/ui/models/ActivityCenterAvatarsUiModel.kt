package bereal.app.activity.center.ui.models

import androidx.compose.runtime.Immutable
import bereal.app.design.avatar.UserAvatar
import bereal.app.design.avatar.avatar
import bereal.app.entities.BasicUser

@Immutable
data class ActivityCenterAvatarsUiModel(
    val mainAvatar: UserAvatar,
    val otherUserAvatar: UserAvatar?,
)

fun previewUserAvatar(): UserAvatar {
    return BasicUser.generate().avatar()
}
