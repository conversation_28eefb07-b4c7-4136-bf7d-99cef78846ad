plugins {
    id(libs.plugins.bereal.library.get().pluginId)
    id(libs.plugins.bereal.ui.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
}

android {
    namespace = "bereal.app.features.activity.center.ui"
}

dependencies {
    implementation(projects.features.post.domain)

    implementation(projects.platform.common)
    implementation(projects.platform.commonAndroid)
    implementation(projects.platform.entities)
    implementation(projects.platform.image.coreUi)

    implementation(projects.features.activityCenter.domain)
    implementation(projects.features.relationship.domain)
    implementation(projects.features.profile.navigation)

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.settings.domain)
    implementation(projects.platform.navigation)
    implementation(projects.platform.time)

    implementation(libs.androidx.navigation.compose)

    remoteImage()
    implementation(libs.androidx.constraintLayout)
}
