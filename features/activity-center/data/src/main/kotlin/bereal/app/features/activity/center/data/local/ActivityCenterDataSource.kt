package bereal.app.features.activity.center.data.local

import bereal.app.entities.UserId
import bereal.app.features.activity.center.domain.models.ActivityCenterRecord
import bereal.app.features.activity.center.domain.models.CoreActivityCenterItem
import kotlinx.coroutines.flow.Flow

interface ActivityCenterDataSource {
    suspend fun saveActivities(accountOwnerUid: UserId, cursor: String?, activities: List<ActivityCenterRecord>)
    fun observeActivitiesForUser(accountOwnerUid: UserId): Flow<List<CoreActivityCenterItem>>
    fun observeSeenActivitiesIdsForUser(accountOwnerUid: UserId): Flow<Set<String>>
    fun observeUnseenActivitiesCountForUser(accountOwnerUid: UserId): Flow<Int>
    suspend fun getCursor(accountOwnerUid: UserId): String?
    suspend fun clearActivitiesNotExistingAnymore(activitiesIds: List<String>)
    suspend fun markAllActivitiesAsSeenForUser(accountOwnerUid: UserId)
    suspend fun markActivityAsSeenForEntity(accountOwnerUid: UserId, mainElementId: String)
    suspend fun getUnseenUserActivitiesIds(accountOwnerUid: UserId, subElementUserId: String): List<String>
    suspend fun markActivitiesAsSeen(activitiesIds: List<String>)
}
