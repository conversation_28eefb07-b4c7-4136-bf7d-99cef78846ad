package bereal.app.features.activity.center.data

import bereal.app.common.Either
import bereal.app.common.Success
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.CacheControlType
import bereal.app.entities.KnownEntities
import bereal.app.entities.KnownEntitiesWithStaleness
import bereal.app.entities.UserId
import bereal.app.entities.error.GenericError
import bereal.app.entities.toUserId
import bereal.app.features.activity.center.data.local.ActivityCenterDataSource
import bereal.app.features.activity.center.data.remote.ActivityCenterRemoteDataSource
import bereal.app.features.activity.center.domain.ActivityCenterRepository
import bereal.app.features.activity.center.domain.models.CoreActivityCenterItem
import bereal.app.features.feed.domain.common.repository.CoreFeedRepository
import bereal.app.user.usecases.ObserveMyUserUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single

private typealias LastUpdatedAtInMs = Long
private typealias ActivityId = String

@Single
class ActivityCenterRepositoryImpl(
    observeMyUserUseCase: ObserveMyUserUseCase,
    applicationScope: CoroutineScope,
    private val remoteActivityCenterDataSource: ActivityCenterRemoteDataSource,
    private val coreFeedRepository: CoreFeedRepository,
    private val activityCenterDataSource: ActivityCenterDataSource,
    private val dispatcherProvider: DispatcherProvider,
) : ActivityCenterRepository {

    private val lastFetchedActivities = mutableMapOf<ActivityId, LastUpdatedAtInMs>()

    private val seenActivitiesIds: SharedFlow<Set<String>> = observeMyUserUseCase()
        .map { it.uid }
        .distinctUntilChanged()
        .flatMapLatest {
            activityCenterDataSource.observeSeenActivitiesIdsForUser(it.toUserId()).distinctUntilChanged()
        }
        .flowOn(dispatcherProvider.data)
        .shareIn(applicationScope, started = SharingStarted.WhileSubscribed(5000), replay = 1)

    override suspend fun getActivityCenter(
        withHydration: Boolean,
        accountOwnerUid: UserId,
    ): Either<GenericError, Long> = withContext(dispatcherProvider.data) {
        val activityCenterCursor = activityCenterDataSource.getCursor(accountOwnerUid)
        val knownEntities = if (withHydration) {
            coreFeedRepository.getKnownEntities(
                listOf(
                    KnownEntitiesWithStaleness(
                        type = CacheControlType.User,
                        withStaleness = true,
                    ),
                    KnownEntitiesWithStaleness(
                        type = CacheControlType.Post,
                        withStaleness = false,
                    ),
                ),
            )
        } else {
            KnownEntities(emptyList(), emptyList())
        }

        remoteActivityCenterDataSource.getActivityFeed(
            activityCenterKnownEntities = knownEntities,
            activityCenterCursor = activityCenterCursor,
            withHydration = withHydration,
        ).mapSuccess {
            val activitiesIds = it.activities.map { activity -> activity.activityId }
            if (withHydration) {
                activityCenterDataSource.clearActivitiesNotExistingAnymore(activitiesIds = activitiesIds)
                coreFeedRepository.updateEntities(
                    myUserId = accountOwnerUid,
                    entities = it.entities,
                    relationshipGraphsToReset = emptySet(),
                    shouldInsertPostEntities = true,
                )
            }

            val activities = if (withHydration) {
                // when we hydrate and display the activity center, we want to insert all the activities without any check
                it.activities
            } else {
                // For the polling without hydration, we keep track of the lastUpdatedAt for each activityId to avoid inserting the same activity multiple times (because even performing an upsert in the DB, it's still a waste of resources)
                it.activities.filter { item ->
                    val existingUpdatedAt = lastFetchedActivities[item.activityId]
                    existingUpdatedAt == null || existingUpdatedAt != item.lastUpdatedAt
                }
            }

            val activitiesToInsert = activities.map { activity ->
                // in case of BE deSynchro, we want to keep the seen status of the activities. Mobile has always a better understanding of what has been seen
                val seen = seenActivitiesIds.firstOrNull()?.contains(activity.activityId) ?: false
                activity.copy(seen = if (seen) true else activity.seen)
            }

            if (activitiesToInsert.isNotEmpty()) {
                activityCenterDataSource.saveActivities(accountOwnerUid = accountOwnerUid, cursor = it.cursor, activities = activitiesToInsert)
            }
            lastFetchedActivities.putAll(activitiesToInsert.associateBy({ it.activityId }, { it.lastUpdatedAt }))

            it.cooldownInMs
        }
    }

    override fun observeUnseenActivitiesCount(accountOwnerUid: UserId): Flow<Int> {
        return activityCenterDataSource.observeUnseenActivitiesCountForUser(accountOwnerUid)
    }

    override fun observeActivities(accountOwnerUid: UserId): Flow<List<CoreActivityCenterItem>> {
        return activityCenterDataSource.observeActivitiesForUser(accountOwnerUid)
    }

    override suspend fun markAllActivitiesAsSeen(accountOwnerUid: UserId): Either<GenericError, Unit> {
        activityCenterDataSource.markAllActivitiesAsSeenForUser(accountOwnerUid)
        return remoteActivityCenterDataSource.markAllActivitiesAsSeen()
    }

    override suspend fun markActivityAsSeenForEntity(accountOwnerUid: UserId, mainElementId: String): Either<GenericError, Unit> {
        activityCenterDataSource.markActivityAsSeenForEntity(accountOwnerUid = accountOwnerUid, mainElementId = mainElementId)
        return remoteActivityCenterDataSource.markActivityAsSeenForEntity(entityId = mainElementId)
    }

    override suspend fun markUserActivitiesAsSeen(accountOwnerUid: UserId, subElementUserId: String): Either<GenericError, Unit> {
        val activitiesIds = activityCenterDataSource.getUnseenUserActivitiesIds(
            accountOwnerUid = accountOwnerUid,
            subElementUserId = subElementUserId,
        )
        if (activitiesIds.isNotEmpty()) {
            activityCenterDataSource.markActivitiesAsSeen(activitiesIds)
            return remoteActivityCenterDataSource.markActivitiesAsSeen(activitiesIds)
        } else {
            return Success(Unit)
        }
    }
}
