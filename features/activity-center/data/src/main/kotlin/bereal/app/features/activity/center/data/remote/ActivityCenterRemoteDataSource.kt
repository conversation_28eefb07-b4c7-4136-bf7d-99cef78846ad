package bereal.app.features.activity.center.data.remote

import bereal.app.common.Either
import bereal.app.entities.KnownEntities
import bereal.app.entities.error.GenericError
import bereal.app.features.activity.center.data.models.GetActivityCenterResult

interface ActivityCenterRemoteDataSource {
    suspend fun getActivityFeed(
        activityCenterKnownEntities: KnownEntities,
        activityCenterCursor: String?,
        withHydration: <PERSON>olean,
    ): Either<GenericError, GetActivityCenterResult>

    suspend fun markAllActivitiesAsSeen(): Either<GenericError, Unit>

    suspend fun markActivityAsSeenForEntity(entityId: String): Either<GenericError, Unit>
    suspend fun markActivitiesAsSeen(activitiesIds: List<String>): Either<GenericError, Unit>
}
