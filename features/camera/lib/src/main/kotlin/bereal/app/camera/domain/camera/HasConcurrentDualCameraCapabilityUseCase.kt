package bereal.app.camera.domain.camera

import android.content.Context
import androidx.camera.core.CameraSelector
import androidx.camera.core.CameraUnavailableException
import androidx.camera.core.InitializationException
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.lifecycle.awaitInstance
import org.koin.core.annotation.Factory

@Factory
class HasConcurrentDualCameraCapabilityUseCase(
    private val context: Context,
) {
    suspend operator fun invoke(): <PERSON><PERSON><PERSON> {
        return try {
            val cameraProvider = ProcessCameraProvider.awaitInstance(context)
            cameraProvider.availableConcurrentCameraInfos.any { cameraInfos ->
                val hasFront = cameraInfos.any { it.lensFacing == CameraSelector.LENS_FACING_FRONT }
                val hasBack = cameraInfos.any { it.lensFacing == CameraSelector.LENS_FACING_BACK }

                // Found a group that can do front + back simultaneously
                hasFront && hasBack
            }
        } catch (e: CameraUnavailableException) {
            false
        } catch (e: InitializationException) {
            false
        }
    }
}
