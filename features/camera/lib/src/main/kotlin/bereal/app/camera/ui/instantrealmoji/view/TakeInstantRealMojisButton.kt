package bereal.app.camera.ui.instantrealmoji.view

import androidx.compose.foundation.layout.Box
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.views.ShutterButton

@Composable
fun TakeInstantRealMojisButton(
    modifier: Modifier = Modifier,
    emoji: String,
    areButtonClickable: Boolean,
    takePicture: () -> Unit,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        ShutterButton(
            visible = true,
            isClickable = areButtonClickable,
            debounceOnClick = takePicture,
        )

        Text(
            text = emoji,
            style = BeRealTheme.typography.title1.default,
        )
    }
}
