package bereal.app.camera.data

import android.graphics.Bitmap
import bereal.app.common.Either
import bereal.app.common.Failure
import bereal.app.common.bytes.kb
import bereal.app.common.bytes.mb
import bereal.app.common.success
import bereal.app.common.then
import bereal.app.entities.BeRealMedia
import bereal.app.image.core.BeRealImageSizes
import bereal.app.image.core.ImageProcessor
import bereal.app.image.core.model.ImageDestination
import bereal.app.image.core.model.ImageFormat
import bereal.app.image.core.model.ImageOperation
import bereal.app.image.core.model.ImageResult
import bereal.app.image.core.model.MaxImageBytesSize
import bereal.app.store.filestore.FileStore
import bereal.app.store.filestore.model.FileCacheDir
import bereal.app.store.filestore.model.FileExtension
import org.koin.core.annotation.Factory

@Factory
class RealMojiStore(
    private val imageProcessor: ImageProcessor,
    private val fileStore: FileStore,
) {
    fun bitmapToFile(
        bitmap: Bitmap,
        fileName: String? = null,
        destinationDir: FileCacheDir,
    ): Either<Throwable, BeRealMedia> {
        // Here we can't use imageProcessor with from/operation/save as the operation recycle the bitmap and we are still using it in the View
        return imageProcessor.resizer
            .resize(
                bitmap = bitmap,
                resize = ImageOperation.Resize.Square(BeRealImageSizes.REALMOJI_SIZE),
            )
            .mapSuccess { scaledBitmap ->
                return fileStore
                    .file(
                        fileName = fileName,
                        fileExtension = FileExtension.WEBP,
                        destinationDir = destinationDir,
                    )
                    .let { file ->
                        imageProcessor.saver
                            .save(
                                image = scaledBitmap,
                                into = ImageResult.SaveInto(
                                    destination = ImageDestination.File(file),
                                    imageFormat = ImageFormat.WebP,
                                    maxImageBytesSize = MaxImageBytesSize.LimitedTo(
                                        tryToReduceTo = 500.kb,
                                        maxAccepted = 1.mb,
                                    ),
                                    quality = BeRealImageSizes.REALMOJI_QUALITY,
                                ),
                            )
                    }
                    .also {
                        scaledBitmap.recycle()
                    }
                    .then {
                        it?.let {
                            BeRealMedia(
                                uri = it.toString(),
                                height = BeRealImageSizes.REALMOJI_SIZE,
                                width = BeRealImageSizes.REALMOJI_SIZE,
                                mediaType = BeRealMedia.MediaType.IMAGE,
                            ).success()
                        } ?: Failure(Throwable("Failed to save in file"))
                    }
            }
    }
}
