package bereal.app.camera.ui.instantrealmoji.domain

import bereal.app.camera.data.RealMojiStore
import bereal.app.common.Either
import bereal.app.entities.BeRealMedia
import bereal.app.features.camera.ui.models.CameraImageBitmapUiModel
import bereal.app.store.filestore.model.FileCacheDir
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.koin.core.annotation.Factory

@Factory
class SaveInstantRealmojiIntoFileUseCase(
    private val realMojiStore: RealMojiStore,
) {

    suspend operator fun invoke(
        image: CameraImageBitmapUiModel,
        fileName: String,
    ): Either<Throwable, BeRealMedia> = coroutineScope {
        // Process the Instant RealMoji and send it to the backend
        val processJob = async {
            // 'filename = postId' is necessary to only have one instant-realmoji by post
            // in memory
            realMojiStore.bitmapToFile(image.imageBitmap.stableValue, fileName, FileCacheDir.RealMojis)
        }
        // Wait to be sure the bitmap is saved into a file before popping the screen and releasing the memory
        val processResult = processJob.await()

        val height = image.height
        val width = image.width

        processResult
            .mapSuccess { imageUri ->
                BeRealMedia(
                    uri = imageUri.uri,
                    height = height,
                    width = width,
                    mediaType = BeRealMedia.MediaType.IMAGE,
                )
            }
    }
}
