package bereal.app.camera.ui.bereal.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import bereal.app.design.berealimageview.model.BeRealViewConstants.CAMERA_ASPECT_RATIO_PORTRAIT
import bereal.app.design.berealimageview.model.BeRealViewConstants.PRIMARY_ROUNDED_CORNER
import bereal.app.design.theme.BeRealTheme

@Composable
fun CameraPreviewError(
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(CAMERA_ASPECT_RATIO_PORTRAIT) // to take same place as CameraXPreview
            .clip(RoundedCornerShape(PRIMARY_ROUNDED_CORNER))
            .background(color = BeRealTheme.colors.grayScale.gray900),
        contentAlignment = Alignment.Center,
    ) {}
}
