package bereal.app.camera.ui.instantrealmoji.domain

import bereal.app.analytics.model.AnalyticsMediaType
import bereal.app.analytics.model.AnalyticsPostType
import bereal.app.analytics.model.AnalyticsView
import bereal.app.common.Either
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.entities.BeRealMedia
import bereal.app.features.camera.ui.models.CameraImageBitmapUiModel
import bereal.app.realmoji.domain.upload.UploadInstantRealmojiIntoPostUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory
import java.util.UUID

@Factory
class SendInstandRealmojiAsyncUseCase(
    private val uploadInstantRealmojiIntoPost: UploadInstantRealmojiIntoPostUseCase,
    private val saveInstantRealmojiIntoFile: SaveInstantRealmojiIntoFileUseCase,
    private val dispatcherProvider: DispatcherProvider,
    private val applicationScope: CoroutineScope,
) {

    companion object {
        const val INSTANT_PHOTO_DISPLAY_TIME: Long = 400
    }

    suspend operator fun invoke(
        index: Int?,
        image: CameraImageBitmapUiModel,
        postId: String?,
        view: AnalyticsView,
        postType: AnalyticsPostType?,
        mediaType: AnalyticsMediaType?,
        isNearbyPostInHome: Boolean,
        isFoF: Boolean,
    ): Either<Throwable, BeRealMedia> = coroutineScope {
        saveInstantRealmojiIntoFile(
            image = image,
            fileName = computeFileName(postId),
        ).alsoSuccess { berealImage ->
            // Start min delay for the user to have time to see the result of the instant realmoji
            val minDelay = async(dispatcherProvider.domain) {
                delay(INSTANT_PHOTO_DISPLAY_TIME)
            }

            if (postId != null) {
                applicationScope.launch(dispatcherProvider.domain) {
                    // if we have defined a post id, we starts the upload, otherways we only return the local bereal image
                    uploadInstantRealmojiIntoPost(
                        postId = postId,
                        berealMedia = berealImage,
                        view = view,
                        postType = postType,
                        mediaType = mediaType,
                        isNearbyPostInHome = isNearbyPostInHome,
                        isFoF = isFoF,
                        index = index,
                    )
                }
            }

            minDelay.await()
        }
    }

    private fun computeFileName(postId: String?): String {
        return postId ?: ("id_" + UUID.randomUUID().toString())
    }
}
