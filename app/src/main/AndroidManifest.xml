<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:replace="android:maxSdkVersion"
        android:maxSdkVersion="29"/>
    <!-- Since Tiramisu(13), we should ask for notification permission -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <!-- For video capture -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!--
        to know if apps are installed
        source : https://medium.com/androiddevelopers/package-visibility-in-android-11-cc857f221cd9
    -->
    <queries>
        <package android:name="com.instagram.android" />
        <package android:name="com.zhiliaoapp.musically" /> <!-- tiktok -->
        <package android:name="com.snapchat.android" />
        <package android:name="com.twitter.android" />
        <package android:name="com.whatsapp" />
    </queries>

    <application
        android:name=".BeRealApplication"
        android:allowBackup="false"
        android:configChanges="colorMode|density|fontScale|keyboard|keyboardHidden|layoutDirection|locale|mcc|mnc|navigation|orientation|screenLayout|screenSize|smallestScreenSize|touchscreen|uiMode"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.BeReal.NoActionBar">
        <profileable
            android:shell="true"
            tools:targetApi="S" />

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.BeReal.NoActionBar"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="@string/deeplink_internal_scheme" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <category android:name="android.intent.category.BROWSABLE" />

                <!-- music -->
                <data
                    android:scheme="bere.al"
                    android:path="/spotify-login-callback"
                    />
                <!-- music -->
                <data
                    android:scheme="@string/deeplink_internal_scheme"
                    android:path="/spotify-login-callback"
                    />
            </intent-filter>

            <!-- Deeplinks for user profiles -->
            <intent-filter android:autoVerify="true" tools:ignore="AppLinkUrlError" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="@string/deeplink_external_scheme_lowercase"
                    android:pathAdvancedPattern="/[^/]*"
                    android:scheme="http" />
                <data
                    android:host="@string/deeplink_external_scheme_lowercase"
                    android:pathAdvancedPattern="/[^/]*"
                    android:scheme="https" />
                <data
                    android:host="@string/deeplink_external_scheme"
                    android:pathAdvancedPattern="/[^/]*"
                    android:scheme="http"
                    />
                <data
                    android:host="@string/deeplink_external_scheme"
                    android:pathAdvancedPattern="/[^/]*"
                    android:scheme="https" />
            </intent-filter>

            <!-- Adjust Universal Links -->
            <intent-filter android:autoVerify="true" tools:ignore="AppLinkUrlError" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="bereal.go.link"
                    android:scheme="http" />
                <data
                    android:host="bereal.go.link"
                    android:scheme="https" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/notification_icon" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="${MAPS_API_KEY}" />

        <service
            android:name=".push.BeRealMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <receiver
            android:name=".notification.data.NotificationPluginEventReceiverImpl"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="android:resource" />
        </provider>

    </application>

</manifest>
