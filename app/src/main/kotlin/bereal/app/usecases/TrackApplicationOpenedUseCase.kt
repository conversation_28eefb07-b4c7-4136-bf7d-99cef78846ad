package bereal.app.usecases

import android.content.Intent
import bereal.app.analytics.AEvent
import bereal.app.analytics.AnalyticsManager
import bereal.app.notification.ui.processor.CreateNotificationContentIntentProcessor
import org.koin.core.annotation.Factory

@Factory
class TrackApplicationOpenedUseCase(
    private val analyticsManager: AnalyticsManager,
) {
    operator fun invoke(intent: Intent?) {
        val notificationTypeBackendIdentifier = getNotificationTypeBackendIdentifier(intent)
        val deeplink = getDeeplink(intent)

        val appOpenedEvent = if (notificationTypeBackendIdentifier != null) {
            // we opened the app from a notification, then we should have received a "notificationType" from the backend in the payload
            AEvent.ApplicationOpened(
                origin = "notification",
                notificationType = notificationTypeBackendIdentifier,
                deeplink = null,
            )
        } else if (deeplink != null) {
            // we opened the app from a deeplink
            AEvent.ApplicationOpened(
                origin = "deeplink",
                notificationType = null,
                deeplink = deeplink,
            )
        } else {
            // else we assume we opened from the appIcon
            AEvent.ApplicationOpened(
                origin = "appIcon",
                notificationType = null,
                deeplink = null,
            )
        }

        analyticsManager.logEvent(appOpenedEvent)
    }

    private fun getNotificationTypeBackendIdentifier(intent: Intent?) =
        intent?.getStringExtra(CreateNotificationContentIntentProcessor.EXTRA_KEY_notificationTypeBackendIdentifier)

    private fun getDeeplink(intent: Intent?): String? {
        val uri = intent?.data ?: return null
        val isHttp = uri.scheme?.contains("http") ?: false
        return if (isHttp) {
            // remove the https://bereal.*/
            uri.toString().substringAfter("://").substringAfter("/")
        } else {
            // suppose it's bereal://, remove what's before the ://
            uri.toString().substringAfter("://") // extract the prefix
        }
    }
}
