package bereal.app.usecases

import bereal.app.common.combines
import bereal.app.features.onboarding.notificationpermission.domain.ObserveIsNotificationPermissionEnabled
import bereal.app.permissions.PermissionManager
import bereal.app.permissions.model.BeRealPermission
import bereal.app.terms.domain.model.TermTypeDomainModel
import bereal.app.terms.domain.usecases.UpdateTermUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import org.koin.core.annotation.Factory

/**
 * Listen to accepted permissions and update the Term repository
 *
 * can write : Term.notification = true / false
 */
@Factory
class BindUserPermissionsWithTermsUseCase(
    private val updateTermsUseCase: UpdateTermUseCase,
    private val permissionManager: PermissionManager,
    private val observeIsNotificationPermissionEnabled: ObserveIsNotificationPermissionEnabled,
) {
    operator fun invoke(): Flow<Unit> = combines(
        permissionManager.isPermissionAccepted(BeRealPermission.ExternalStorage.Write)
            .distinctUntilChanged()
            .onEach { status ->
                updateTermsUseCase(
                    type = TermTypeDomainModel.fileWrite,
                    accepted = status.accepted,
                )
            },
        observeIsNotificationPermissionEnabled()
            .distinctUntilChanged()
            .onEach { accepted ->
                updateTermsUseCase(
                    type = TermTypeDomainModel.pushNotifications,
                    accepted = accepted,
                )
            },
    ).map { /* expose nothing */ }
}
