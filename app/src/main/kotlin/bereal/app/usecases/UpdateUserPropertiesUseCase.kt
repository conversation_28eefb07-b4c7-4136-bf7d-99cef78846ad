package bereal.app.usecases

import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.model.AnalyticsUserPropertyType
import bereal.app.common.combines
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.domain.refresh.RefreshUseCase
import bereal.app.entities.DeviceId
import bereal.app.entities.RealMojiType
import bereal.app.features.feed.domain.common.usecases.ObserveIsPublicProfileByCheckingNumberOfPublicPostUseCase
import bereal.app.features.friendrecommendations.domain.usecases.ObserveFriendRecommendationsSettingsUseCase
import bereal.app.features.official.accounts.common.domain.ObserveAllFollowingOfficialAccountIdsUseCase
import bereal.app.features.onboarding.notificationpermission.domain.ObserveIsNotificationPermissionEnabled
import bereal.app.features.relationship.domain.usecases.ObserveReceivedPendingFriendRequestsUseCase
import bereal.app.friending.usecases.ObserveFriendsUseCase
import bereal.app.friending.usecases.publics.ObserveMyFollowersUseCase
import bereal.app.notification.domain.models.NotificationSettingsDomainModel
import bereal.app.notification.domain.usecases.NotificationSettingsObserveUseCase
import bereal.app.post.usecases.ObserveHasUserPostedUseCase
import bereal.app.profile.domain.v2.myprofile.usecases.pin.ObserveMyPinnedPostsUseCase
import bereal.app.realmoji.domain.myuser.ObserveMyUserCreatedRealmojisUseCase
import bereal.app.settings.ui.subscreens.notifications.domain.usecases.SettingsNotificationsAnalyticsUseCase
import bereal.app.settings.usecases.ObserveIsFreeRideReadEnableUseCase
import bereal.app.settings.usecases.ObserveIsFreeRideWriteEnableUseCase
import bereal.app.video.domain.usecase.ObserveIsAudioPermissionEnableUseCase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory

private const val USER_PROPERTY_NOTIFICATIONS_AUTHORIZED = "authorized"
private const val USER_PROPERTY_NOTIFICATIONS_DENIED = "denied"

@Factory
class UpdateUserPropertiesUseCase(
    private val analyticsManager: AnalyticsManager,
    private val dispatcherProvider: DispatcherProvider,
    private val observeFriendRecommendationsSettingsUseCase: ObserveFriendRecommendationsSettingsUseCase,
    private val observeMyPinnedPostsUseCase: ObserveMyPinnedPostsUseCase,
    private val observeNotificationSettingsUseCase: NotificationSettingsObserveUseCase,
    private val observeFriendsUseCase: ObserveFriendsUseCase,
    private val refreshUseCase: RefreshUseCase,
    private val observeIsFreeRideReadEnableUseCase: ObserveIsFreeRideReadEnableUseCase,
    private val observeIsFreeRideWriteEnableUseCase: ObserveIsFreeRideWriteEnableUseCase,
    private val observeHasUserPostedUseCase: ObserveHasUserPostedUseCase,
    private val observeIsAudioPermissionEnabledUseCase: ObserveIsAudioPermissionEnableUseCase,
    private val observeAllFollowingOfficialAccountIdsUseCase: ObserveAllFollowingOfficialAccountIdsUseCase,
    private val observeMyUserCreatedRealmojisUseCase: ObserveMyUserCreatedRealmojisUseCase,
    private val deviceId: DeviceId,
    private val observeMyFollowersUseCase: ObserveMyFollowersUseCase,
    private val observeIsPublicProfileByCheckingNumberOfPublicPostUseCase: ObserveIsPublicProfileByCheckingNumberOfPublicPostUseCase,
    private val observeReceivedPendingFriendRequestsUseCase: ObserveReceivedPendingFriendRequestsUseCase,
    private val obersveMyUserStatsUseCase: bereal.app.profile.domain.v2.myprofile.usecases.stats.ObserveMyUserStatsUseCase,
    private val observeIsNotificationPermissionEnabled: ObserveIsNotificationPermissionEnabled,
) {
    operator fun invoke(scope: CoroutineScope) {
        updateDeviceId(scope)
        observeFriendRecommendationsSettings(scope = scope)
        observeMyPinnedMemories(scope = scope)
        observeNotificationSettings(scope = scope)
        observeNotificationPermission(scope = scope)
        observeFriends(scope = scope)
        observePendingFriendRequest(scope = scope)
        observeFreeRide(scope = scope)
        observeMyPost(scope = scope)
        observeIsAudioPermissionEnable(scope = scope)
        observeAllFollowingOfficialAccount(scope = scope)
        observeMyUserCreatedRealmojis(scope = scope)
        observePublicProfile(scope = scope)
        observeUserStats(scope = scope)
    }

    private fun observeUserStats(scope: CoroutineScope) {
        scope.launch {
            obersveMyUserStatsUseCase()
                .filterNotNull()
                .onEach { userStats ->
                    analyticsManager.updateUserProperty(
                        mapOf(
                            AnalyticsUserPropertyType.PublicBeRealsCount.value to userStats.numberOfPublicPosts,
                        ),
                    )
                }
                .flowOn(dispatcherProvider.viewmodel)
                .launchIn(scope)
        }
    }

    private fun observePublicProfile(scope: CoroutineScope) {
        scope.launch {
            combines(
                observeMyFollowersUseCase(),
                observeIsPublicProfileByCheckingNumberOfPublicPostUseCase(),
            ).onEach { (myFollowers, isPublicProfile) ->
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.FollowerCount.value to myFollowers.total,
                        AnalyticsUserPropertyType.IsPublicProfile.value to isPublicProfile,
                    ),
                )
            }
                .flowOn(dispatcherProvider.viewmodel)
                .launchIn(scope)
        }
    }

    private fun observeMyUserCreatedRealmojis(scope: CoroutineScope) {
        scope.launch {
            observeMyUserCreatedRealmojisUseCase().onEach { realmojis: Map<RealMojiType, String> ->
                val basicEmojiCount: Int = realmojis.filter { realmoji: Map.Entry<RealMojiType, String> ->
                    (
                        realmoji.key == RealMojiType.HeartEyes ||
                            realmoji.key == RealMojiType.Up ||
                            realmoji.key == RealMojiType.Surprised ||
                            realmoji.key == RealMojiType.Laughing ||
                            realmoji.key == RealMojiType.Happy
                        ) && realmoji.value.isNotEmpty()
                }.count()
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.RealmojiHeart.value to realmojis[RealMojiType.HeartEyes].isNullOrBlank().not(),
                        AnalyticsUserPropertyType.RealmojiThumbsUp.value to realmojis[RealMojiType.Up].isNullOrBlank().not(),
                        AnalyticsUserPropertyType.RealmojiWow.value to realmojis[RealMojiType.Surprised].isNullOrBlank().not(),
                        AnalyticsUserPropertyType.RealmojiLaugh.value to realmojis[RealMojiType.Laughing].isNullOrBlank().not(),
                        AnalyticsUserPropertyType.RealmojiSmile.value to realmojis[RealMojiType.Happy].isNullOrBlank().not(),
                        AnalyticsUserPropertyType.RealmojiCreatedNumber.value to basicEmojiCount,
                    ),
                )
            }
                .flowOn(dispatcherProvider.viewmodel)
                .launchIn(scope)
        }
    }

    private fun updateDeviceId(scope: CoroutineScope) {
        scope.launch {
            analyticsManager.updateUserProperty(
                mapOf(
                    AnalyticsUserPropertyType.InternalDeviceID.value to deviceId.value,
                ),
            )
        }
    }

    private fun observeIsAudioPermissionEnable(scope: CoroutineScope) {
        observeIsAudioPermissionEnabledUseCase().onEach { isAudioPermissionEnabled ->
            val value = if (isAudioPermissionEnabled) {
                "ACCEPTED"
            } else {
                "DECLINED"
            }
            analyticsManager.updateUserProperty(
                mapOf(
                    AnalyticsUserPropertyType.TermsMicrophone.value to value,
                ),
            )
        }.flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeFreeRide(scope: CoroutineScope) {
        observeIsFreeRideReadEnableUseCase()
            .combine(observeIsFreeRideWriteEnableUseCase(), ::Pair)
            .onEach { (read, write) ->
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.FreerideOneRead.value to read,
                        AnalyticsUserPropertyType.FreerideOneWrite.value to write,
                    ),
                )
            }.flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeFriendRecommendationsSettings(scope: CoroutineScope) {
        observeFriendRecommendationsSettingsUseCase()
            .onEach {
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.PrivacySettingsHideMyPhoneNumber.value to it.hidePhoneNumber,
                        AnalyticsUserPropertyType.PrivacySettingsDisableContactsSyncing.value to it.noContactsSharing,
                    ),
                )
            }.flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeMyPinnedMemories(scope: CoroutineScope) {
        observeMyPinnedPostsUseCase().map { it.size }
            .onEach {
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.PinnedMemoryCount.value to it,
                    ),
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeNotificationPermission(scope: CoroutineScope) {
        observeIsNotificationPermissionEnabled()
            .distinctUntilChanged()
            .map { enabled ->
                if (enabled)
                    USER_PROPERTY_NOTIFICATIONS_AUTHORIZED
                else
                    USER_PROPERTY_NOTIFICATIONS_DENIED
            }
            .onEach {
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.NotificationStatus.value to it,
                    ),
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeNotificationSettings(scope: CoroutineScope) {
        observeNotificationSettingsUseCase()
            .onEach {
                // if the user never went to the settings/notif page, we are receiving null from the local data source
                // but everything is enabled by default, here we can just fallback to a version with everything true
                val notifSettings = it ?: NotificationSettingsDomainModel(
                    mentions = true,
                    comments = true,
                    friendRequest = true,
                    recap = true,
                    realMoji = true,
                    oaNewPost = true,
                    oaRealFan = true,
                    streakReminders = true,
                    memories = true,
                    newFeatures = true,
                    events = true,
                    community = true,
                    newFollower = true,
                )
                val enabledNotifs = buildList {
                    if (notifSettings.mentions == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_MENTIONS)
                    }
                    if (notifSettings.comments == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_COMMENTS)
                    }
                    if (notifSettings.friendRequest == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_FRIEND_REQUEST)
                    }
                    if (notifSettings.recap == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_RECAP)
                    }
                    if (notifSettings.realMoji == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_REAL_MOJI)
                    }
                    if (notifSettings.oaNewPost == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_OA_NEW_POST)
                    }
                    if (notifSettings.oaRealFan == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_OA_REALFAN)
                    }
                    if (notifSettings.streakReminders == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_STREAK_REMINDERS)
                    }
                    if (notifSettings.newFeatures == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_NEW_FEATURE)
                    }
                    if (notifSettings.memories == true) {
                        add(SettingsNotificationsAnalyticsUseCase.VALUE_MEMORIES)
                    }
                }
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.NotificationSettings.value to enabledNotifs,
                    ),
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeFriends(scope: CoroutineScope) {
        observeFriendsUseCase()
            .drop(1)
            .distinctUntilChanged()
            .onEach {
                analyticsManager.updateUserProperty(
                    mapOf(AnalyticsUserPropertyType.FriendsCount.value to it.size),
                )
                // Force refreshing setting when friends changes to refresh chat data.
                refreshUseCase(
                    settingsRefreshType = RefreshUseCase.RefreshType.ForceRefresh,
                    profileRefreshType = RefreshUseCase.RefreshType.None,
                    friendRequestsRefreshType = RefreshUseCase.RefreshType.None,
                    friendsRefreshType = RefreshUseCase.RefreshType.None,
                    termsRefreshType = RefreshUseCase.RefreshType.None,
                    notificationsRefreshType = RefreshUseCase.RefreshType.None,
                    regionsRefreshType = RefreshUseCase.RefreshType.None,
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observePendingFriendRequest(scope: CoroutineScope) {
        observeReceivedPendingFriendRequestsUseCase()
            .distinctUntilChanged()
            .onEach {
                analyticsManager.updateUserProperty(
                    mapOf(AnalyticsUserPropertyType.FriendsPendingRequestCount.value to it.size),
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeMyPost(scope: CoroutineScope) {
        observeHasUserPostedUseCase()
            .distinctUntilChanged()
            .onEach {
                analyticsManager.updateUserProperty(
                    mapOf(
                        AnalyticsUserPropertyType.HasCurrentPost.value to it,
                    ),
                )
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }

    private fun observeAllFollowingOfficialAccount(scope: CoroutineScope) {
        observeAllFollowingOfficialAccountIdsUseCase().distinctUntilChanged().onEach { officialAccountIds ->
            analyticsManager.updateUserProperty(
                mapOf(
                    AnalyticsUserPropertyType.CurrentOaAdds.value to officialAccountIds.map { it.userId },
                ),
            )
        }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(scope)
    }
}
