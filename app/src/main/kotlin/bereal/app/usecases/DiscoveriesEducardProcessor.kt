package bereal.app.usecases

import bereal.app.analytics.AEvent
import bereal.app.analytics.AnalyticsManager
import bereal.app.commonandroid.BeRealStrings
import bereal.app.commonandroid.StringProvider
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.model.ActionUrlState
import bereal.app.inappmessaging.data.InAppMessagingRepository
import bereal.app.inappmessaging.model.InAppMessageScreen
import bereal.app.inappmessaging.model.InAppMessageTag
import bereal.app.inappmessaging.model.NOT_A_CAMPAIGN
import bereal.app.inappmessaging.ui.mapper.resId
import bereal.app.inappmessaging.ui.model.InAppMessageListener
import bereal.app.inappmessaging.ui.model.InAppMessageUiModel
import bereal.app.navigation.model.direction.MainScreenDirection
import bereal.app.navigation.scheme.SchemeProvider
import org.koin.core.annotation.Factory

@Factory
class DiscoveriesEducardProcessor(
    private val inAppMessagingRepository: InAppMessagingRepository,
    private val schemeProvider: SchemeProvider,
    private val stringProvider: StringProvider,
    private val analyticsManager: AnalyticsManager,
) {

    fun showDiscoveriesEducard() {
        val discoveriesEducardMessage = InAppMessageUiModel(
            title = stringProvider[BeRealStrings.discoveries_educard_title],
            body = stringProvider[BeRealStrings.discoveries_educard_subtitle],
            image = ImageDataModel.Url("https://cdn-us1.bereal.network/assets/nearby-educard.jpg"),
            tag = stringProvider[resId(InAppMessageTag.UPDATE)],
            isBlocking = false,
            screen = InAppMessageScreen.Home,
            action = ActionUrlState(
                name = stringProvider[BeRealStrings.explore_educard_button],
                url = "${schemeProvider.provideInternal()}://${MainScreenDirection.routePrefix}?${MainScreenDirection.KEY_TAB}=discoveries",
            ),
            listener = InAppMessageListener(
                onDisplay = ::onNearbyEducardDisplay,
            ),
            campaign = "internal-discoveries-$NOT_A_CAMPAIGN",
        )
        inAppMessagingRepository.displayMessages(messages = listOf(discoveriesEducardMessage))
    }

    private fun onNearbyEducardDisplay() {
        analyticsManager.logEvent(AEvent.ViewedDiscoveriesEducard)
    }
}
