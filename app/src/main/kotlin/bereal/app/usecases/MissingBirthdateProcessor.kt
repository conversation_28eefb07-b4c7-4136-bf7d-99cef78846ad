package bereal.app.usecases

import bereal.app.analytics.AEvent
import bereal.app.analytics.AnalyticsManager
import bereal.app.commonandroid.BeRealStrings
import bereal.app.commonandroid.StringProvider
import bereal.app.deeplinks.ui.model.NavigationDeeplink
import bereal.app.design.image.model.options.ImageDataModel
import bereal.app.design.model.ActionUrlState
import bereal.app.entities.User
import bereal.app.entities.UserBirthdate
import bereal.app.entities.UserType
import bereal.app.inappmessaging.data.InAppMessagingRepository
import bereal.app.inappmessaging.model.NOT_A_CAMPAIGN
import bereal.app.inappmessaging.ui.model.InAppMessageListener
import bereal.app.inappmessaging.ui.model.InAppMessageUiModel
import bereal.app.navigation.scheme.SchemeProvider
import org.koin.core.annotation.Factory

@Factory
class MissingBirthdateProcessor(
    private val inAppMessagingRepository: InAppMessagingRepository,
    private val stringProvider: StringProvider,
    private val analyticsManager: AnalyticsManager,
    private val schemeProvider: SchemeProvider,
) {

    fun checkUserBirthdate(myUser: User) {
        if (
            myUser.birthdate is UserBirthdate.Set ||
            myUser.birthdate is UserBirthdate.Unknown ||
            myUser.type is UserType.OfficialAccount
        ) return

        displayInappMissingBirthdate()
    }

    private fun displayInappMissingBirthdate() {
        val missingBirthdateInAppMessage = InAppMessageUiModel(
            title = stringProvider[BeRealStrings.date_of_birth_missing_add_your_date_of_birth_title],
            body = stringProvider[BeRealStrings.add_your_date_of_birth_body],
            image = ImageDataModel.DrawableRes(bereal.app.design.R.drawable.warning_dob),
            tag = stringProvider[BeRealStrings.inapp_messaging_tag_warning],
            isBlocking = true,
            action = ActionUrlState(
                name = stringProvider[BeRealStrings.date_of_birth_missing_add_your_date_of_birth_button],
                url = "${schemeProvider.provideInternal()}://${NavigationDeeplink.MissingBirthdate.path}",
            ),
            listener = InAppMessageListener(
                onDisplay = this::onMissingBirthdayClicked,
            ),
            campaign = "internal-user-birthday-$NOT_A_CAMPAIGN",
        )
        inAppMessagingRepository.displayMessages(messages = listOf(missingBirthdateInAppMessage))
    }

    private fun onMissingBirthdayClicked() {
        analyticsManager.logEvent(AEvent.ViewedMissingBirthdayModal)
    }
}
