package bereal.app

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Bundle
import android.os.TransactionTooLargeException
import android.view.MotionEvent
import androidx.activity.SystemBarStyle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.CompositionLocalProvider
import androidx.core.view.WindowCompat
import androidx.fragment.app.FragmentActivity
import bereal.app.bts.ui.player.mute.DeviceVolumeManager
import bereal.app.commonandroid.CurrentActivity
import bereal.app.debug.mode.DebugMenu
import bereal.app.dialog.PlayServices
import bereal.app.features.ads.domain.usecases.adn.InitAdnSdkUseCase
import bereal.app.features.ads.ui.arbitrageur.AdSetupViewModel
import bereal.app.features.ads.ui.privacy.VoodooPrivacyManagerFactory
import bereal.app.features.antibot.domain.InitAntibotUseCase
import bereal.app.shakebugs.ui.ShakeInitializer
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.ui.BeRealApp
import bereal.app.ui.MainActivityViewModel
import bereal.app.update.BeRealInAppUpdateManager
import io.voodoo.apps.privacy.CurrentVoodooPrivacyManager
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

class MainActivity : FragmentActivity() {

    private val shakeInitializer by inject<ShakeInitializer>()

    private val mainActivityViewModel by viewModel<MainActivityViewModel>()
    private val currentActivity by inject<CurrentActivity>()

    private val berealTimeProvider by inject<BeRealTimeProvider>()

    private val debugMenu by inject<DebugMenu.Launcher>()

    private val initAntibotUseCase by inject<InitAntibotUseCase>()
    private val initAdnStandaloneAdsUseCase by inject<InitAdnSdkUseCase>()
    private val deviceVolumeListener by inject<DeviceVolumeManager>()

    private val voodooPrivacyManagerFactory by inject<VoodooPrivacyManagerFactory>()
    private val voodooConsentManager by lazy {
        voodooPrivacyManagerFactory.create(
            activity = this,
        )
    }

    private val currentVoodooConsentManager by inject<CurrentVoodooPrivacyManager>()
    private val inAppUpdateManager by inject<BeRealInAppUpdateManager>()
    private val adSetupViewModel by viewModel<AdSetupViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge(
            statusBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
            navigationBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
        ) // set the status bar & navigation bar to transparent
        super.onCreate(savedInstanceState)

        shakeInitializer.init()

        deviceVolumeListener.start()
        inAppUpdateManager.registerInAppListener()
        currentVoodooConsentManager.set(voodooConsentManager)

        // adding clear task flag to avoid navController recreating the activity when its handleDeeplink() method is called
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK

        currentActivity.set(this)
        adSetupViewModel.initialize(
            activity = this,
            lifecycle = this.lifecycle,
        )
        initAdnStandaloneAdsUseCase()

        // Turn off the decor fitting system windows, which means we need to through handling
        // insets
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            CompositionLocalProvider(
                androidx.lifecycle.compose.LocalLifecycleOwner provides androidx.compose.ui.platform.LocalLifecycleOwner.current,
            ) {
                BeRealApp()
            }
        }

        if (savedInstanceState == null) {
            // else it will be handled by onNewIntent
            // navController.handleDeeplink() is called automatically, need to check if manual routing needed
            mainActivityViewModel.handleIntent(intent = intent, fromNewIntent = false)
        }

        PlayServices.displayErrorIfNotAvailable(activity = this)
        initAntibotUseCase()
        inAppUpdateManager.checkForInAppUpdate(activity = this)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        mainActivityViewModel.handleIntent(intent = intent, fromNewIntent = true)
    }

    override fun onResume() {
        super.onResume()
        mainActivityViewModel.onResume(intent)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        try {
            super.onSaveInstanceState(outState)
        } catch (e: TransactionTooLargeException) {
            outState.clear()
            finish()
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        event?.let { debugMenu.listenTouches(event) }
        return super.onTouchEvent(event)
    }

    private val timeSettingsChangedReciever = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            berealTimeProvider.sync()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()

        applicationContext.registerReceiver(
            timeSettingsChangedReciever,
            IntentFilter().apply {
                addAction(Intent.ACTION_DATE_CHANGED)
                addAction(Intent.ACTION_TIME_CHANGED)
                addAction(Intent.ACTION_TIMEZONE_CHANGED)
            },
        )
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        runCatching {
            applicationContext.unregisterReceiver(timeSettingsChangedReciever)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        deviceVolumeListener.stop()
        inAppUpdateManager.unregisterInAppListener()
    }
}
