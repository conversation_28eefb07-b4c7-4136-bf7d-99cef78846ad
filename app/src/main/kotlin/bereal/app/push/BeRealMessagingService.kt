package bereal.app.push

import bereal.app.common.Failure
import bereal.app.notification.ui.processor.NotificationProcessor
import bereal.app.user.usecases.ComputeIsAccountCreatedAndValidFromCacheUseCase
import bereal.app.user.usecases.UpdateFCMTokenUseCase
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.runBlocking
import org.koin.android.ext.android.inject
import timber.log.Timber

class BeRealMessagingService : FirebaseMessagingService() {

    private val isAccountValidUseCase by inject<ComputeIsAccountCreatedAndValidFromCacheUseCase>()
    private val updateFCMToken by inject<UpdateFCMTokenUseCase>()
    private val notificationProcessor by inject<NotificationProcessor>()

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        runBlocking {
            if (isAccountValidUseCase() is Failure) {
                return@runBlocking
            }

            val data = remoteMessage.data
            Timber.i("NotificationReceived ${remoteMessage.data}")

            notificationProcessor(data)
        }
    }

    override fun onNewToken(token: String) {
        Timber.i("onNewToken $token")
        runBlocking {
            updateFCMToken(token)
        }
    }
}
