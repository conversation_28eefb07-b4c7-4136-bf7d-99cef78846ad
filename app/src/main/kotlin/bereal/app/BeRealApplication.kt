package bereal.app

import android.app.Application
import bereal.app.commonandroid.applogger.AppLogger
import bereal.app.design.time.TimeSpentManager
import bereal.app.di.initializeBerealDependencies
import bereal.app.features.ads.domain.AdsLogsRepository
import bereal.app.notification.ui.processor.CreateNotificationChannelsProcessor
import bereal.app.settings.usecases.experiments.FetchAmplitudeExperimentUseCase
import bereal.app.settings.usecases.experiments.InitializeAmplitudeExperimentSdkUseCase
import bereal.app.store.filestore.FileStore
import com.google.firebase.FirebaseApp
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.koin.android.ext.android.get
import org.koin.core.context.startKoin

class BeRealApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        System.loadLibrary("sqlcipher")

        startKoin {
            initializeBerealDependencies(
                application = this@BeRealApplication,
                noOp = false,
            )
        }

        FirebaseApp.initializeApp(this)
        Firebase.auth.useAppLanguage()

        get<CreateNotificationChannelsProcessor>().invoke()

        get<TimeSpentManager>().init(application = this)
        get<AdsLogsRepository>().setInfo(
            AdsLogsRepository.Info(
                type = AdsLogsRepository.Info.Type.AppStarted,
                at = System.currentTimeMillis(),
            ),
        )

        // Clean instant cache folder
        val fileStore: FileStore = get()
        fileStore.clearTemp()

        // flipper / http / Logcat logger in debug
        val appLogger: AppLogger = get()
        appLogger.start()

        initAmplitudeSDK()
    }

    private fun initAmplitudeSDK() {
        val applicationScope: CoroutineScope = get()
        val initAmplitudeExperimentSdkUseCase: InitializeAmplitudeExperimentSdkUseCase = get()
        initAmplitudeExperimentSdkUseCase()

        val fetchAmplitudeExperimentUseCase: FetchAmplitudeExperimentUseCase = get()
        applicationScope.launch {
            fetchAmplitudeExperimentUseCase()
        }
    }
}
