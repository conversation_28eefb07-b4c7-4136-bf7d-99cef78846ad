package bereal.app.ui

import androidx.annotation.VisibleForTesting
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavDestination
import bereal.app.BuildConfig
import bereal.app.analytics.AnalyticsManager
import bereal.app.analytics.model.AnalyticsCameraOrigin
import bereal.app.analytics.model.AnalyticsUserPropertyType
import bereal.app.camera.domain.camera.HasConcurrentDualCameraCapabilityUseCase
import bereal.app.common.combines
import bereal.app.common.getOrNull
import bereal.app.common.ifErrorThen
import bereal.app.common.stateIn
import bereal.app.commonandroid.CurrentActivity
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.deeplinks.ui.model.NavigationDirectionFromDeeplinkUiModel
import bereal.app.deeplinks.ui.processors.GetNavigationDirectionFromDeeplinkProcessor
import bereal.app.design.Open
import bereal.app.design.model.ActionUrlState
import bereal.app.device.usecases.GetDeviceCountryCodeUseCase
import bereal.app.device.usecases.GetDeviceInstallationIdUseCase
import bereal.app.dialog.model.AppDialogsModel
import bereal.app.domain.clear.moment.ClearEverythingOnMomentChangeUseCase
import bereal.app.domain.logout.LogoutUseCase
import bereal.app.domain.moment.OnMomentChangePerformActionUseCase
import bereal.app.domain.moment.model.OnMomentChangeAction
import bereal.app.domain.refresh.RefreshUseCase
import bereal.app.entities.DeviceId
import bereal.app.entities.User
import bereal.app.entities.UserBirthdate
import bereal.app.entities.settings.BerealSettingsEntity
import bereal.app.entities.toBasicUser
import bereal.app.features.ads.domain.analytics.AdsEvents
import bereal.app.features.ads.domain.customtargetting.RefreshAdsExtraParametersUseCase
import bereal.app.features.ads.domain.models.AdsAdjustEvent
import bereal.app.features.ads.domain.usecases.InitAdjustUseCase
import bereal.app.features.ads.domain.usecases.InitAdsUseCase
import bereal.app.features.ads.domain.usecases.ObserveIsAdsEnabledUseCase
import bereal.app.features.ads.domain.usecases.ObserveRejectConsentEnforcementExperimentUseCase
import bereal.app.features.ads.domain.usecases.SendAdjustEventEventUseCase
import bereal.app.features.ads.domain.usecases.SetRePromptLastDateUseCase
import bereal.app.features.ads.domain.usecases.adn.UpdateAdnPropertiesUseCase
import bereal.app.features.camera.domain.usecases.dual.HasSentConcurrentDualCameraCapability
import bereal.app.features.camera.domain.usecases.dual.SaveHasSentConcurrentDualCameraCapability
import bereal.app.features.contactbook.domain.usecases.UploadContactBookUseCase
import bereal.app.features.feed.domain.common.usecases.realmojis.SendRealmojisRankedChangeEventUseCase
import bereal.app.features.infeedmemories.domain.usecases.FetchInFeedMemoriesUseCase
import bereal.app.features.memories.domain.usecases.FetchMemoriesForRealUpdatesMemoriesCardUseCase
import bereal.app.features.memories.recap.domain.usecases.FetchMemoriesRecap2024UseCase
import bereal.app.features.memories.recap.domain.usecases.FetchMonthlyRecapUseCase
import bereal.app.features.multi.account.domain.usecases.FetchAllMyAccountsUseCase
import bereal.app.features.official.accounts.common.domain.AddUserToOfficialAccountDataUseCase
import bereal.app.features.official.accounts.recommendations.domain.usecases.FetchOfficialAccountsRecommendationsUseCase
import bereal.app.features.official.accounts.relationship.domain.usecases.follow.FetchAllFollowingOfficialAccountsUseCase
import bereal.app.friending.usecases.publics.FetchMyFollowRelationsUseCase
import bereal.app.inappmessaging.ListerForCampaignsToShowStateHolder
import bereal.app.inappmessaging.model.NOT_A_CAMPAIGN
import bereal.app.inappmessaging.ui.InAppMessageStateHolder
import bereal.app.inappmessaging.ui.model.InAppMessageListener
import bereal.app.inappmessaging.ui.model.InAppMessageUiModel
import bereal.app.moment.usecases.momentchanged.FetchSpecialMomentsUseCase
import bereal.app.moment.usecases.momentchanged.ObserveMomentChangeFromPollingUseCase
import bereal.app.moment.usecases.momentchanged.ObserveOnMomentChangedFromNotificationUseCase
import bereal.app.music.MusicPlayer
import bereal.app.navigation.BottomBarNavigationManager
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.CameraDirection
import bereal.app.navigation.model.direction.MainScreenDirection
import bereal.app.navigation.model.direction.ReactionsDirection
import bereal.app.navigation.model.direction.RestorePendingDeletionInAppDirection
import bereal.app.notification.domain.InAppNotification
import bereal.app.notification.domain.usecases.GetDeviceNotificationInstanceIdUseCase
import bereal.app.platform.data.core.storage.data.ClearOldEntitiesDatabaseScheduler
import bereal.app.post.usecases.CheckUnsentPostsUseCase
import bereal.app.realmoji.domain.myuser.ObserveRealmojisCountOnAnyOfMyUserPostsUseCase
import bereal.app.realtime.domain.ObserveIsRealTimeServiceEnabledUseCase
import bereal.app.remote.logger.RemoteLogger
import bereal.app.remote.logger.ext.logError
import bereal.app.rgpd.domain.NeedToDisplayRgpdUseCase
import bereal.app.rgpd.ui.model.RGPDDialog
import bereal.app.settings.model.DiscoveriesEducardShowState
import bereal.app.settings.repositories.SettingsRepository
import bereal.app.settings.usecases.InitializeRemoteLoggerUseCase
import bereal.app.settings.usecases.ObserveShouldShowDiscoveriesEducardUseCase
import bereal.app.settings.usecases.SetHasSeenDiscoveriesEducardUseCase
import bereal.app.settings.usecases.ads.ObserveTrackingSamplingExperimentUseCase
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.time.provider.currentTimeInstant
import bereal.app.translations.R
import bereal.app.usecases.BindUserPermissionsWithTermsUseCase
import bereal.app.usecases.DiscoveriesEducardProcessor
import bereal.app.usecases.MissingBirthdateProcessor
import bereal.app.usecases.UpdateUserPropertiesUseCase
import bereal.app.user.usecases.ComputeIsAccountCreatedAndValidFromCacheUseCase
import bereal.app.user.usecases.ComputeIsAccountCreatedAndValidFromRemoteUseCase
import bereal.app.user.usecases.ObserveMyUserUseCase
import bereal.app.user.usecases.UpdateFCMTokenUseCase
import bereal.app.user.usecases.UpdateProfileAndDeviceInfosUseCase
import bereal.app.user.usecases.id.OnUserIdChangedUseCase
import bereal.app.whistler.domain.usecases.CleanMessagingLocalMediaUseCase
import bereal.app.whistler.domain.usecases.ClearDirectConversationPreChatV2UseCase
import bereal.app.whistler.domain.usecases.GenerateFeedRequestIdUseCase
import bereal.app.whistler.domain.usecases.ListenForRealTimeChatMessagesAndStoreUseCase
import bereal.app.whistler.domain.usecases.RefreshFeedDataUseCase
import io.voodoo.apps.privacy.CurrentVoodooPrivacyManager
import io.voodoo.apps.privacy.VoodooPrivacyManager
import io.voodoo.apps.privacy.model.VoodooPrivacyConsent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.format
import kotlinx.datetime.format.DateTimeComponents
import net.swiftzer.semver.SemVer
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.Factory
import timber.log.Timber
import java.util.Locale

@Factory
class AppViewModelDataSources(
    val settingsRepository: SettingsRepository,
    val deviceId: DeviceId,
)

@Factory
class AppViewModelUseCases(
    val sendRealmojisRankedChangeEventUseCase: SendRealmojisRankedChangeEventUseCase,
    val refreshUseCase: RefreshUseCase,
    val inAppMessageStateHolder: InAppMessageStateHolder,
    val checkUnsentPostsUseCase: CheckUnsentPostsUseCase,
    val getNavigationDirectionFromDeeplinkProcessor: GetNavigationDirectionFromDeeplinkProcessor,
    val onUserIdChangedUseCase: OnUserIdChangedUseCase,
    val listenForInAppMessages: ListerForCampaignsToShowStateHolder,
    val fetchAllMyAccountsUseCase: FetchAllMyAccountsUseCase,
    val myUser: MyUser,
    val config: Config,
    val ads: Ads,
    val moment: MomentsUseCase,
    val featureUseCases: FeatureUseCases,
) {

    data class FeatureUseCases(
        val messaging: Messaging,
        val sunset: Sunset,
        val fetchInFeedMemoriesUseCase: FetchInFeedMemoriesUseCase,
        val missingBirthdateProcessor: MissingBirthdateProcessor,
        val observeIsRealTimeServiceEnabledUseCase: ObserveIsRealTimeServiceEnabledUseCase,
        val discoveriesUseCase: DiscoveriesUseCase,
    )

    data class DiscoveriesUseCase(
        val observeShouldShowDiscoveriesEducardUseCase: ObserveShouldShowDiscoveriesEducardUseCase,
        val discoveriesEducardProcessor: DiscoveriesEducardProcessor,
        val setHasSeenDiscoveriesEducardUseCase: SetHasSeenDiscoveriesEducardUseCase,
    )

    data class MomentsUseCase(
        val observeOnMomentChangedFromNotificationUseCase: ObserveOnMomentChangedFromNotificationUseCase,
        val observeOnMomentChangedFromPollingUseCase: ObserveMomentChangeFromPollingUseCase,
        val clearEverythingOnMomentChangeUseCase: ClearEverythingOnMomentChangeUseCase,
        val onMomentChangePerformActionUseCase: OnMomentChangePerformActionUseCase,
        val fetchNextSpecialMoments: FetchSpecialMomentsUseCase,
    )

    data class Config(
        val initializeRemoteLoggerUseCase: InitializeRemoteLoggerUseCase,
        val hasConcurrentDualCameraCapabilityUseCase: HasConcurrentDualCameraCapabilityUseCase,
        val needToDisplayRgpdUseCase: NeedToDisplayRgpdUseCase,
        val bindUserPermissionsWithTermsUseCase: BindUserPermissionsWithTermsUseCase,
        val hasSentConcurrentDualCameraCapability: HasSentConcurrentDualCameraCapability,
        val saveHasSentConcurrentDualCameraCapability: SaveHasSentConcurrentDualCameraCapability,
        val observeTrackingSamplingExperimentUseCase: ObserveTrackingSamplingExperimentUseCase,
    )

    data class MyUser(
        val observeMyUser: ObserveMyUserUseCase,
        val isAccountCreatedAndValidFromCache: ComputeIsAccountCreatedAndValidFromCacheUseCase,
        val isAccountCreatedAndValidFromRemote: ComputeIsAccountCreatedAndValidFromRemoteUseCase,
        val updateFCMToken: UpdateFCMTokenUseCase,
        val getDeviceInstallationId: GetDeviceInstallationIdUseCase,
        val getDeviceNotificationInstanceId: GetDeviceNotificationInstanceIdUseCase,
        val updateProfileInfos: UpdateProfileAndDeviceInfosUseCase,
        val getDeviceCountryCodeUseCase: GetDeviceCountryCodeUseCase,
        val updateUserPropertiesUseCase: UpdateUserPropertiesUseCase,
        val onUserIdChangedUseCase: OnUserIdChangedUseCase,
        val logoutUseCase: LogoutUseCase,
        val fetchMemoriesForRealUpdatesMemoriesCardUseCase: FetchMemoriesForRealUpdatesMemoriesCardUseCase,
        val fetchMyFollowRelationsUseCase: FetchMyFollowRelationsUseCase,
        val fetchMemoriesRecap2024UseCase: FetchMemoriesRecap2024UseCase,
        val fetchMonthlyRecapUseCase: FetchMonthlyRecapUseCase,
        val observeHasRealmojisOnAnyOfMyUserPostsUseCase: ObserveRealmojisCountOnAnyOfMyUserPostsUseCase,
        val uploadContactBookUseCase: UploadContactBookUseCase,
    )

    data class Messaging(
        val listenForRealTimeChatMessagesAndStoreUseCase: ListenForRealTimeChatMessagesAndStoreUseCase,
        val cleanMessagingLocalMediaUseCase: CleanMessagingLocalMediaUseCase,
        val refreshFeedDataUseCase: RefreshFeedDataUseCase,
        val generateFeedRequestIdUseCase: GenerateFeedRequestIdUseCase,
        val clearDirectConversationPreChatV2UseCase: ClearDirectConversationPreChatV2UseCase,
    )

    data class Sunset(
        val fetchOfficialAccountsRecommendationsUseCase: FetchOfficialAccountsRecommendationsUseCase,
        val addUserToOfficialAccountDatas: AddUserToOfficialAccountDataUseCase,
        val fetchAllFollowingOfficialAccountsUseCase: FetchAllFollowingOfficialAccountsUseCase,
    )

    data class Ads(
        val observeIsAdsEnabledUseCase: ObserveIsAdsEnabledUseCase,
        val refreshAdsExtraParametersUseCase: RefreshAdsExtraParametersUseCase,
        val initAdsUseCase: InitAdsUseCase,
        val initAdjustUseCase: InitAdjustUseCase,
        val setRePromptLastDateUseCase: SetRePromptLastDateUseCase,
        val sendAdjustEventEventUseCase: SendAdjustEventEventUseCase,
        val observeRejectConsentEnforcementExperimentUseCase: ObserveRejectConsentEnforcementExperimentUseCase,

        // adn
        val updateAdnProperties: UpdateAdnPropertiesUseCase,
    )
}

@OptIn(ExperimentalCoroutinesApi::class)
@KoinViewModel
class AppViewModel(
    private val dispatcherProvider: DispatcherProvider,
    private val dataSources: AppViewModelDataSources,
    val navManager: NavigationManager,
    private val remoteLogger: RemoteLogger,
    private val useCases: AppViewModelUseCases,
    private val stringProvider: StringProvider,
    private val currentActivity: CurrentActivity,
    private val analyticsManager: AnalyticsManager,
    private val currentVoodooPrivacyManager: CurrentVoodooPrivacyManager,
    private val clearOldEntitiesDatabaseScheduler: ClearOldEntitiesDatabaseScheduler,
    private val beRealTimeProvider: BeRealTimeProvider,
    private val musicPlayer: MusicPlayer,
    private val gdprDelegate: GdprDelegate,
    bottomBarNavigationManager: BottomBarNavigationManager,
) : ViewModel(CoroutineScope(dispatcherProvider.viewmodel + SupervisorJob())) {

    private var initWhenAuthDone = false

    private val _showAppDialog = MutableStateFlow<AppDialogsModel?>(null)
    val showAppDialog = _showAppDialog.asStateFlow()

    val inAppMessageToDisplay: StateFlow<InAppMessageUiModel?> = combines(
        navManager.currentDestination,
        bottomBarNavigationManager.currentPage,
        useCases.inAppMessageStateHolder.inappMessagesToDisplay.map {
            it.mapNotNull {
                it as? InAppMessageUiModel
            }
        },
    )
        .distinctUntilChanged()
        .map { (route, mainScreenTab, messages) ->
            messages.firstOrNull { message ->
                useCases.inAppMessageStateHolder.canDisplayMessage(
                    message = message,
                    route = route,
                    mainScreenTab = mainScreenTab,
                )
            }
        }
        .distinctUntilChanged()
        .stateIn(viewModelScope, null)

    init {
        observeMyUser()
        startAppObservations()
        logHasConcurrentDualCameraCapability()
        remoteLogger.setDeviceId(dataSources.deviceId.value)
        useCases.myUser.updateUserPropertiesUseCase(viewModelScope)
        initAds()
        bindMusicPlayerToLifeCycle()
        observeReceivedRealmoji()
        fetchNextsSpecialMoments()
        checkRealmojiRankChanges()
    }

    private fun checkRealmojiRankChanges() {
        viewModelScope.launch {
            useCases.sendRealmojisRankedChangeEventUseCase()
        }
    }

    private fun bindMusicPlayerToLifeCycle() {
        viewModelScope.launch {
            navManager.currentDestination.collect { backStackEntry ->
                val currentMusicPlayerId = musicPlayer.currentPlayerId.first()
                if (backStackEntry?.route != ReactionsDirection.route) {
                    musicPlayer.stop()
                } else if (backStackEntry.arguments[ReactionsDirection.KEY_POST_ID].toString() != currentMusicPlayerId) {
                    musicPlayer.stop()
                }
            }
        }
    }

    val navEventQueues = navManager.navEventQueues

    private fun observeMyUser() {
        useCases.myUser.observeMyUser()
            .flowOn(dispatcherProvider.viewmodel)
            .filter { it.accountDeleteScheduledAt == null }
            .distinctUntilChanged()
            .onEach(::onUserChanged)
            .distinctUntilChangedBy { it.uid }
            .onEach(::onUserLoaded)
            .launchIn(viewModelScope)

        useCases.myUser.observeMyUser()
            .filter { it.accountDeleteScheduledAt == null }
            .map { it.uid }
            .distinctUntilChanged()
            .onEach {
                useCases.onUserIdChangedUseCase(it, initExperimentSdkSynchronously = false)
                onUserIdChanged()
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(viewModelScope)

        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.myUser.isAccountCreatedAndValidFromCache()
                .ifErrorThen {
                    useCases.myUser.isAccountCreatedAndValidFromRemote()
                }
                .alsoSuccess {
                    initWhenAuthDone = true
                    launch(dispatcherProvider.viewmodel) {
                        updateUserToken()
                    }
                    launch(dispatcherProvider.viewmodel) {
                        useCases.refreshUseCase(settingsRefreshType = RefreshUseCase.RefreshType.ForceRefresh)
                    }

                    useCases.checkUnsentPostsUseCase()
                    clearOldEntitiesDatabaseScheduler.clear()
                }
        }
    }

    private fun startAppObservations() {
        dataSources.settingsRepository.settings
            .filterNotNull() // wait for config fetched from server
            .flowOn(dispatcherProvider.viewmodel)
            .onEach { config ->
                displayUpdateAppDialog(config)
                analyticsManager.updateUserProperty(
                    buildMap {
                        put(
                            AnalyticsUserPropertyType.FeatureFlagFoF.value,
                            config.featureFlags.friendsOfFriendsFeed,
                        )
                        put(
                            AnalyticsUserPropertyType.FeatureFlagMusic.value,
                            config.featureFlags.music,
                        )
                        put(
                            AnalyticsUserPropertyType.DelhiBTSRead.value,
                            config.featureFlags.btsDelhiRead,
                        )
                        put(
                            AnalyticsUserPropertyType.DelhiBTSWrite.value,
                            config.featureFlags.btsDelhiWrite,
                        )
                    },
                )
            }
            .launchIn(viewModelScope)

        logHasConcurrentDualCameraCapability()

        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.config.initializeRemoteLoggerUseCase()
        }

        useCases.config.bindUserPermissionsWithTermsUseCase()
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(viewModelScope)
    }

    private fun displayUpdateAppDialog(config: BerealSettingsEntity) {
        var hasDisplayedMandatoryDialog = false
        config.androidMandatoryVersion?.let {
            try {
                val currentVersion = SemVer.parse(BuildConfig.VERSION_NAME)
                val minVersion = SemVer.parse(it)

                if (currentVersion < minVersion) {
                    val updateInAppMessage = buildUpdateInAppMessage(mandatory = true)
                    useCases.inAppMessageStateHolder.displayMessages(
                        messages = listOf(
                            updateInAppMessage,
                        ),
                    )
                    hasDisplayedMandatoryDialog = true
                }
            } catch (t: Throwable) {
                remoteLogger.logError(TAG, "compare androidMandatoryVersion", t)
            }
        }

        if (!hasDisplayedMandatoryDialog) {
            config.androidRecommendedVersion?.let {
                try {
                    val currentVersion = SemVer.parse(BuildConfig.VERSION_NAME)
                    val minVersion = SemVer.parse(it)

                    if (currentVersion < minVersion) {
                        val updateInAppMessage = buildUpdateInAppMessage(mandatory = false)
                        useCases.inAppMessageStateHolder.displayMessages(
                            messages = listOf(
                                updateInAppMessage,
                            ),
                        )
                    }
                } catch (t: Throwable) {
                    remoteLogger.logError(TAG, "compare androidRecommendedVersion", t)
                }
            }
        }
    }

    suspend fun onAppStart() {
        merge(
            useCases.moment.observeOnMomentChangedFromNotificationUseCase(),
            useCases.moment.observeOnMomentChangedFromPollingUseCase(),
        )
            .filterNotNull()
            .distinctUntilChangedBy { it.id }
            .flowOn(dispatcherProvider.viewmodel)
            .collect { newMoment ->
                // always clear local repos on each moment changed
                useCases.moment.clearEverythingOnMomentChangeUseCase(newMoment)

                // then see if we need to open the camera
                when (
                    val actionToPerform =
                        useCases.moment.onMomentChangePerformActionUseCase(newMoment)
                ) {
                    is OnMomentChangeAction.Nothing -> {}
                    is OnMomentChangeAction.LaunchCamera -> {
                        navManager.navigate(
                            CameraDirection.Moment.direction(
                                expirationDateMs = actionToPerform.expirationDate.toEpochMilliseconds(),
                                origin = when (actionToPerform.source) {
                                    OnMomentChangeAction.LaunchCamera.NewMomentSource.Notification -> AnalyticsCameraOrigin.newMomentNotification.name
                                    OnMomentChangeAction.LaunchCamera.NewMomentSource.Polling -> AnalyticsCameraOrigin.inAppDuringMoment.name
                                },
                            ),
                        )
                    }
                }
            }
    }

    fun appInForeground() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.refreshUseCase(
                settingsRefreshType = RefreshUseCase.RefreshType.IfEmptyOrOutDated,
                profileRefreshType = RefreshUseCase.RefreshType.ForceRefresh,
                friendRequestsRefreshType = RefreshUseCase.RefreshType.None,
                friendsRefreshType = RefreshUseCase.RefreshType.None,
                termsRefreshType = RefreshUseCase.RefreshType.None,
                notificationsRefreshType = RefreshUseCase.RefreshType.None,
                regionsRefreshType = RefreshUseCase.RefreshType.None,
            )
        }
    }

    fun appInBackground() {
        // no op
    }

    fun onboardingDone() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            navManager.navigateAsRoot(
                MainScreenDirection.direction(
                    fromAnalyticsViewValue = null,
                ),
            )

            launch(dispatcherProvider.viewmodel) {
                useCases.myUser.updateProfileInfos()
                initWhenAuthDone = true
                updateUserToken()
            }
        }
    }

    fun navigateBack() {
        navManager.pop()
    }

    private fun logHasConcurrentDualCameraCapability() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            if (!useCases.config.hasSentConcurrentDualCameraCapability()) {
                useCases.config.hasConcurrentDualCameraCapabilityUseCase()
                    .also { hasConcurrentDualCameraCapability ->
                        analyticsManager.updateUserProperty(
                            buildMap {
                                put(
                                    AnalyticsUserPropertyType.HasConcurrentDualCameraCapability.value,
                                    hasConcurrentDualCameraCapability,
                                )
                            },
                        )
                        useCases.config.saveHasSentConcurrentDualCameraCapability()
                    }
            }
        }
    }

    // region User session

    // careful, this can be called several times (e.g. when the user active date changes)
    private suspend fun onUserChanged(user: User) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.sunset.addUserToOfficialAccountDatas(user.toBasicUser())
        }

        val countryCode =
            user.countryCode ?: useCases.myUser.getDeviceCountryCodeUseCase()
        val birthDateYear = (user.birthdate as? UserBirthdate.Set)?.value?.year
        val birthDateFormatted =
            (user.birthdate as? UserBirthdate.Set)?.value?.format(LocalDate.Formats.ISO /* iso : YYYY-MM-DD */)

        val deviceInstanceId = useCases.myUser.getDeviceInstallationId().getOrNull()

        analyticsManager.updateUserProperty(
            buildMap {
                user.creationAt
                    ?.let { Instant.fromEpochMilliseconds(it) }
                    ?.format(DateTimeComponents.Formats.ISO_DATE_TIME_OFFSET)
                    ?.let {
                        put(AnalyticsUserPropertyType.SignUpDate.value, it)
                    }
                put(
                    AnalyticsUserPropertyType.AppLanguage.value,
                    stringProvider.getAnalyticsAppLanguage(),
                )
                put(AnalyticsUserPropertyType.BuildNumber.value, BuildConfig.VERSION_CODE)
                put(AnalyticsUserPropertyType.CountryCode.value, countryCode)
                put(AnalyticsUserPropertyType.BirthYear.value, birthDateYear)
                birthDateFormatted?.let {
                    put(AnalyticsUserPropertyType.BirthdateFull.value, it)
                }
                put(AnalyticsUserPropertyType.Region.value, user.region)
                put(AnalyticsUserPropertyType.Gender.value, user.gender.analyticsValue)
                put(
                    AnalyticsUserPropertyType.InstanceID.value,
                    deviceInstanceId.let {
                        it?.take(36)?.replace("-", "_")
                    },
                )
                put(
                    AnalyticsUserPropertyType.CurrentStreakCount.value,
                    "${user.streakLength ?: "none"}",
                )
                put(AnalyticsUserPropertyType.Username.value, user.userName)
                put(AnalyticsUserPropertyType.HasProfilePicture.value, user.photoUrl != null)
                put(AnalyticsUserPropertyType.HasProfileBio.value, user.biography.isNullOrBlank())
                put(
                    AnalyticsUserPropertyType.HasProfileLocation.value,
                    user.location.isNullOrBlank(),
                )
                user.lastActiveAt?.let {
                    put(AnalyticsUserPropertyType.LastActivityDatetime.value, it)
                }
            },
        )
    }

    private suspend fun onUserLoaded(user: User) {
        remoteLogger.onUser(
            userId = user.uid,
            creationAt = user.creationAt,
            language = Locale.getDefault().language,
            region = user.region,
        )
        checkMultiDeviceConnected(user)
        checkRgpd()
        checkMissingBirthdate(user)
        checkProfilePendingDeletion(user)
        whistlerInitializations()
        listenForInAppMessages()
        refreshLinkedUsers(user)
        fetchMemoriesRecap2024UseCase()
        fetchMonthlyRecapUseCase()
        showDiscoveriesEducard()
        fetchInFeedMemories()
        uploadContactBookIfNeeded()
    }

    private fun showDiscoveriesEducard() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.discoveriesUseCase.observeShouldShowDiscoveriesEducardUseCase()
                .collect { shouldShowCardState ->
                    when (shouldShowCardState) {
                        DiscoveriesEducardShowState.Show -> {
                            useCases.featureUseCases.discoveriesUseCase.discoveriesEducardProcessor.showDiscoveriesEducard()
                            useCases.featureUseCases.discoveriesUseCase.setHasSeenDiscoveriesEducardUseCase()
                        }

                        DiscoveriesEducardShowState.Hide -> {
                            /* no-op */
                        }
                    }
                }
        }
    }

    private fun fetchInFeedMemories() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.fetchInFeedMemoriesUseCase()
        }
    }

    private fun uploadContactBookIfNeeded() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.myUser.uploadContactBookUseCase()
        }
    }

    private fun onUserIdChanged() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.myUser.fetchMemoriesForRealUpdatesMemoriesCardUseCase()
            fetchAllOfficialAccountRecommendations()
            fetchAllFollowingOfficialAccounts()
            refreshAdsExtraParameters()
        }
    }

    private fun fetchAllOfficialAccountRecommendations() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.sunset.fetchOfficialAccountsRecommendationsUseCase()
        }
    }

    private fun refreshAdsExtraParameters() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            currentVoodooPrivacyManager.value?.getPrivacyConsent()?.let { privacyConsent ->
                if (privacyConsent.adConsent && !privacyConsent.doNotSellDataEnabled) {
                    useCases.ads.refreshAdsExtraParametersUseCase().collect()
                }
            }
        }
    }

    private fun fetchAllFollowingOfficialAccounts() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.sunset.fetchAllFollowingOfficialAccountsUseCase()
        }
    }

    private fun fetchNextsSpecialMoments() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.moment.fetchNextSpecialMoments()
        }
    }

    private fun listenForInAppMessages() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.listenForInAppMessages()
        }
    }

    private fun fetchMemoriesRecap2024UseCase() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            // it has a debounce inside
            useCases.myUser.fetchMemoriesRecap2024UseCase()
        }
    }

    private fun fetchMonthlyRecapUseCase() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.myUser.fetchMonthlyRecapUseCase()
        }
    }

    private fun checkProfilePendingDeletion(user: User) {
        user.accountDeleteScheduledAt?.let {
            navManager.navigate(RestorePendingDeletionInAppDirection(it))
        }
    }

    private fun checkMissingBirthdate(user: User) {
        useCases.featureUseCases.missingBirthdateProcessor.checkUserBirthdate(user)
    }

    private var initAdsJobs: Job? = null
    private var shouldTriggerGdrpPopupEvent = false
    private fun initAds() {
        if (initAdsJobs == null) {
            initTrackingSamplingExperiment()
            initAdsJobs = viewModelScope.launch(dispatcherProvider.viewmodel) {
                useCases.ads.observeIsAdsEnabledUseCase()
                    .distinctUntilChanged()
                    .mapLatest {
                        currentVoodooPrivacyManager.value?.let {
                            rejectAllConsentsIfNeeded(voodooPrivacyManager = it)
                            it.setOnStatusUpdate { consentStatus ->
                                // Can't find a better way to know if the user tap on the consent popup
                                if (!shouldTriggerGdrpPopupEvent && consentStatus == VoodooPrivacyManager.ConsentStatus.UI_SHOWN) {
                                    sendScreenViewGdprPopupAnalytics()
                                    shouldTriggerGdrpPopupEvent =
                                        consentStatus == VoodooPrivacyManager.ConsentStatus.UI_SHOWN
                                }
                            }
                            it.setOnConsentReady(::onAdsConsentReady)
                            it.initializeConsent()
                        }
                    }
                    .flowOn(dispatcherProvider.viewmodel)
                    .launchIn(viewModelScope)
            }
        }
    }

    private fun rejectAllConsentsIfNeeded(voodooPrivacyManager: VoodooPrivacyManager) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.ads.observeRejectConsentEnforcementExperimentUseCase()
                .collect { isRejectConsentExperimentEnabled ->
                    voodooPrivacyManager.rejectAllConsentsIfNeeded(isRejectConsentExperimentEnabled)
                }
        }
    }

    private fun initTrackingSamplingExperiment() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.config
                .observeTrackingSamplingExperimentUseCase()
                .collect {
                    Timber.d("TrackingSampling disabled = $it")
                }
        }
    }

    private fun initAdsInternal(
        doNotSellDataEnabled: Boolean,
        gdprConsentDescription: String?,
    ) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.ads
                .initAdsUseCase(
                    doNotSellDataEnabled = doNotSellDataEnabled,
                    gdprConsentDescription = gdprConsentDescription,
                )
                .distinctUntilChanged()
                .collect {
                    Timber.d("INIT_ADS SDK STATUS : $it")
                }
        }
    }

    fun updateCurrentRoute(route: NavDestination?) {
        navManager.updateCurrentRoute(route)
    }

    private fun sendScreenViewGdprPopupAnalytics() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            gdprDelegate.sendScreenViewGdprPopupAnalytics()
        }
    }

    private fun onAdsConsentReady(voodooPrivacyConsent: VoodooPrivacyConsent) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            if (shouldTriggerGdrpPopupEvent) {
                val currentRoute = gdprDelegate.getGDPRRoutePrefix()
                shouldTriggerGdrpPopupEvent = false
                // Send analytics after popup GDPR popup has been shown
                val buttonUserClickAdsConsent = if (voodooPrivacyConsent.adConsent) {
                    AdsEvents.ClickGdprPopup.ButtonUserClick.ads_consent_accept
                } else {
                    AdsEvents.ClickGdprPopup.ButtonUserClick.ads_consent_refuse
                }
                analyticsManager.logEvent(
                    AdsEvents.ClickGdprPopup(
                        buttonUserClicked = buttonUserClickAdsConsent,
                        placement = currentRoute,
                    ),
                )
                val buttonUserClickAnalyticsConsent = if (voodooPrivacyConsent.analyticsConsent) {
                    AdsEvents.ClickGdprPopup.ButtonUserClick.analytics_consent_accept
                } else {
                    AdsEvents.ClickGdprPopup.ButtonUserClick.analytics_consents_refuse
                }
                analyticsManager.logEvent(
                    AdsEvents.ClickGdprPopup(
                        buttonUserClicked = buttonUserClickAnalyticsConsent,
                        placement = currentRoute,
                    ),
                )

                val buttonUserClickAnalyticsDataSharingWithVoodoo = if (voodooPrivacyConsent.dataSharingWithVoodooConsentGiven) {
                    AdsEvents.ClickGdprPopup.ButtonUserClick.data_sharing_with_voodoo_accept
                } else {
                    AdsEvents.ClickGdprPopup.ButtonUserClick.data_sharing_with_voodoo_refuse
                }
                analyticsManager.logEvent(
                    AdsEvents.ClickGdprPopup(
                        buttonUserClicked = buttonUserClickAnalyticsDataSharingWithVoodoo,
                        placement = currentRoute,
                    ),
                )

                // Set date for RePrompt GDPR
                useCases.ads.setRePromptLastDateUseCase(beRealTimeProvider.currentTimeInstant)
            }

            val hasUserConsentOrNotApplicable =
                voodooPrivacyConsent.adConsent || !voodooPrivacyConsent.gdprApplicable
            val doNotSellDataEnabled = voodooPrivacyConsent.doNotSellDataEnabled

            analyticsManager.updateUserProperty(
                buildMap {
                    put(
                        AnalyticsUserPropertyType.AdsConsentGiven.value,
                        /* true if all 10 elements under personalisedAdvertising are toggled on */
                        if (voodooPrivacyConsent.gdprApplicable) {
                            // https://www.notion.so/voodoo/ADS-Consent-User-property-value-outside-EU-P2-1e7a0b481db480feb6fdff5f3e4c9cc5
                            // Outside GDPR EU zone, the consent value should always be set to true for
                            // - adsConsentGiven / storeAccessDataConsent / dataSharingWithVoodooConsentGiven
                            voodooPrivacyConsent.personalisedAdvertising
                        } else {
                            true
                        },
                    )
                    put(
                        /* "storeAccessDataConsent" */
                        AnalyticsUserPropertyType.AdsStoreAccessDataConsent.value,
                        /* 6656fcd5a0fa9305065e56a3 */
                        if (voodooPrivacyConsent.gdprApplicable) {
                            voodooPrivacyConsent.storeAccessDataConsent
                        } else {
                            true
                        },
                    )
                    put(
                        /* "dataSharingWithVoodooConsentGiven" */
                        AnalyticsUserPropertyType.AdsDataSharingWithVoodooConsentGiven.value,
                        /* 6777ba5d67aa0906190c1ac3 */
                        if (voodooPrivacyConsent.gdprApplicable) {
                            voodooPrivacyConsent.dataSharingWithVoodooConsentGiven
                        } else {
                            true
                        },
                    )
                    put(
                        AnalyticsUserPropertyType.AdsSDKinitiated.value,
                        hasUserConsentOrNotApplicable,
                    )
                    put(
                        AnalyticsUserPropertyType.AdsConsentUuid.value,
                        voodooPrivacyConsent.grpdUUId,
                    )
                },
            )

            if (hasUserConsentOrNotApplicable) {
                // here we have the user consent (or outside the grpd authority), we initialize applovin

                // Ads can only being initialized when consent is retrieved / when privacy is not applicable
                initAdsInternal(
                    doNotSellDataEnabled = doNotSellDataEnabled,
                    gdprConsentDescription = voodooPrivacyConsent.gdprConsentDescription,
                )
            } else {
                useCases.ads.updateAdnProperties(
                    doNotSell = doNotSellDataEnabled,
                    hasUserContent = hasUserConsentOrNotApplicable,
                )
            }

            viewModelScope.launch(dispatcherProvider.data) {
                useCases.ads.initAdjustUseCase()
            }
        }
    }

    fun logout() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.myUser.logoutUseCase()
        }
    }

    @VisibleForTesting
    suspend fun updateUserToken() {
        useCases.myUser.getDeviceNotificationInstanceId()
            .alsoSuccess {
                useCases.myUser.updateFCMToken(it)
            }
    }

// endregion

    private fun checkRgpd() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.config.needToDisplayRgpdUseCase()?.let {
                _showAppDialog.value = AppDialogsModel.RGPD(
                    RGPDDialog(
                        request = it,
                        onAccepted = {
                            _showAppDialog.value = null // dismiss
                        },
                    ),
                )
            }
        }
    }

    private suspend fun checkMultiDeviceConnected(user: User) {
        if (user.deviceId != null && dataSources.deviceId.value != user.deviceId && initWhenAuthDone) {
            useCases.myUser.logoutUseCase()
        }
    }

    private fun whistlerInitializations() {
        useCases.featureUseCases.observeIsRealTimeServiceEnabledUseCase()
            .filter { it }
            .distinctUntilChanged()
            .mapLatest {
                useCases.featureUseCases.messaging.listenForRealTimeChatMessagesAndStoreUseCase()
            }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(viewModelScope)

        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.messaging.clearDirectConversationPreChatV2UseCase()
        }
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.messaging.refreshFeedDataUseCase(
                requestId = useCases.featureUseCases.messaging.generateFeedRequestIdUseCase(),
                pageSize = FEED_PAGE_SIZE,
            )
        }

        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.featureUseCases.messaging.cleanMessagingLocalMediaUseCase()
        }
    }

    private fun refreshLinkedUsers(user: User) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            useCases.fetchAllMyAccountsUseCase(currentUser = user)
        }
    }

    fun onInAppNotificationClicked(notification: InAppNotification) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            when (
                val result =
                    useCases.getNavigationDirectionFromDeeplinkProcessor(notification.deeplink)
            ) {
                is NavigationDirectionFromDeeplinkUiModel.Direction -> {
                    navManager.navigate(result.direction)
                }

                else -> {
                    Timber.e("Deeplink (${notification.deeplink}) not supported")
                }
            }
        }
    }

    fun clearDialog() {
        _showAppDialog.value = null
    }

    private fun buildUpdateInAppMessage(mandatory: Boolean): InAppMessageUiModel {
        return InAppMessageUiModel(
            title = if (mandatory) {
                stringProvider[R.string.update_mandatoryTitle]
            } else {
                stringProvider[R.string.update_recommendedTitle]
            },
            body = stringProvider[R.string.update_app_description_android],
            image = null,
            tag = stringProvider[R.string.inapp_messaging_tag_warning],
            isBlocking = mandatory,
            action = ActionUrlState(
                name = stringProvider[R.string.update_action],
                url = "",
            ),
            listener = InAppMessageListener(
                onClick = this::onInappMessageAppUpdateClicked,
            ),
            campaign = "internal-update-app-$NOT_A_CAMPAIGN",
        )
    }

    private fun onInappMessageAppUpdateClicked() {
        currentActivity.current?.let { context ->
            Open.store(context)
        }
    }

    private var observeReceivedRealmojiJob: Job? = null
    private fun observeReceivedRealmoji() {
        observeReceivedRealmojiJob = viewModelScope.launch {
            if (!dataSources.settingsRepository.hasSentReceivedRealmojiAdjustEvent()) {
                useCases.myUser.observeHasRealmojisOnAnyOfMyUserPostsUseCase()
                    .collect { realmojisCount ->
                        if (realmojisCount > 0) {
                            useCases.ads.sendAdjustEventEventUseCase(AdsAdjustEvent.ReceivedRealmoji)
                            dataSources.settingsRepository.setHasSentReceivedRealmojiAdjustEvent()
                            observeReceivedRealmojiJob?.cancel()
                        }
                    }
            }
        }
    }

    companion object {
        private const val TAG = "AppViewModel"
        private const val FEED_PAGE_SIZE = 100
    }
}

private val User.Gender?.analyticsValue: String?
    get() {
        return this?.let {
            when (it) {
                User.Gender.Male -> "male"
                User.Gender.Female -> "female"
            }
        }
    }
