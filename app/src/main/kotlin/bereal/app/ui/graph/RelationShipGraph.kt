package bereal.app.ui.graph

import androidx.compose.material.navigation.bottomSheet
import androidx.navigation.NavGraphBuilder
import androidx.navigation.navigation
import bereal.app.navigation.model.direction.BottomSheetFollowersDestination
import bereal.app.navigation.model.direction.BottomSheetFollowingsDestination
import bereal.app.navigation.model.direction.BottomSheetFriendRequestsDestination
import bereal.app.navigation.model.direction.BottomSheetFriendsDestination
import bereal.app.navigation.model.direction.BottomSheetSentRequestsDestination
import bereal.app.navigation.model.direction.RelationShipDestination
import bereal.app.navigation.model.direction.RelationShipGraphDestination
import bereal.app.navigation.model.direction.RelationshipSuggestionsDestination
import bereal.app.relationship.FriendingScreen
import bereal.app.relationship.bottomsheet.followers.FollowersBottomSheet
import bereal.app.relationship.bottomsheet.followings.FollowingsBottomSheet
import bereal.app.relationship.bottomsheet.friendrequests.FriendRequestBottomSheet
import bereal.app.relationship.bottomsheet.friends.FriendsBottomSheet
import bereal.app.relationship.bottomsheet.sentrequests.SentRequestBottomSheet
import bereal.app.relationship.views.FriendingTopBarType

fun NavGraphBuilder.relationShipGraph() {
    navigation<RelationShipGraphDestination>(
        startDestination = RelationShipDestination(),
    ) {
        bottomSheet<RelationShipDestination> {
            FriendingScreen(type = FriendingTopBarType.Title(true))
        }

        bottomSheet<RelationshipSuggestionsDestination> {
            FriendingScreen(type = FriendingTopBarType.Title(true))
        }

        bottomSheet(
            route = BottomSheetFriendsDestination.route,
        ) { backStackEntry ->
            FriendsBottomSheet()
        }

        bottomSheet(
            BottomSheetFriendRequestsDestination.route,
        ) { backStackEntry ->
            FriendRequestBottomSheet()
        }

        bottomSheet(
            BottomSheetSentRequestsDestination.route,
        ) { backStackEntry ->
            SentRequestBottomSheet()
        }

        bottomSheet(
            BottomSheetFollowersDestination.route,
        ) { backStackEntry ->
            FollowersBottomSheet()
        }

        bottomSheet(
            BottomSheetFollowingsDestination.route,
        ) { backStackEntry ->
            FollowingsBottomSheet()
        }
    }
}
