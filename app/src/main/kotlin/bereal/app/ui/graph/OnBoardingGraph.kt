package bereal.app.ui.graph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import bereal.app.features.onboarding.host.OnboardingScreen
import bereal.app.features.onboarding.notificationfirst.ui.FirstNotificationScreen
import bereal.app.navigation.model.direction.OnBoardingDestination
import bereal.app.navigation.model.direction.OnBoardingFirstNotificationDestination
import bereal.app.navigation.model.direction.OnboardingGraphDestination
import bereal.app.requireuser.ui.RequireUser

fun NavGraphBuilder.onBoardingGraph(
    onOnBoardingDone: () -> Unit,
    navigateBack: () -> Unit,
) {
    navigation<OnboardingGraphDestination>(
        startDestination = OnBoardingDestination(startRoute = OnBoardingDestination.StartRoute.DEFAULT),
    ) {
        composable<OnBoardingDestination> {
            OnboardingScreen(
                onOnBoardingDone = onOnBoardingDone,
                onNavigateBackToMainGraph = navigateBack,
            )
        }

        composable<OnBoardingFirstNotificationDestination> {
            RequireUser { _ ->
                FirstNotificationScreen()
            }
        }
    }
}
