package bereal.app.ui.graph

import androidx.compose.material.navigation.bottomSheet
import androidx.navigation.NavGraphBuilder
import bereal.app.analytics.model.AnalyticsView
import bereal.app.features.sharing.domain.model.ShareFrom
import bereal.app.features.sharing.ui.SharingParams
import bereal.app.features.sharing.ui.SharingScreen
import bereal.app.navigation.model.direction.SharingBottomSheetDestination

fun NavGraphBuilder.sharingNavigationGraph() {
    bottomSheet(
        route = SharingBottomSheetDestination.route,
        arguments = SharingBottomSheetDestination.args,
    ) { backStackEntry ->
        val postId: String? = backStackEntry.arguments?.getString(SharingBottomSheetDestination.KEY_POST_ID)
        val analyticsView: AnalyticsView? =
            backStackEntry.arguments?.getString(SharingBottomSheetDestination.KEY_ANALYTICS_VIEW)?.let { AnalyticsView.fromValue(it) }
        val shareFrom: ShareFrom? =
            backStackEntry.arguments?.getString(SharingBottomSheetDestination.KEY_SHARE_FROM)?.let { ShareFrom.valueOf(it) }
        SharingScreen(
            SharingParams(
                postId = postId,
                analyticsView = analyticsView,
                shareFrom = shareFrom,
            ),
        )
    }
}
