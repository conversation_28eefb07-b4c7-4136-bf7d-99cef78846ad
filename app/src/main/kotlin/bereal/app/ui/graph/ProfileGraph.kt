package bereal.app.ui.graph

import androidx.compose.runtime.remember
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import bereal.app.entities.placeholder.ProfilePlaceholder
import bereal.app.features.official.accounts.profile.ProfileDestination
import bereal.app.features.official.accounts.profile.ProfileFeedDestination
import bereal.app.features.official.accounts.profile.ProfileGraphDestination
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.decodeNavigationArgument
import bereal.app.profile.domain.v3.usecases.IsProfileV3EnabledUseCase
import bereal.app.profile.ui.v2.myprofile.MyProfileScreen
import bereal.app.profile.ui.v2.profile.ProfileScreen
import bereal.app.profile.ui.v2.profile.model.UserIdentifier
import bereal.app.profile.ui.v2.profilefeed.ProfileFeedScreen
import bereal.app.profile.ui.v3.profile.other.OtherProfileV3Screen
import bereal.app.profile.ui.v3.profile.own.MyProfileV3Screen
import bereal.app.requireuser.ui.RequireUser
import org.koin.compose.koinInject

fun NavGraphBuilder.profileGraph(
    navigationManager: NavigationManager,
) {
    navigation<ProfileGraphDestination>(
        startDestination = ProfileDestination(),
    ) {
        composable<ProfileDestination> { backStackEntry ->
            val arguments = backStackEntry.toRoute<ProfileDestination>()
            RequireUser { myUser ->
                val userName = arguments.username
                val userId = arguments.userId
                val previousScreenAnalyticsValue = arguments.fromAnalyticsViewValue
                val analyticsOriginExtraParamsEncoded = arguments.analyticsOriginExtraParamsEncoded
                val user = remember {
                    arguments.encoded?.let {
                        decodeNavigationArgument<ProfilePlaceholder>(it)
                    }
                }
                val isMyProfile = when {
                    user != null -> (myUser.uid == user.userId) || (myUser.uid == userId)
                    userName != null -> myUser.userName == userName
                    else -> false
                }
                val isV3 = koinInject<IsProfileV3EnabledUseCase>()()

                if (isMyProfile) { // if navigate to my profile, don't send to ProfileScreen
                    if (isV3) {
                        MyProfileV3Screen(withBackButton = true)
                    } else {
                        MyProfileScreen(withBackButton = true)
                    }
                } else {
                    val userIdentifier = when {
                        userId != null -> UserIdentifier.UserId(userId)
                        userName != null -> UserIdentifier.UserName(userName)
                        user != null -> UserIdentifier.UserId(user.userId)
                        else -> {
                            navigationManager.pop()
                            return@RequireUser
                        }
                    }
                    if (isV3) {
                        OtherProfileV3Screen(
                            previousScreenAnalyticsValue = previousScreenAnalyticsValue,
                            analyticsOriginExtraParamsEncoded = analyticsOriginExtraParamsEncoded,
                            userIdentifier = userIdentifier,
                        )
                    } else {
                        ProfileScreen(
                            userIdentifier = userIdentifier,
                            previousScreenAnalyticsValue = previousScreenAnalyticsValue,
                            analyticsOriginExtraParamsEncoded = analyticsOriginExtraParamsEncoded,
                        )
                    }
                }
            }
        }

        composable<ProfileFeedDestination> {
            ProfileFeedScreen()
        }
    }
}
