package bereal.app.ui.graph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import bereal.app.design.theme.BeRealThemeM3
import bereal.app.navigation.model.direction.SettingsAboutDestination
import bereal.app.navigation.model.direction.SettingsAdsHiddenDestination
import bereal.app.navigation.model.direction.SettingsBlockedUsersDestination
import bereal.app.navigation.model.direction.SettingsContactDestination
import bereal.app.navigation.model.direction.SettingsDeleteMyAccountDestination
import bereal.app.navigation.model.direction.SettingsGraphDestination
import bereal.app.navigation.model.direction.SettingsHelpDestination
import bereal.app.navigation.model.direction.SettingsHiddenUsersDestination
import bereal.app.navigation.model.direction.SettingsMemoriesDestination
import bereal.app.navigation.model.direction.SettingsMusicDestination
import bereal.app.navigation.model.direction.SettingsNotificationsDestination
import bereal.app.navigation.model.direction.SettingsOtherDestination
import bereal.app.navigation.model.direction.SettingsPrivacyDestination
import bereal.app.navigation.model.direction.SettingsRootDestination
import bereal.app.navigation.model.direction.SettingsTimeZoneDestination
import bereal.app.requireuser.ui.RequireUser
import bereal.app.settings.ui.Settings
import bereal.app.settings.ui.subscreens.about.SettingsAbout
import bereal.app.settings.ui.subscreens.adshidden.SettingsAdsHiddenScreen
import bereal.app.settings.ui.subscreens.blockeduser.BlockedUsersScreen
import bereal.app.settings.ui.subscreens.contact.SettingsContact
import bereal.app.settings.ui.subscreens.deletemyaccount.SettingsDeleteMyAccount
import bereal.app.settings.ui.subscreens.help.SettingsHelp
import bereal.app.settings.ui.subscreens.hiddenusers.HiddenUsersScreen
import bereal.app.settings.ui.subscreens.memories.SettingsMemories
import bereal.app.settings.ui.subscreens.music.SettingsMusic
import bereal.app.settings.ui.subscreens.notifications.ui.SettingsNotifications
import bereal.app.settings.ui.subscreens.other.SettingsOther
import bereal.app.settings.ui.subscreens.privacy.ui.SettingsPrivacy
import bereal.app.settings.ui.subscreens.timezone.ui.view.SettingsTimezone

fun NavGraphBuilder.settingsGraph() {
    navigation<SettingsGraphDestination>(
        startDestination = SettingsRootDestination,
    ) {
        composable<SettingsRootDestination> {
            RequireUser {
                Settings()
            }
        }

        composable<SettingsMemoriesDestination> {
            RequireUser {
                SettingsMemories()
            }
        }

        composable<SettingsMusicDestination> {
            RequireUser {
                SettingsMusic()
            }
        }

        composable<SettingsBlockedUsersDestination> {
            RequireUser {
                BlockedUsersScreen()
            }
        }

        composable<SettingsHiddenUsersDestination> {
            RequireUser {
                HiddenUsersScreen()
            }
        }

        composable<SettingsNotificationsDestination> {
            RequireUser {
                SettingsNotifications()
            }
        }

        composable<SettingsPrivacyDestination> {
            RequireUser {
                SettingsPrivacy()
            }
        }

        composable<SettingsTimeZoneDestination> {
            RequireUser {
                BeRealThemeM3 {
                    SettingsTimezone()
                }
            }
        }

        composable<SettingsOtherDestination> {
            RequireUser {
                SettingsOther()
            }
        }

        composable<SettingsAdsHiddenDestination> {
            RequireUser {
                SettingsAdsHiddenScreen()
            }
        }

        composable<SettingsHelpDestination> {
            RequireUser {
                SettingsHelp()
            }
        }

        composable<SettingsAboutDestination> {
            RequireUser {
                SettingsAbout()
            }
        }

        composable<SettingsContactDestination> {
            RequireUser {
                SettingsContact()
            }
        }

        composable<SettingsDeleteMyAccountDestination> {
            RequireUser {
                SettingsDeleteMyAccount()
            }
        }
    }
}
