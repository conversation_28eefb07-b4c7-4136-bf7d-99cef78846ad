package bereal.app.ui.graph

import androidx.compose.material.navigation.bottomSheet
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import bereal.app.analytics.model.AnalyticsView
import bereal.app.navigation.model.direction.MutualFriendsDirection
import bereal.app.navigation.model.direction.MyProfileEditDestination
import bereal.app.navigation.model.direction.MyProfileUsernameDestination
import bereal.app.navigation.model.direction.QrCodeSharingDestination
import bereal.app.profile.domain.v3.usecases.IsProfileV3EnabledUseCase
import bereal.app.profile.ui.editprofile.EditMyProfileScreen
import bereal.app.profile.ui.editprofile.EditMyProfileV3Screen
import bereal.app.profile.ui.editprofile.navigation.EditMyProfileV3Destination
import bereal.app.profile.ui.editprofile.username.EditMyProfileUserNameScreen
import bereal.app.profile.ui.editprofile.view.bio.EditMyProfileBioV3Screen
import bereal.app.profile.ui.editprofile.view.fullname.EditMyProfileFullNameV3Screen
import bereal.app.profile.ui.editprofile.view.links.EditMyProfileLinksV3Screen
import bereal.app.profile.ui.editprofile.viewmodel.EditMyProfileViewModelParam
import bereal.app.profile.ui.mutualfriends.MutualFriendsScreen
import bereal.app.profile.ui.v2.addbereals.AddPublicBeRealDestination
import bereal.app.profile.ui.v2.addbereals.AddPublicBeRealsParam
import bereal.app.profile.ui.v2.addbereals.AddPublicBeRealsScreen
import bereal.app.profile.ui.v2.addbereals.analytics.AddPublicBeRealsAnalytics
import bereal.app.profile.ui.v2.qrcodesharing.SharingScreenWithQRCodeScreen
import bereal.app.profile.ui.v2.qrcodesharing.SharingScreenWithQrCodeParam
import bereal.app.requireuser.ui.RequireUser
import org.koin.compose.koinInject

fun NavGraphBuilder.myProfileGraph() {
    composable<MyProfileEditDestination> { navBackStackEntry ->
        val fieldToEdit = navBackStackEntry.toRoute<MyProfileEditDestination>().fieldToEdit
        RequireUser { user ->
            val isProfileV3EnabledUseCase: IsProfileV3EnabledUseCase = koinInject()
            if (isProfileV3EnabledUseCase()) {
                EditMyProfileV3Screen(
                    param = EditMyProfileViewModelParam(
                        fieldToEditText = fieldToEdit,
                        myProfileInitialInfos = user,
                    ),
                )
            } else {
                EditMyProfileScreen(
                    param = EditMyProfileViewModelParam(
                        fieldToEditText = fieldToEdit,
                        myProfileInitialInfos = user,
                    ),
                )
            }
        }
    }

    composable<MutualFriendsDirection> {
        RequireUser {
            MutualFriendsScreen()
        }
    }

    composable<MyProfileUsernameDestination> {
        RequireUser {
            EditMyProfileUserNameScreen(user = it)
        }
    }

    bottomSheet(
        route = AddPublicBeRealDestination.route,
        arguments = AddPublicBeRealDestination.args,
    ) { backStackEntry ->
        val origin =
            backStackEntry.arguments?.getString(AddPublicBeRealDestination.KEY_ORIGIN)?.let(
                AddPublicBeRealsAnalytics.ViewedAddPublicBeReals.Origin::valueOf,
            ) ?: AddPublicBeRealsAnalytics.ViewedAddPublicBeReals.Origin.MyProfile

        RequireUser {
            AddPublicBeRealsScreen(
                param = AddPublicBeRealsParam(
                    origin = origin,
                ),
            )
        }
    }

    bottomSheet(
        route = QrCodeSharingDestination.route,
        arguments = QrCodeSharingDestination.args,
    ) { backStackEntry ->
        val origin = backStackEntry.arguments?.getString(QrCodeSharingDestination.KEY_ORIGIN)?.let(
            AnalyticsView::valueOf,
        )

        RequireUser {
            SharingScreenWithQRCodeScreen(
                SharingScreenWithQrCodeParam(
                    origin,
                ),
            )
        }
    }

    bottomSheet<EditMyProfileV3Destination.FullNameDestination> {
        RequireUser {
            EditMyProfileFullNameV3Screen()
        }
    }

    bottomSheet<EditMyProfileV3Destination.BioDestination> {
        RequireUser {
            EditMyProfileBioV3Screen()
        }
    }

    bottomSheet<EditMyProfileV3Destination.LinkDestination> {
        RequireUser {
            EditMyProfileLinksV3Screen()
        }
    }
}
