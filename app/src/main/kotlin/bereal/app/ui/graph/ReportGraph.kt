package bereal.app.ui.graph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import bereal.app.navigation.model.direction.ReportChatMomentDestination
import bereal.app.navigation.model.direction.ReportConversationDestination
import bereal.app.navigation.model.direction.ReportGraphDestination
import bereal.app.navigation.model.direction.ReportPostDestination
import bereal.app.navigation.model.direction.ReportUserDestination
import bereal.app.report.ui.conversation.ReportConversationScreen
import bereal.app.report.ui.post.ReportPostScreen
import bereal.app.report.ui.user.ReportUserScreen
import bereal.app.requireuser.ui.RequireUser

fun NavGraphBuilder.reportGraph() {
    navigation<ReportGraphDestination>(
        startDestination = ReportPostDestination(),
    ) {
        composable<ReportPostDestination> {
            RequireUser {
                ReportPostScreen()
            }
        }

        composable<ReportUserDestination> {
            RequireUser {
                ReportUserScreen()
            }
        }

        composable<ReportChatMomentDestination> {
            RequireUser {
                ReportPostScreen()
            }
        }

        composable<ReportConversationDestination> {
            RequireUser {
                ReportConversationScreen()
            }
        }
    }
}
