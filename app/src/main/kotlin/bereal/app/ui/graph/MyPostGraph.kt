package bereal.app.ui.graph

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import bereal.app.mypost.ui.delete.MyPostDeleteScreen
import bereal.app.navigation.model.direction.MyPostDeleteDestination
import bereal.app.navigation.model.direction.MyPostGraphDestination
import bereal.app.requireuser.ui.RequireUser

fun NavGraphBuilder.myPostGraph(
    navController: NavHostController,
) {
    navigation<MyPostGraphDestination>(
        startDestination = MyPostDeleteDestination(""),
    ) {
        composable<MyPostDeleteDestination> {
            RequireUser {
                MyPostDeleteScreen(
                    navController = navController,
                )
            }
        }
    }
}
