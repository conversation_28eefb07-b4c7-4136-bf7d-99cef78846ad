package bereal.app.ui.graph

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideIn
import androidx.compose.animation.slideOut
import androidx.compose.ui.unit.IntOffset
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import bereal.app.navigation.model.direction.ProfileV3OnboardingGraphDestination
import bereal.app.profile.ui.v3.onboarding.main.ProfileV3OnboardingScreen
import bereal.app.profile.ui.v3.onboarding.splash.ProfileV3OnboardingSplashScreen

fun NavGraphBuilder.profileV3OnboardingGraph() {
    navigation<ProfileV3OnboardingGraphDestination>(
        startDestination = ProfileV3OnboardingGraphDestination.START,
    ) {
        composable<ProfileV3OnboardingGraphDestination.ProfileV3OnboardingSplashDestination>(
            enterTransition = { slideIn(initialOffset = { IntOffset(0, it.height) }) },
            exitTransition = { fadeOut() },
            popEnterTransition = { fadeIn() },
            popExitTransition = { slideOut(targetOffset = { IntOffset(0, it.height) }) },
        ) {
            ProfileV3OnboardingSplashScreen()
        }
        composable<ProfileV3OnboardingGraphDestination.ProfileV3OnboardingDestination> {
            ProfileV3OnboardingScreen()
        }
    }
}
