package bereal.app.ui.graph

import androidx.compose.material.navigation.bottomSheet
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import androidx.navigation.toRoute
import bereal.app.analytics.model.AnalyticsView
import bereal.app.memories.ui.detail.v2.MemoriesV2DetailsScreen
import bereal.app.memories.ui.detailpinned.DetailPinnedMemoryScreen
import bereal.app.memories.ui.home.ui.MemoriesScreen
import bereal.app.memories.ui.home.ui.MemoriesScreenType
import bereal.app.memories.ui.home.ui.MemoriesTab
import bereal.app.memories.ui.postvisibility.PostVisibilitySelectorBottomSheet
import bereal.app.memories.ui.videos.ui.views.AllRecapScreen
import bereal.app.navigation.model.direction.AllRecapDestination
import bereal.app.navigation.model.direction.MemoriesAllMyMemoriesDestination
import bereal.app.navigation.model.direction.MemoriesDetailDestination
import bereal.app.navigation.model.direction.MemoriesDetailPinnedMemorySelectionDestination
import bereal.app.navigation.model.direction.MemoriesGraphDestination
import bereal.app.navigation.model.direction.MemoriesSelectVisibilityDestination
import bereal.app.requireuser.ui.RequireUser

fun NavGraphBuilder.memoriesGraph() {
    navigation<MemoriesGraphDestination>(
        startDestination = MemoriesAllMyMemoriesDestination(),
    ) {
        bottomSheet(
            route = MemoriesSelectVisibilityDestination.route,
            arguments = MemoriesSelectVisibilityDestination.args,
        ) {
            PostVisibilitySelectorBottomSheet()
        }

        composable<MemoriesAllMyMemoriesDestination> { backStackEntry ->
            val args = backStackEntry.toRoute<MemoriesAllMyMemoriesDestination>()
            val initialTab = args.tab
                .lowercase()
                .let { memoryTab ->
                    when (memoryTab) {
                        MemoriesTab.CALENDAR_KEY -> MemoriesTab.Calendar()
                        MemoriesTab.VIDEO_KEY -> MemoriesTab.Video()
                        else -> MemoriesTab.Calendar()
                    }
                }

            val deeplinkFromAnalyticsView: AnalyticsView? = args.fromAnalyticsViewValue?.let(AnalyticsView::fromValue)
            RequireUser {
                MemoriesScreen(
                    initialTab = initialTab,
                    screenType = MemoriesScreenType.StandaloneScreen,
                    fromAnalyticsView = deeplinkFromAnalyticsView,
                )
            }
        }

        composable<AllRecapDestination> {
            RequireUser {
                AllRecapScreen()
            }
        }

        composable<MemoriesDetailDestination> {
            RequireUser {
                MemoriesV2DetailsScreen()
            }
        }

        composable<MemoriesDetailPinnedMemorySelectionDestination> {
            RequireUser {
                DetailPinnedMemoryScreen()
            }
        }
    }
}
