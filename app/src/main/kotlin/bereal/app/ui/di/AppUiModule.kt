package bereal.app.ui.di

import bereal.app.ui.AppViewModelUseCases
import bereal.app.ui.MainActivityViewModel
import bereal.app.update.di.AppUpdateModule
import bereal.app.usecases.di.AppUsecasesModule
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.module.dsl.factoryOf
import org.koin.dsl.module
import org.koin.ksp.generated.module

@Module(includes = [AppUsecasesModule::class, AppUpdateModule::class])
@ComponentScan("bereal.app.ui")
class AppUiModule

private val mainUiModule = module {
    factoryOf(MainActivityViewModel::UseCases)
}

val appUiModule = module {
    includes(AppUiModule().module, mainUiModule)

    factoryOf(AppViewModelUseCases::FeatureUseCases)
    factoryOf(AppViewModelUseCases::MyUser)
    factoryOf(AppViewModelUseCases::Messaging)
    factoryOf(AppViewModelUseCases::Config)
    factoryOf(AppViewModelUseCases::Sunset)
    factoryOf(AppViewModelUseCases::Ads)
    factoryOf(AppViewModelUseCases::MomentsUseCase)
    factoryOf(AppViewModelUseCases::DiscoveriesUseCase)
}
