package bereal.app.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import bereal.app.R
import bereal.app.design.theme.BeRealTheme

@Composable
fun SplashScreen() {
    Box(
        modifier = Modifier
            .background(MaterialTheme.colors.background)
            .fillMaxSize(),
    ) {
        Image(
            painterResource(id = bereal.app.design.R.drawable.logo_white_500),
            contentDescription = null,
            modifier = Modifier.align(Alignment.Center),
        )
    }
}

@Preview
@Composable
private fun SplashScreenPreview() {
    BeRealTheme {
        SplashScreen()
    }
}
