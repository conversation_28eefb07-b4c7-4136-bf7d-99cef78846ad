package bereal.app.ui.bind

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import bereal.app.common.stateIn
import bereal.app.commonandroid.FeedDebugInfosUiModel
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.features.ads.domain.usecases.ObserveFeedDebugInfosFlagUseCase
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class FeedDebugInfosViewModel(
    dispatcherProvider: DispatcherProvider,
    observeFeedDebugInfosFlagUseCase: ObserveFeedDebugInfosFlagUseCase,
) : ViewModel() {

    val feedDebugInfos: StateFlow<FeedDebugInfosUiModel> = observeFeedDebugInfosFlagUseCase()
        .map {
            FeedDebugInfosUiModel(isEnabled = it)
        }
        .flowOn(dispatcherProvider.viewmodel)
        .stateIn(
            viewModelScope,
            FeedDebugInfosUiModel(
                isEnabled = false,
            ),
        )
}
