package bereal.app.ui.bind

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import bereal.app.commonandroid.FeedDebugInfosUiModel
import org.koin.androidx.compose.koinViewModel

@Composable
fun feedDebugInfosValue(): FeedDebugInfosUiModel {
    val viewModel: FeedDebugInfosViewModel = koinViewModel()
    val value by viewModel.feedDebugInfos.collectAsStateWithLifecycle()
    return value
}
