package bereal.app.ui.nav

import android.content.Intent
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navDeepLink
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.messaging.MessagingDestination
import bereal.app.navigation.model.direction.ReactionsDirection
import bereal.app.navigation.scheme.SchemeProvider
import bereal.app.reactions.ReactionsScreen
import bereal.app.reactions.mapper.ParamMapper
import bereal.app.reactions.viewmodel.ReactionsViewModel
import bereal.app.requireuser.ui.RequireUser
import bereal.app.timeline.ui.reactions.ReactionsPostDataInteractionsDelegateForTimeLine
import bereal.app.timeline.ui.realmoji.RealmojisViewerDelegateForTimeline
import bereal.app.whistler.ui.moment.reactions.ChatRealmojisViewerDelegate
import bereal.app.whistler.ui.moment.reactions.ReactionsPostDataInteractionsDelegateForChat
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.getKoin
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf
import timber.log.Timber

@OptIn(ExperimentalMaterialApi::class)
fun NavGraphBuilder.reactionsGraph(
    schemeProvider: SchemeProvider,
    navigationManager: NavigationManager,
) {
    composable(
        route = MessagingDestination.MomentDetail.route,
        arguments = listOf(
            navArgument(ReactionsDirection.KEY_POST_ID) {
                type = NavType.StringType
                nullable = false
            },
            navArgument(ReactionsDirection.KEY_FROM) {
                type = NavType.StringType
                defaultValue = ReactionsDirection.ReactionsFrom.Chat.value
            },
        ),
        deepLinks = listOf(
            navDeepLink {
                action = Intent.ACTION_VIEW
                uriPattern = MessagingDestination.MomentDetail.getDeeplinkPattern(
                    schemeProvider.provideInternal(),
                )
            },
        ),
    ) { navBackStackEntry ->
        RequireUser {
            val delegate = getKoin().get<ReactionsPostDataInteractionsDelegateForChat>()
            val viewerDelegate = getKoin().get<ChatRealmojisViewerDelegate>()
            val params = remember(navBackStackEntry) {
                ParamMapper.getParam(
                    params = navBackStackEntry.arguments,
                    viewerDelegate = viewerDelegate,
                    delegate = delegate,
                )
            }
            if (params == null) {
                val navigationManager: NavigationManager = koinInject()
                LaunchedEffect(navigationManager) {
                    Timber.e("WhistlerNavigationGraph : empty params")
                    navigationManager.pop()
                }
            } else {
                val viewModel =
                    koinViewModel<ReactionsViewModel>(parameters = { parametersOf(params) })

                ReactionsScreen(viewModel = viewModel, backStackEntry = navBackStackEntry)
            }
        }
    }

    composable(
        ReactionsDirection.route,
        arguments = listOf(
            navArgument(ReactionsDirection.KEY_POST_ID) {
                type = NavType.StringType
            },
            navArgument(ReactionsDirection.KEY_PLACEHOLDER) {
                type = NavType.StringType
                nullable = true
            },
        ),
        deepLinks = listOf(
            navDeepLink {
                uriPattern = ReactionsDirection.getDeeplinkPattern(schemeProvider.provideInternal())
                action = Intent.ACTION_VIEW
            },
            navDeepLink {
                uriPattern = ReactionsDirection.getGenericPostDeeplinkPattern(schemeProvider.provideInternal())
                action = Intent.ACTION_VIEW
            },
            navDeepLink {
                uriPattern = ReactionsDirection.getExternalDeeplinkPattern(schemeProvider.provideExternal().lowercase())
                action = Intent.ACTION_VIEW
            },
        ),
    ) { navBackStackEntry ->
        val viewerDelegate = getKoin().get<RealmojisViewerDelegateForTimeline>()
        val delegate = getKoin().get<ReactionsPostDataInteractionsDelegateForTimeLine>()

        val params = remember(navBackStackEntry) {
            ParamMapper.getParam(
                params = navBackStackEntry.arguments,
                viewerDelegate = viewerDelegate,
                delegate = delegate,
            )
        }
        if (params == null) {
            LaunchedEffect(navigationManager) {
                Timber.e("TimelineNavigationGraph : empty params")
                navigationManager.pop()
            }
        } else {
            val vm = koinViewModel<ReactionsViewModel>(parameters = { parametersOf(params) })

            RequireUser {
                ReactionsScreen(viewModel = vm, backStackEntry = navBackStackEntry)
            }
        }
    }
}
