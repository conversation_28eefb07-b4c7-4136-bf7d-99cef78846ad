package bereal.app.ui.nav

import android.content.Intent
import androidx.compose.animation.ExitTransition
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideIn
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.IntOffset
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navDeepLink
import bereal.app.features.camera.ui.CameraOrganiseeScreen
import bereal.app.features.camera.ui.CameraOrganiseeViewModel
import bereal.app.navigation.messaging.MessagingDestination
import bereal.app.navigation.model.direction.CameraDirection
import bereal.app.navigation.scheme.SchemeProvider
import bereal.app.whistler.ui.camera.CameraForChat
import bereal.app.whistler.ui.camera.getCameraParamsForChat
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.getKoin
import org.koin.core.parameter.parametersOf

@OptIn(ExperimentalMaterialApi::class)
fun NavGraphBuilder.whistlerCameraGraph(
    schemeProvider: SchemeProvider,
) {
// used only for new camera refact (camera organisee)
    composable(
        route = MessagingDestination.Camera.route,
        arguments = listOf(
            navArgument(CameraDirection.Moment.KEY_EXPIRATION) {
                type = NavType.LongType
                defaultValue = -1
            },
            navArgument(CameraDirection.Moment.KEY_CAPTURE_ID) {
                type = NavType.StringType
                nullable = true
            },
            navArgument(CameraDirection.Moment.KEY_INSTANT_OPTIONS) {
                type = NavType.StringType
                nullable = true
            },
            navArgument(CameraDirection.Moment.KEY_SELECT_AUDIENCE) {
                type = NavType.StringType
                nullable = true
            },
            navArgument(CameraDirection.Moment.KEY_AUTO_ENABLE_LOCATION) {
                type = NavType.BoolType
                defaultValue = false
            },
        ),
        deepLinks = listOf(
            navDeepLink {
                action = Intent.ACTION_VIEW
                uriPattern =
                    CameraDirection.Moment.getDeeplinkPattern(schemeProvider.provideInternal())
            },
        ),
        enterTransition = {
            slideIn(
                animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                initialOffset = { IntOffset(0, it.height) },
            ) + fadeIn(
                animationSpec = tween(durationMillis = 300, easing = LinearEasing),
            )
        },
        exitTransition = {
            ExitTransition.None
        },
        popEnterTransition = {
            slideIn(
                animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                initialOffset = { IntOffset(0, it.height) },
            ) + fadeIn(
                animationSpec = tween(durationMillis = 300, easing = LinearEasing),
            )
        },
        popExitTransition = {
            ExitTransition.None
        },
    ) { navBackStackEntry ->
        val cameraTypeDelegate = getKoin().get<CameraForChat>()
        val params = remember(navBackStackEntry, cameraTypeDelegate) {
            CameraOrganiseeViewModel.Params(
                params = getCameraParamsForChat(navBackStackEntry.arguments),
                cameraTypeDelegate = cameraTypeDelegate,
            )
        }
        CameraOrganiseeScreen(
            viewModel = koinViewModel<CameraOrganiseeViewModel>(
                parameters = {
                    parametersOf(
                        params,
                    )
                },
            ),
        )
    }
}
