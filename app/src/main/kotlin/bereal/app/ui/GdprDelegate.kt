package bereal.app.ui

import bereal.app.analytics.AnalyticsManager
import bereal.app.features.ads.domain.analytics.AdsEvents
import bereal.app.navigation.NavigationManager
import org.koin.core.annotation.Factory

@Factory
class GdprDelegate(
    val navManager: NavigationManager,
    val analyticsManager: AnalyticsManager,
) {

    fun sendScreenViewGdprPopupAnalytics() {
        analyticsManager.logEvent(
            AdsEvents.ScreenViewGdprPopup(
                placement = getGDPRRoutePrefix(),
                view = getGDPRRouteView(),
            ),
        )
    }

    private fun getCurrentRoute(): String? {
        return navManager.currentDestination.value?.route?.substringBefore("?")
    }

    fun getGDPRRoutePrefix(): String {
        val current = getCurrentRoute().orEmpty()
        return if (current == "settings/privacy") {
            // If current screen is privacy it means it has been trigger from the GDPR preprompt
            navManager.currentDestination.value?.route?.substringBefore("?").orEmpty()
        } else {
            current
        }
    }

    fun getGDPRRouteView(): String {
        val current = getCurrentRoute()?.lowercase().orEmpty()
        return if (current.contains("setting")) {
            "settings"
        } else if (current.contains("onboarding") || current.contains("mainscreen") || current.contains("timeline")) {
            "appStart"
        } else "other"
    }
}
