package bereal.app.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.material.ModalBottomSheetDefaults
import androidx.compose.material.ModalBottomSheetValue
import androidx.compose.material.navigation.BottomSheetNavigator
import androidx.compose.material.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.whenStarted
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import bereal.app.bts.ui.player.BTSVideoPlayer
import bereal.app.design.dialog.GenericDialogsDisplayer
import bereal.app.design.feedback.GenericFeedbackDisplayer
import bereal.app.design.fps.FpsDisplayer
import bereal.app.design.lifecycle.BindWithLifecycle
import bereal.app.design.navigation.SendNavigationToAnalytics
import bereal.app.design.remotelogger.RemoteLoggerUi
import bereal.app.design.theme.BeRealTheme
import bereal.app.dialog.AppDialogs
import bereal.app.dualview.player.provider.rememberDefaultDualViewPlayersProvider
import bereal.app.haptic.BeRealHaptics
import bereal.app.haptic.SetupHaptics
import bereal.app.image.core.download.ImageDownloaderUi
import bereal.app.image.core.imageloader.BeRealImageLoaders
import bereal.app.inappmessaging.ui.view.InAppMessagingDisplayer
import bereal.app.music.MusicPlayerSetup
import bereal.app.music.MusicPlayerUi
import bereal.app.navigation.model.direction.MainScreenDirection
import bereal.app.notification.ui.manager.InAppNotifications
import bereal.app.permissions.PermissionRequester
import bereal.app.remote.image.coil.impl.rememberRemoteImageCoilImplementation
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.ui.bind.feedDebugInfosValue
import bereal.app.video.ui.model.BeRealVideoPlayerUi
import com.datadog.android.compose.ExperimentalTrackingApi
import com.datadog.android.compose.NavigationViewTrackingEffect
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.koinInject

@Composable
fun BeRealApp() {
    val beRealImageLoaders: BeRealImageLoaders = koinInject()
    val beRealHaptics: BeRealHaptics = koinInject()
    val musicPlayer: MusicPlayerUi = koinInject()
    val remoteLogger: RemoteLoggerUi = koinInject()
    val beRealVideoPlayer: BeRealVideoPlayerUi = koinInject()
    val btsVideoPlayer: BTSVideoPlayer = koinInject()
    val imageDownloader: ImageDownloaderUi = koinInject()
    val beRealTimeProvider: BeRealTimeProvider = koinInject()

    val remoteImageImplementation = rememberRemoteImageCoilImplementation()
    val dualViewPlayersProvider = rememberDefaultDualViewPlayersProvider()
    val feedDebugInfos = feedDebugInfosValue()

    // defines global dependencies that can be used by all composables
    BeRealLocalProvider(
        beRealImageLoaders = beRealImageLoaders,
        beRealHaptics = beRealHaptics,
        musicPlayer = musicPlayer,
        remoteLogger = remoteLogger,
        beRealVideoPlayer = beRealVideoPlayer,
        btsVideoPlayer = btsVideoPlayer,
        imageDownloader = imageDownloader,
        beRealTimeProvider = beRealTimeProvider,
        dualViewPlayersProvider = dualViewPlayersProvider,
        remoteImageImplementation = remoteImageImplementation,
        feedDebugInfos = feedDebugInfos,
    ) {
        BeRealAppContent()
    }
}

@OptIn(
    ExperimentalTrackingApi::class,
)
@Composable
private fun BeRealAppContent() {
    val appViewModel: AppViewModel = koinViewModel()
    val appDataRefresherViewModel: AppDataRefresherViewModel = koinViewModel()

    val sheetState = rememberModalBottomSheetState(
        initialValue = ModalBottomSheetValue.Hidden,
        animationSpec = ModalBottomSheetDefaults.AnimationSpec,
        skipHalfExpanded = true,
    )

    val bottomSheetNavigator = remember(sheetState) {
        BottomSheetNavigator(sheetState)
    }

    val navController = rememberNavController(bottomSheetNavigator).apply {
        NavigationViewTrackingEffect(navController = this)
        SendNavigationToAnalytics(navController = this)
    }

    val isBottomBarDisplayed by isBottomBarDisplayed(navController)
    BeRealTheme {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            PermissionRequester()
            GenericDialogsDisplayer()

            GenericFeedbackDisplayer(
                logout = appViewModel::logout,
                isBottomBarDisplayed = isBottomBarDisplayed,
            ) {
                // To detect the current navigation route
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                LaunchedEffect(navBackStackEntry) { appViewModel.updateCurrentRoute(navBackStackEntry?.destination) }
                val navEvents by appViewModel.navEventQueues.collectAsStateWithLifecycle({})
                LaunchedEffect(navEvents) { navEvents(navController) }

                BeRealNavigation(
                    modifier = Modifier.fillMaxSize(),
                    navController = navController,
                    bottomSheetNavigator = bottomSheetNavigator,
                    navigationManager = appViewModel.navManager,
                    onOnBoardingDone = appViewModel::onboardingDone,
                    navigateBack = appViewModel::navigateBack,
                    flushVisiblePosts = appDataRefresherViewModel::flushVisiblePosts,
                )

                InAppNotifications(
                    onNotificationClicked = appViewModel::onInAppNotificationClicked,
                )

                // In App Message
                val inAppMessageToDisplay by appViewModel.inAppMessageToDisplay.collectAsStateWithLifecycle()
                InAppMessagingDisplayer(message = inAppMessageToDisplay)

                // Dialogs that can be displayed above the app
                val showAppDialog by appViewModel.showAppDialog.collectAsStateWithLifecycle()
                AppDialogs(
                    showAppDialog = showAppDialog,
                )

                MusicPlayerSetup()

                FpsDisplayer(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .navigationBarsPadding(),
                )
            }
        }

        val lifecycle = LocalLifecycleOwner.current
        LaunchedEffect(key1 = appViewModel) {
            lifecycle.whenStarted {
                appViewModel.onAppStart()
            }
        }

        SetupHaptics()
    }

    BindWithLifecycle(
        onForeground = {
            appViewModel.appInForeground()
            appDataRefresherViewModel.onStart()
        },
        onBackground = {
            appViewModel.appInBackground()
            appDataRefresherViewModel.onStop()
        },
    )
}

@Composable
private fun isBottomBarDisplayed(
    navHostController: NavHostController,
): State<Boolean> {
    val backStackEntry by navHostController.currentBackStackEntryAsState()
    return remember {
        derivedStateOf {
            backStackEntry?.destination?.route?.contains(MainScreenDirection.routePrefix) == true
        }
    }
}
