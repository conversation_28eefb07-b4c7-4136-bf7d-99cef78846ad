package bereal.app.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavDestination.Companion.hasRoute
import bereal.app.common.combines
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.features.activity.center.domain.usecases.FetchActivityCenterUseCase
import bereal.app.features.feed.domain.common.usecases.FetchMyPostsStateUseCase
import bereal.app.features.feed.domain.common.usecases.FetchVisiblePostsUseCase
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.ActivityCenterDestination
import bereal.app.user.usecases.ObserveMyUserUseCase
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class AppDataRefresherViewModel(
    private val dispatcherProvider: DispatcherProvider,
    private val fetchVisiblePostsUseCase: FetchVisiblePostsUseCase,
    private val fetchMyPostsStateUseCase: FetchMyPostsStateUseCase,
    private val fetchActivityCenterUseCase: FetchActivityCenterUseCase,
    private val observeMyUserUseCase: ObserveMyUserUseCase,
    private val navigationManager: NavigationManager,
) : ViewModel() {

    private var forceActivityCenterRefresh = false
    private val appInForeground = MutableStateFlow(true)
    private val lastPollingDateForVisiblePosts = MutableStateFlow<Long?>(null)
    private val lastPollingDateForMyPostsState = MutableStateFlow<Long?>(null)
    private val lastPollingDateForActivityCenter = MutableStateFlow<Long?>(null)
    private val delayBetweenEachPollingForVisiblePosts = MutableStateFlow(FetchVisiblePostsUseCase.INITIAL_COOLDOWN_MS)
    private val delayBetweenEachPollingForMyPostsState = MutableStateFlow(FetchMyPostsStateUseCase.INITIAL_COOLDOWN_MS)
    private val delayBetweenEachPollingForActivityCenter = MutableStateFlow(FetchActivityCenterUseCase.INITIAL_COOLDOWN_MS)
    private val visiblePostsFlushTrigger = MutableStateFlow(System.currentTimeMillis())

    init {
        combines(
            appInForeground,
            observeMyUserUseCase().map { it.uid }.distinctUntilChanged(),
        ).flatMapLatest { (foreground, _) ->
            if (foreground) {
                combines(
                    visiblePostsFlushTrigger.flatMapLatest {
                        pollVisiblePosts()
                    },
                    pollMyPostsState(),
                    pollActivityCenter(),
                )
            } else {
                flowOf(Unit)
            }
        }
            .flowOn(dispatcherProvider.viewmodel)
            .launchIn(viewModelScope)
    }

    private fun pollVisiblePosts() = flow {
        emit(Unit)
        while (true) {
            val timeSinceLastPoll = System.currentTimeMillis() - (lastPollingDateForVisiblePosts.value ?: 0L)
            val timeToWait = (delayBetweenEachPollingForVisiblePosts.value - timeSinceLastPoll).coerceAtLeast(0)

            delay(timeToWait)

            fetchVisiblePostsUseCase().alsoSuccess { cooldownInMs ->
                delayBetweenEachPollingForVisiblePosts.value = cooldownInMs
            }
            lastPollingDateForVisiblePosts.value = System.currentTimeMillis()
        }
    }

    private fun pollMyPostsState() = flow {
        emit(Unit)
        while (true) {
            val timeSinceLastPoll = System.currentTimeMillis() - (lastPollingDateForMyPostsState.value ?: 0L)
            val timeToWait = (delayBetweenEachPollingForMyPostsState.value - timeSinceLastPoll).coerceAtLeast(0)

            delay(timeToWait)

            fetchMyPostsStateUseCase().alsoSuccess { cooldownInMs ->
                delayBetweenEachPollingForMyPostsState.value = cooldownInMs
            }
            lastPollingDateForMyPostsState.value = System.currentTimeMillis()
        }
    }

    // we want to poll th activity center without hydration on the entire app, except when we are on the activity center screen
    private fun pollActivityCenter(): Flow<Unit> {
        return navigationManager.currentDestination.map {
            it == null || !it.hasRoute(ActivityCenterDestination::class)
        }.distinctUntilChanged().flatMapLatest { shouldPoll ->
            if (shouldPoll) {
                fetchActivityCenterFlow()
            } else {
                flowOf(Unit)
            }
        }
    }

    private fun fetchActivityCenterFlow() = flow {
        emit(Unit)
        while (true) {
            val timeSinceLastPoll = System.currentTimeMillis() - (lastPollingDateForActivityCenter.value ?: 0L)
            val timeToWait = (delayBetweenEachPollingForActivityCenter.value - timeSinceLastPoll).coerceAtLeast(0)

            if (!forceActivityCenterRefresh) {
                delay(timeToWait)
            } else {
                forceActivityCenterRefresh = false
            }

            fetchActivityCenterUseCase(withHydration = false).alsoSuccess { cooldownInMs ->
                delayBetweenEachPollingForActivityCenter.value = cooldownInMs
            }
            lastPollingDateForActivityCenter.value = System.currentTimeMillis()
        }
    }

    fun flushVisiblePosts() {
        lastPollingDateForVisiblePosts.value = null
        visiblePostsFlushTrigger.tryEmit(System.currentTimeMillis())
    }

    fun onStart() {
        forceActivityCenterRefresh = true
        appInForeground.value = true
    }

    fun onStop() {
        appInForeground.value = false
    }
}
