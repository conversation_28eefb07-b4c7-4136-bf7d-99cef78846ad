package bereal.app.ui

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import bereal.app.care.CustomerCareScreenProvider
import bereal.app.common.throttleFirst
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.injection.DispatcherProvider
import bereal.app.deeplinks.ui.model.NavigationDirectionFromDeeplinkUiModel
import bereal.app.deeplinks.ui.processors.GetDeeplinkDestinationProcessor
import bereal.app.deeplinks.ui.processors.GetDeeplinkTypeFromIntentProcessor
import bereal.app.deeplinks.ui.processors.GetNavigationDirectionFromDeeplinkProcessor
import bereal.app.deeplinks.ui.processors.customercare.model.CustomerCareNavigationTypeUiModel
import bereal.app.design.feedback.BeRealFeedbackManager
import bereal.app.entities.error.GenericError
import bereal.app.moment.usecases.HandleMomentNotificationIntentUseCase
import bereal.app.moment.usecases.IsIntentFromMomentNotificationProcessor
import bereal.app.music.CheckSpotifyLoginResultUseCase
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.MainScreenDirection
import bereal.app.navigation.scheme.SchemeProvider
import bereal.app.notification.domain.NotificationPlugin
import bereal.app.notification.domain.models.NotificationEventTrackingDomainModel
import bereal.app.notification.ui.processor.GetNotificationPluginFromIntentProcessor
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.translations.R
import bereal.app.usecases.TrackApplicationOpenedUseCase
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import org.koin.android.annotation.KoinViewModel
import timber.log.Timber

@KoinViewModel
class MainActivityViewModel(
    private val dispatcherProvider: DispatcherProvider,
    private val navigationManager: NavigationManager,
    private val dispatchersProvider: DispatcherProvider,
    private val customerCareScreenProvider: CustomerCareScreenProvider,
    private val feedbackManager: BeRealFeedbackManager,
    private val stringProvider: StringProvider,
    private val schemeProvider: SchemeProvider,
    private val timeProvider: BeRealTimeProvider,
    private val useCases: UseCases,
) : ViewModel() {

    private data class AppOpened(
        val at: Long,
        val intent: Intent?,
    )

    // if we open the app from the icon, we are passing by the onResume(intent)
    // if we close then open the app from the icon, we are passing by the onResume(intent)
    // if we have a deeplink : we are passing by onNewIntent(intent) then directly onResume(intent.copy(data = null))
    // then I'm just adding a debouncer to take only the first emitted value each 500ms
    private val trackApplicationOpenedChannel = MutableStateFlow<AppOpened?>(null)

    data class UseCases(
        val getNavigationDirectionFromDeeplinkProcessor: GetNavigationDirectionFromDeeplinkProcessor,
        val checkSpotifyLoginResultUseCase: CheckSpotifyLoginResultUseCase,
        val handleMomentNotificationIntentUseCase: HandleMomentNotificationIntentUseCase,
        val getNotificationPluginFromIntentProcessor: GetNotificationPluginFromIntentProcessor,
        val getDeeplinkTypeFromIntentProcessor: GetDeeplinkTypeFromIntentProcessor,
        val isIntentFromMomentNotificationProcessor: IsIntentFromMomentNotificationProcessor,
        val trackApplicationOpenedUseCase: TrackApplicationOpenedUseCase,
        val getDeeplinkDestinationProcessor: GetDeeplinkDestinationProcessor,
    )

    init {
        listenTrackApplicationOpenedChannel()
    }

    fun onResume(intent: Intent?) {
        timeProvider.sync()
        trackApplicationOpened(intent)
        checkSpotifyLoginResult(intent)
    }

    private fun checkSpotifyLoginResult(intent: Intent?) {
        viewModelScope.launch(dispatchersProvider.viewmodel) {
            useCases.checkSpotifyLoginResultUseCase(intent?.data)
        }
    }

    fun handleIntent(intent: Intent, fromNewIntent: Boolean) {
        Timber.d("Handling intent with data: ${intent?.data} fromNewIntent:$fromNewIntent")
        handleNavigation(intent = intent, fromNewIntent = fromNewIntent)
        checkIntent(intent)
        checkSpotifyLoginResult(intent)
        trackApplicationOpened(intent)
    }

    private fun listenTrackApplicationOpenedChannel() {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            trackApplicationOpenedChannel
                .filterNotNull()
                // only the first emit within this window
                .throttleFirst(500)
                .collect {
                    useCases.trackApplicationOpenedUseCase(it.intent)
                }
        }
    }

    private fun trackApplicationOpened(intent: Intent?) {
        viewModelScope.launch(dispatcherProvider.viewmodel) {
            trackApplicationOpenedChannel.emit(AppOpened(at = System.currentTimeMillis(), intent = intent))
        }
    }

    private fun checkIntent(intent: Intent) {
        intent.extras?.let { intentExtras ->
            viewModelScope.launch(dispatcherProvider.viewmodel) {
                if (useCases.isIntentFromMomentNotificationProcessor(intent)) {
                    useCases.handleMomentNotificationIntentUseCase(intent)
                }

                useCases.getNotificationPluginFromIntentProcessor(intent)?.let { plugin ->
                    NotificationEventTrackingDomainModel.Opened(
                        notificationId = intentExtras.getString(NotificationPlugin.EXTRA_KEY_NOTIFICATION_ID) ?: "",
                        notificationType = intentExtras.getString(NotificationPlugin.EXTRA_KEY_NOTIFICATION_TYPE) ?: "",
                        landingScreen = plugin.onNotificationClicked(intentExtras).value,
                    )
                }
            }
        }
    }

    private fun handleNavigation(intent: Intent, fromNewIntent: Boolean) {
        intent.data?.let { uri ->
            // New deeplink logic
            val beRealDestination = useCases.getDeeplinkDestinationProcessor(uri)
            if (beRealDestination != null) {
                navigationManager.navigate(beRealDestination)
                return
            }

            if (fromNewIntent) {
                manuallyNavigateTo(intent.data)
            } else {
                // navController.handleDeeplink has been called automatically, need to check if manual navigation needed
                val navigationDeeplink = useCases.getDeeplinkTypeFromIntentProcessor(intent.data)
                if (navigationDeeplink == null || navigationDeeplink.manualRouteNeeded) {
                    manuallyNavigateTo(intent.data)
                }
            }
        }
    }

    private fun manuallyNavigateTo(uri: Uri?) {
        viewModelScope.launch(dispatchersProvider.viewmodel) {
            when (val result = useCases.getNavigationDirectionFromDeeplinkProcessor(uri)) {
                is NavigationDirectionFromDeeplinkUiModel.Direction -> {
                    navigationManager.navigate(result.direction)
                }

                is NavigationDirectionFromDeeplinkUiModel.CustomDirection.CustomerCareCustomDirection -> {
                    handleCustomerCareNavigation(result)
                }

                is NavigationDirectionFromDeeplinkUiModel.Destination -> {
                    navigationManager.navigate(result.destination)
                }

                null -> {
                    Timber.e("Deeplink ($uri) not supported")
                }
            }
        }
    }

    private suspend fun handleCustomerCareNavigation(result: NavigationDirectionFromDeeplinkUiModel.CustomDirection.CustomerCareCustomDirection) {
        when (val action = result.type) {
            is CustomerCareNavigationTypeUiModel.LaunchHomePageWithErrorMessage -> {
                navigationManager.navigate(MainScreenDirection.direction(tab = MainScreenDirection.Tab.Timeline, fromAnalyticsViewValue = null))
                feedbackManager.displayError(
                    GenericError.Unhandled(stringProvider[R.string.general_error_something_went_wrong]),
                )
            }

            is CustomerCareNavigationTypeUiModel.LaunchRequestScreenWithBackStack -> {
                val timelineIntent = Intent(
                    Intent.ACTION_VIEW,
                    MainScreenDirection.getDeeplinkUri(
                        scheme = schemeProvider.provideInternal(),
                        tab = MainScreenDirection.Tab.Timeline,
                    ),
                ).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                }
                customerCareScreenProvider.launchRequestScreenWithBackStack(
                    action.ticketId,
                    listOf(timelineIntent),
                )
            }
        }
    }
}
