package bereal.app.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import bereal.app.bts.ui.LocalBTSVideoPlayer
import bereal.app.bts.ui.player.BTSVideoPlayer
import bereal.app.commonandroid.FeedDebugInfosUiModel
import bereal.app.commonandroid.LocalFeedDebugInfos
import bereal.app.design.LocalBeRealTimeProvider
import bereal.app.design.image.LocalRemoteImageImplementation
import bereal.app.design.image.RemoteImageImplementation
import bereal.app.design.remotelogger.LocalRemoteLogger
import bereal.app.design.remotelogger.RemoteLoggerUi
import bereal.app.dualview.player.provider.DualViewPlayersProvider
import bereal.app.dualview.player.provider.LocalDualViewPlayersProvider
import bereal.app.haptic.BeRealHaptics
import bereal.app.haptic.LocalBeRealHaptics
import bereal.app.image.core.download.ImageDownloaderUi
import bereal.app.image.core.download.LocalImageDownloader
import bereal.app.image.core.imageloader.BeRealImageLoaders
import bereal.app.image.core.imageloader.LocalBeRealImageLoaders
import bereal.app.image.core.imageloader.LocalStableImageLoader
import bereal.app.image.core.imageloader.StableImageLoader
import bereal.app.music.LocalMusicPlayer
import bereal.app.music.MusicPlayerUi
import bereal.app.time.provider.BeRealTimeProvider
import bereal.app.video.ui.LocalVideoPlayer
import bereal.app.video.ui.model.BeRealVideoPlayerUi

@Composable
fun BeRealLocalProvider(
    beRealImageLoaders: BeRealImageLoaders,
    beRealHaptics: BeRealHaptics,
    musicPlayer: MusicPlayerUi,
    beRealVideoPlayer: BeRealVideoPlayerUi,
    btsVideoPlayer: BTSVideoPlayer,
    remoteLogger: RemoteLoggerUi,
    imageDownloader: ImageDownloaderUi,
    beRealTimeProvider: BeRealTimeProvider,
    dualViewPlayersProvider: DualViewPlayersProvider,
    remoteImageImplementation: RemoteImageImplementation,
    feedDebugInfos: FeedDebugInfosUiModel,
    content: @Composable () -> Unit,
) {
    val imageLoaders: StableImageLoader = remember(beRealImageLoaders) {
        beRealImageLoaders.baseImageLoader
    }

    CompositionLocalProvider(
        LocalBeRealImageLoaders provides beRealImageLoaders,
        LocalStableImageLoader provides imageLoaders,
        LocalBeRealHaptics provides beRealHaptics,
        LocalMusicPlayer provides musicPlayer,
        LocalRemoteLogger provides remoteLogger,
        LocalVideoPlayer provides beRealVideoPlayer,
        LocalBTSVideoPlayer provides btsVideoPlayer,
        LocalImageDownloader provides imageDownloader,
        LocalBeRealTimeProvider provides beRealTimeProvider,
        LocalDualViewPlayersProvider provides dualViewPlayersProvider,
        LocalRemoteImageImplementation provides remoteImageImplementation,
        LocalFeedDebugInfos provides feedDebugInfos,
    ) {
        content()
    }
}
