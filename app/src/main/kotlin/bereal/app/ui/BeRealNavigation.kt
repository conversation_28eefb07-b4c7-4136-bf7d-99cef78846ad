package bereal.app.ui

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideIn
import androidx.compose.animation.slideOut
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.navigation.BottomSheetNavigator
import androidx.compose.material.navigation.ModalBottomSheetLayout
import androidx.compose.material.navigation.bottomSheet
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.IntOffset
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.navigation.navDeepLink
import androidx.navigation.toRoute
import bereal.app.activity.center.ui.navigation.activityCenterGraph
import bereal.app.camera.ui.instantrealmoji.TakeInstantRealMojiScreen
import bereal.app.camera.ui.realmoji.TakeRealMojiView
import bereal.app.care.navigation.customerCareGraph
import bereal.app.design.theme.BeRealTheme
import bereal.app.features.camera.ui.CameraOrganiseeScreen
import bereal.app.features.camera.ui.CameraOrganiseeViewModel
import bereal.app.features.camera.ui.delegate.CameraForClassicBeReal
import bereal.app.features.camera.ui.mapper.getCameraParamsForRegularBeReal
import bereal.app.features.camera.ui.profilepicture.CameraProfilePictureDestination
import bereal.app.features.camera.ui.profilepicture.CameraProfilePictureScreen
import bereal.app.features.captionv2.ui.EditCaptionScreen
import bereal.app.features.feed.ui.discovery.shared.detailsFeedNavigationGraph
import bereal.app.features.feed.ui.topic.screen.onboarding.TopicInterestsScreen
import bereal.app.features.memories.monthly.ui.navigation.monthlyRecapGraph
import bereal.app.features.memories.recap.ui.navigation.memoriesRecapGraph
import bereal.app.features.official.accounts.common.navigation.officialAccounts
import bereal.app.features.onboarding.restoredeletionprofile.model.PendingDeletionFrom
import bereal.app.features.onboarding.restoredeletionprofile.ui.RestorePendingDeletionProfileScreen
import bereal.app.features.resharing.ui.resharingGraph
import bereal.app.features.streaks.ui.streakNavigationGraph
import bereal.app.features.tagging.ui.taggingGraph
import bereal.app.main.navigation.mainScreenGraph
import bereal.app.map.ui.postmap.PostMapScreen
import bereal.app.multi.account.ui.navigation.multiAccountGraph
import bereal.app.navigation.NavigationManager
import bereal.app.navigation.model.direction.CameraDirection
import bereal.app.navigation.model.direction.CaptionDirection
import bereal.app.navigation.model.direction.MainScreenDirection
import bereal.app.navigation.model.direction.MutualFriendsDirection
import bereal.app.navigation.model.direction.PostMapDirection
import bereal.app.navigation.model.direction.RestorePendingDeletionInAppDirection
import bereal.app.navigation.model.direction.TimelineRealmojiViewerDestination
import bereal.app.navigation.model.direction.TopicDirection
import bereal.app.navigation.scheme.SchemeProvider
import bereal.app.profile.ui.mutualfriends.MutualFriendsScreen
import bereal.app.realmojiViewer.ui.RealMojisViewerScreen
import bereal.app.realmojiViewer.ui.getRealmojisViewerViewModel
import bereal.app.requireuser.ui.RequireUser
import bereal.app.timeline.ui.realmoji.RealmojisViewerDelegateForTimeline
import bereal.app.ui.graph.memoriesGraph
import bereal.app.ui.graph.myPostGraph
import bereal.app.ui.graph.myProfileGraph
import bereal.app.ui.graph.onBoardingGraph
import bereal.app.ui.graph.profileGraph
import bereal.app.ui.graph.profileV3OnboardingGraph
import bereal.app.ui.graph.relationShipGraph
import bereal.app.ui.graph.reportGraph
import bereal.app.ui.graph.settingsGraph
import bereal.app.ui.graph.sharingNavigationGraph
import bereal.app.ui.nav.reactionsGraph
import bereal.app.ui.nav.whistlerCameraGraph
import bereal.app.whistler.navigation.chatGraph
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.getKoin
import org.koin.compose.koinInject
import org.koin.core.parameter.parametersOf

@Composable
fun BeRealNavigation(
    navController: NavHostController,
    bottomSheetNavigator: BottomSheetNavigator,
    navigationManager: NavigationManager,
    modifier: Modifier = Modifier,
    onOnBoardingDone: () -> Unit,
    navigateBack: () -> Unit,
    flushVisiblePosts: () -> Unit,
) {
    val schemeProvider: SchemeProvider = koinInject()

    BackHandler { // overrides the BottomSheetNavigator$BackHandler which causes NPE
        navigateBack()
    }

    ModalBottomSheetLayout(
        modifier = modifier,
        bottomSheetNavigator = bottomSheetNavigator,
        scrimColor = Color.Transparent,
        sheetBackgroundColor = BeRealTheme.colors.sheetBackground,
    ) {
        NavHost(
            modifier = Modifier.fillMaxSize(),
            navController = navController,
            startDestination = MainScreenDirection.route,
            enterTransition = { slideIn(initialOffset = { IntOffset(it.width, 0) }) },
            exitTransition = { fadeOut() },
            popEnterTransition = { fadeIn() },
            popExitTransition = { slideOut(targetOffset = { IntOffset(it.width, 0) }) },
        ) {
            // region new nav
            onBoardingGraph(
                onOnBoardingDone = onOnBoardingDone,
                navigateBack = navigateBack,
            )
            settingsGraph()
            myProfileGraph()
            memoriesGraph()
            monthlyRecapGraph()
            myPostGraph(navController = navController)
            profileGraph(navigationManager = navigationManager)
            relationShipGraph()
            reportGraph()
            sharingNavigationGraph()
            detailsFeedNavigationGraph()
            activityCenterGraph()
            multiAccountGraph()
            customerCareGraph(navController)
            composable<RestorePendingDeletionInAppDirection> { backStackEntry ->
                val profileDeletionDate =
                    backStackEntry.toRoute<RestorePendingDeletionInAppDirection>().profileDeletionDate

                RestorePendingDeletionProfileScreen(
                    profileDeletionDate = profileDeletionDate,
                    pendingDeletionFrom = PendingDeletionFrom.InApp,
                )
            }
            composable<MutualFriendsDirection> {
                RequireUser {
                    MutualFriendsScreen()
                }
            }
            composable<CameraProfilePictureDestination> {
                CameraProfilePictureScreen()
            }
            profileV3OnboardingGraph()

            // endregion

            // region old nav
            reactionsGraph(schemeProvider = schemeProvider, navigationManager = navigationManager)

            bottomSheet<TimelineRealmojiViewerDestination> {
                val delegate = getKoin().get<RealmojisViewerDelegateForTimeline>()
                val vm = getRealmojisViewerViewModel(delegate = delegate)

                RequireUser {
                    RealMojisViewerScreen(vm)
                }
            }

            mainScreenGraph(schemeProvider = schemeProvider, flushVisiblePosts = flushVisiblePosts)
            whistlerCameraGraph(schemeProvider = schemeProvider)

            composable(
                route = CameraDirection.Moment.route,
                arguments = listOf(
                    navArgument(CameraDirection.Moment.KEY_EXPIRATION) {
                        type = NavType.LongType
                        defaultValue = -1
                    },
                    navArgument(CameraDirection.Moment.KEY_OPEN_FROM_FIRST_NOTIF) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                    navArgument(CameraDirection.Moment.KEY_CAPTURE_TYPE) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.Moment.KEY_CAPTURE_ID) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.Moment.KEY_INSTANT_OPTIONS) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.Moment.KEY_EVENT_OPTIONS) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.Moment.KEY_TAP_FROM) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.Moment.KEY_SELECT_AUDIENCE) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.Moment.KEY_AUTO_ENABLE_LOCATION) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                    navArgument(CameraDirection.Moment.KEY_STORY_MODE) {
                        type = NavType.BoolType
                        defaultValue = false
                    },
                ),
                deepLinks = listOf(
                    navDeepLink {
                        action = Intent.ACTION_VIEW
                        uriPattern =
                            CameraDirection.Moment.getDeeplinkPattern(schemeProvider.provideInternal())
                    },
                ),
                enterTransition = {
                    slideIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        initialOffset = { IntOffset(0, it.height) },
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                exitTransition = {
                    slideOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        targetOffset = { IntOffset(0, it.height) },
                    ) + fadeOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                popEnterTransition = {
                    slideIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        initialOffset = { IntOffset(0, it.height) },
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                popExitTransition = {
                    slideOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        targetOffset = { IntOffset(0, it.height) },
                    ) + fadeOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
            ) { navBackStackEntry ->
                val cameraTypeDelegate = getKoin().get<CameraForClassicBeReal>()
                val params = remember(navBackStackEntry, cameraTypeDelegate) {
                    CameraOrganiseeViewModel.Params(
                        cameraTypeDelegate = cameraTypeDelegate,
                        params = getCameraParamsForRegularBeReal(navBackStackEntry.arguments),
                    )
                }
                CameraOrganiseeScreen(
                    viewModel = koinViewModel<CameraOrganiseeViewModel>(
                        parameters = {
                            parametersOf(params)
                        },
                    ),
                )
            }

            composable(
                route = CameraDirection.RealMoji.Regular.route,
                enterTransition = {
                    slideIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        initialOffset = { IntOffset(0, it.height) },
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                exitTransition = {
                    slideOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        targetOffset = { IntOffset(0, it.height) },
                    ) + fadeOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                popEnterTransition = {
                    slideIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        initialOffset = { IntOffset(0, it.height) },
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                popExitTransition = {
                    slideOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        targetOffset = { IntOffset(0, it.height) },
                    ) + fadeOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                arguments = listOf(
                    navArgument(CameraDirection.RealMoji.Regular.KEY_REALMOJI) {
                        type = NavType.StringType
                    },
                    navArgument(CameraDirection.Moment.KEY_CAPTURE_ID) {
                        type = NavType.StringType
                    },
                ),
            ) { backStackEntry ->
                val realMoji =
                    backStackEntry.arguments?.getString(CameraDirection.RealMoji.Regular.KEY_REALMOJI)!!
                RequireUser {
                    TakeRealMojiView(
                        realMoji = realMoji,
                    )
                }
            }

            composable(
                route = CameraDirection.RealMoji.Instant.route,
                enterTransition = {
                    slideIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        initialOffset = { IntOffset(0, it.height) },
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                exitTransition = {
                    slideOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        targetOffset = { IntOffset(0, it.height) },
                    ) + fadeOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                popEnterTransition = {
                    slideIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        initialOffset = { IntOffset(0, it.height) },
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                popExitTransition = {
                    slideOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                        targetOffset = { IntOffset(0, it.height) },
                    ) + fadeOut(
                        animationSpec = tween(durationMillis = 300, easing = LinearEasing),
                    )
                },
                arguments = listOf(
                    navArgument(CameraDirection.RealMoji.Instant.KEY_POST_ID) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(CameraDirection.RealMoji.Instant.KEY_FROM_ANALYTICS_VIEW) {
                        type = NavType.StringType
                    },
                    navArgument(CameraDirection.Moment.KEY_CAPTURE_ID) {
                        type = NavType.StringType
                    },
                    navArgument(CameraDirection.RealMoji.Instant.KEY_IS_NEARBY_IN_HOME) {
                        type = NavType.BoolType
                    },
                ),
            ) {
                RequireUser {
                    TakeInstantRealMojiScreen()
                }
            }

            composable(CaptionDirection.ROUTE) {
                EditCaptionScreen() // transition ?
            }

            bottomSheet(
                TopicDirection.BottomSheetInterests.route,
            ) { backStackEntry ->
                TopicInterestsScreen(
                    bottomBarPadding = PaddingValues(),
                )
            }

            composable(
                route = PostMapDirection.route,
                arguments = listOf(
                    navArgument(PostMapDirection.KEY_MAP_INFO_ENCODED) {
                        type = NavType.StringType
                    },
                    navArgument(PostMapDirection.KEY_FROM_ORIGIN_ANALYTICS_VIEW) {
                        type = NavType.StringType
                        nullable = true
                    },
                    navArgument(PostMapDirection.KEY_FROM_ORIGIN_PARENT_ANALYTICS_VIEW) {
                        type = NavType.StringType
                        nullable = true
                    },
                ),
            ) {
                RequireUser {
                    PostMapScreen()
                }
            }

            chatGraph(schemeProvider)

            taggingGraph(bottomSheetNavigator)
            resharingGraph()
            memoriesRecapGraph()

            officialAccounts()
            streakNavigationGraph()
            // endregion
        }
    }
}
