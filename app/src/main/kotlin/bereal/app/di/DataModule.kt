package bereal.app.di

import bereal.app.bts.data.di.btsDataModule
import bereal.app.data.auth.di.authDataModule
import bereal.app.data.core.di.dataCoreModule
import bereal.app.data.friending.repository.di.friendingDataModule
import bereal.app.data.moderation.di.moderationDataModule
import bereal.app.data.music.di.musicDataModule
import bereal.app.data.post.di.postRemoteEntitiesModule
import bereal.app.data.post.repository.di.postDataModule
import bereal.app.data.realmoji.auto.open.di.autoOpenRealmojiDataModule
import bereal.app.data.region.di.regionDataModule
import bereal.app.data.review.di.reviewDataModule
import bereal.app.data.signed.urls.repository.di.signedUrlDataModule
import bereal.app.data.terms.repository.di.termsDataModule
import bereal.app.data.user.repository.di.userDataModule
import bereal.app.device.di.deviceDataModule
import bereal.app.features.infeedmemories.data.di.inFeedMemoriesDataModule
import bereal.app.features.relationship.data.di.relationshipDataModule
import bereal.app.features.sunset.suggestions.data.di.sunsetSuggestionsDataModule
import bereal.app.features.whatyoumissed.data.di.whatYouMissedDataModule
import bereal.app.friending.di.friendingDomainModule
import bereal.app.moment.di.momentsDataModule
import bereal.app.myuser.repository.di.myUserDataModule
import bereal.app.official.accounts.common.data.impl.di.officialAccountsDataImplModule
import bereal.app.onboarding.repository.di.onBoardingRepositoryModule
import bereal.app.profile.data.di.profileDataModule
import bereal.app.realtime.di.realTimeDomainModule
import bereal.app.realtime.di.realTimeModule
import bereal.app.settings.repositories.di.settingsDataModule
import bereal.app.video.data.di.videoDataModule
import bereal.app.whistler.di.whistlerDataModule
import org.koin.dsl.module

val dataDefaultModule = module {
    includes(
        dataCoreModule,
        authDataModule,
        autoOpenRealmojiDataModule,
        btsDataModule,
        videoDataModule,
        deviceDataModule,
        inFeedMemoriesDataModule,
        moderationDataModule,
        regionDataModule,
        momentsDataModule,
        musicDataModule,
        postDataModule,
        postRemoteEntitiesModule,
        onBoardingRepositoryModule,
        friendingDomainModule,
        friendingDataModule,
        reviewDataModule,
        settingsDataModule,
        signedUrlDataModule,
        relationshipDataModule,
        termsDataModule,
        userDataModule,
        myUserDataModule,
        realTimeModule,
        realTimeDomainModule,
        whistlerDataModule,
        sunsetSuggestionsDataModule,
        officialAccountsDataImplModule,
        profileDataModule,
        whatYouMissedDataModule,
    )
}
