package bereal.app.di

import android.annotation.SuppressLint
import android.provider.Settings
import bereal.app.entities.DeviceId
import bereal.app.requireuser.di.requireUserModule
import bereal.app.ui.di.appUiModule
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

@SuppressLint("HardwareIds")
val appModule = module {
    single {
        DeviceId(
            Settings.Secure.getString(
                androidContext().contentResolver,
                Settings.Secure.ANDROID_ID,
            ),
        )
    }

    includes(
        requireUserModule,
        appUiModule,
    )
}
