package bereal.app.di

import bereal.app.R
import bereal.app.analytics.di.analyticsModule
import bereal.app.appreview.di.appReviewModule
import bereal.app.bereal.videogeneration.di.berealVideoGenerationModule
import bereal.app.common.network.di.networkModule
import bereal.app.common.network.domain.di.networkDomainModule
import bereal.app.common.proto.di.commonProtoModule
import bereal.app.commonandroid.di.commonAndroidModule
import bereal.app.commonandroid.di.translationsModule
import bereal.app.data.user.di.userModule
import bereal.app.debug.mode.di.debugMenuModule
import bereal.app.design.di.designModule
import bereal.app.design.error.genericErrorDataModule
import bereal.app.design.topbar.di.topBarModule
import bereal.app.domain.clear.di.clearDomainModule
import bereal.app.domain.di.domainModule
import bereal.app.haptic.di.hapticModule
import bereal.app.image.core.di.imageModule
import bereal.app.location.di.locationDataModule
import bereal.app.location.di.locationDomainModule
import bereal.app.music.di.musicModule
import bereal.app.music.di.musicPlayerImplModule
import bereal.app.navigation.di.navigationModule
import bereal.app.navigation.scheme.SchemeProvider
import bereal.app.permissions.di.permissionsManagerModule
import bereal.app.platform.core.network.di.coreNetworkModule
import bereal.app.platform.data.core.storage.data.di.coreStorageDataModule
import bereal.app.platform.data.core.storage.domain.di.coreStorageDomainModule
import bereal.app.profile.domain.di.profileDomainModule
import bereal.app.remote.image.coil.di.imageImplModule
import bereal.app.remote.logger.di.remoteLoggerModule
import bereal.app.security.di.securityModule
import bereal.app.settings.di.settingsDomainModule
import bereal.app.shakebugs.ui.di.shakebugsModule
import bereal.app.time.di.timeModule
import bereal.app.videogeneration.di.videoGenerationModule
import bereal.app.videoprocessing.lightcompressor.di.videoCompressionLightCompressorModule
import bereal.app.videoprocessing.media3.di.videoProcessingMedia3Module
import bereal.app.whistler.domain.di.messagingDomainModule
import bereal.app.zendesk.di.zendeskModule
import io.voodoo.apps.privacy.di.adsPrivacyModule
import org.koin.android.ext.koin.androidApplication
import org.koin.dsl.module

fun platformModule(noOp: Boolean) = module {
    single<SchemeProvider> {
        object : SchemeProvider {
            override fun provideInternal() = androidApplication().getString(R.string.deeplink_internal_scheme)

            override fun provideExternal() = androidApplication().getString(R.string.deeplink_external_scheme)

            override fun provideAdjust() = androidApplication().getString(R.string.deeplink_adjust_scheme)
        }
    }
    includes(
        commonAndroidModule,
        commonProtoModule,
        networkModule,
        coreNetworkModule,
        networkDomainModule,
        analyticsModule,
        adsPrivacyModule,
        hapticModule,
        navigationModule,
        designModule,
        topBarModule,
        clearDomainModule,
        domainModule,
        messagingDomainModule,
        profileDomainModule,
        coreStorageDomainModule,
        imageModule(noOp = noOp),
        imageImplModule,
        musicModule,
        musicPlayerImplModule,
        timeModule,
        locationDataModule,
        locationDomainModule,
        appReviewModule,
        permissionsManagerModule,
        remoteLoggerModule(noOp = noOp),
        translationsModule,
        zendeskModule,
        debugMenuModule,
        genericErrorDataModule,
        securityModule,
        settingsDomainModule,
        videoCompressionLightCompressorModule,
        videoProcessingMedia3Module,
        coreStorageDataModule,
        userModule,
        shakebugsModule,
        videoGenerationModule,
        berealVideoGenerationModule,
    )
}
