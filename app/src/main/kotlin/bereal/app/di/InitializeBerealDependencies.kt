package bereal.app.di

import android.app.Application
import bereal.app.BuildConfig
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.androidx.workmanager.koin.workManagerFactory
import org.koin.core.KoinApplication
import org.koin.core.logger.Level

fun KoinApplication.initializeBerealDependencies(
    application: Application,
    noOp: Boolean,
) {
    // Koin Android logger
    androidLogger(if (BuildConfig.DEBUG) Level.ERROR else Level.NONE)
    // inject Android context
    androidContext(application)
    workManagerFactory()
    // use modules
    modules(
        appModule,
        featuresModule,
        dataDefaultModule,
        platformModule(
            noOp = noOp,
        ),
        storeModule,
        logModule(noOp = noOp),
    )
}
