package bereal.app.di

import bereal.app.activity.center.ui.di.activityCenterUiModule
import bereal.app.berealview.di.berealviewModule
import bereal.app.bts.domain.di.moduleBTSDomain
import bereal.app.bts.ui.di.btsUiModule
import bereal.app.camera.di.cameraModule
import bereal.app.caption.di.captionModule
import bereal.app.care.di.customerCareDomainModule
import bereal.app.care.di.customerCareUiModule
import bereal.app.data.auth.di.authDomainModule
import bereal.app.data.signed.urls.di.signedUrlsDomainModule
import bereal.app.deeplinks.ui.di.deeplinksUiModule
import bereal.app.device.di.deviceDomainModule
import bereal.app.dualview.di.dualViewModule
import bereal.app.features.activity.center.data.di.activityCenterDataModule
import bereal.app.features.activity.center.domain.di.activityCenterDomainModule
import bereal.app.features.ads.applovin.di.adsApplovinDomainModule
import bereal.app.features.ads.data.di.adsDataModule
import bereal.app.features.ads.domain.di.adsDomainModule
import bereal.app.features.ads.ui.di.adsUiModule
import bereal.app.features.antibot.data.di.antibotDataModule
import bereal.app.features.antibot.domain.di.antibotDomainModule
import bereal.app.features.camera.data.di.cameraOrganiseeDataModule
import bereal.app.features.camera.domain.di.cameraOrganiseeDomainModule
import bereal.app.features.camera.ui.di.cameraOrganiseeUiModule
import bereal.app.features.caption.domain.di.captionDomainModule
import bereal.app.features.captionv2.data.di.captionV2DataModule
import bereal.app.features.captionv2.di.captionV2UiModule
import bereal.app.features.captionv2.domain.di.captionV2DomainModule
import bereal.app.features.captionv2.navigationimpl.di.captionV2NavigationImplModule
import bereal.app.features.contactbook.data.di.contactBookDataModule
import bereal.app.features.contactbook.domain.di.contactBookDomainModule
import bereal.app.features.contactbook.ui.di.contactBookUiModule
import bereal.app.features.discoveries.ui.di.discoveriesUiModule
import bereal.app.features.feed.data.di.feedDataModule
import bereal.app.features.feed.domain.di.feedDomainModule
import bereal.app.features.feed.ui.di.feedUiModule
import bereal.app.features.friendrecommendations.data.di.friendRecommendationsDataModule
import bereal.app.features.friendrecommendations.domain.di.friendRecommendationsDomainModule
import bereal.app.features.friendrecommendations.ui.di.friendRecommendationsUiModule
import bereal.app.features.infeedmemories.di.inFeedUiModule
import bereal.app.features.infeedmemories.domain.di.inFeedDomainModule
import bereal.app.features.memories.data.di.memoriesDataModule
import bereal.app.features.memories.domain.di.memoriesDomainModule
import bereal.app.features.memories.models_data.di.memoriesDataModelsModule
import bereal.app.features.memories.notification.di.memoriesNotificationModule
import bereal.app.features.memories.recap.data.di.MemoriesRecapDataModule
import bereal.app.features.memories.recap.domain.di.memoriesRecapDomainModule
import bereal.app.features.memories.recap.notification.di.MemoriesRecapNotificationModule
import bereal.app.features.memories.recap.ui.di.memoriesRecapUiModule
import bereal.app.features.multi.account.domain.di.multiAccountDomainModule
import bereal.app.features.mypost.domain.di.myPostDomainModule
import bereal.app.features.official.accounts.common.domain.di.officialAccountsCommonDomainModule
import bereal.app.features.official.accounts.onboarding.ui.di.officialAccountsOnboardingUiModule
import bereal.app.features.official.accounts.profile.di.profileNavigationModule
import bereal.app.features.official.accounts.profile.domain.di.officialProfileDomainModule
import bereal.app.features.official.accounts.recommendations.domain.di.officialAccountsRecommendationsDomainModule
import bereal.app.features.official.accounts.recommendations.ui.di.officialAccountsRecommendationsUiModule
import bereal.app.features.official.accounts.relationship.domain.di.officialAccountsRelationshipDomainModule
import bereal.app.features.onboarding.onBoardingModule
import bereal.app.features.passkeys.data.di.passkeysDataModule
import bereal.app.features.passkeys.domain.di.passkeysDomainModule
import bereal.app.features.passkeys.ui.di.passkeysUiModule
import bereal.app.features.publicsposts.domain.di.publicPostsDomainModule
import bereal.app.features.relationship.domain.di.relationshipDomainModule
import bereal.app.features.resharing.data.di.resharingDataModule
import bereal.app.features.resharing.domain.di.resharingDomainModule
import bereal.app.features.resharing.ui.di.resharingUiModule
import bereal.app.features.search.data.di.searchDataModule
import bereal.app.features.search.domain.di.searchDomainModule
import bereal.app.features.search.ui.di.searchUiModule
import bereal.app.features.sharing.data.di.sharingDataModule
import bereal.app.features.sharing.domain.di.sharingDomainModule
import bereal.app.features.sharing.ui.di.sharingModule
import bereal.app.features.streaks.domain.di.streaksDomainModule
import bereal.app.features.streaks.notification.di.streaksNotificationModule
import bereal.app.features.streaks.ui.di.streaksUiModule
import bereal.app.features.sunset.suggestions.domain.di.sunsetSuggestionsDomainModule
import bereal.app.features.tagging.data.di.taggingDataModule
import bereal.app.features.tagging.domain.di.taggingDomainModule
import bereal.app.features.tagging.ui.di.taggingUiModule
import bereal.app.features.whatyoumissed.domain.di.whatYouMissedDomainModule
import bereal.app.inappmessaging.di.inAppMessagingDataModule
import bereal.app.inappmessaging.di.inAppMessagingDomainModule
import bereal.app.inappmessaging.di.inAppMessagingUiModule
import bereal.app.main.di.mainScreenModuleUi
import bereal.app.map.di.mapModule
import bereal.app.mediaservice.di.mediaServiceDataModule
import bereal.app.memories.ui.memoriesUiModule
import bereal.app.moderation.di.moderationUseCasesModule
import bereal.app.moment.di.momentNotificationModule
import bereal.app.multi.account.ui.di.multiAccountUiModule
import bereal.app.music.di.musicUiModule
import bereal.app.mypost.ui.di.myPostUiModule
import bereal.app.navigation.camera.di.cameraNavigationImplModule
import bereal.app.navigation.messaging.di.whistlerNavigationModule
import bereal.app.notification.data.di.notificationDataModule
import bereal.app.notification.domain.di.notificationDomainModule
import bereal.app.notification.ui.di.notificationUiModule
import bereal.app.onboarding.data.di.onboardingDataModule
import bereal.app.onboarding.domain.di.onboardingDomainModule
import bereal.app.post.di.postDomainModule
import bereal.app.post.ui.di.postUiModule
import bereal.app.profile.ui.di.profileUiModule
import bereal.app.reactions.di.reactionsDomainModule
import bereal.app.reactions.di.reactionsModule
import bereal.app.realmoji.data.di.realmojiDataModule
import bereal.app.realmoji.ui.di.realMojiPickerModule
import bereal.app.realmojiViewer.domain.di.realMojiViewerDomainModule
import bereal.app.realmojiViewer.ui.di.realMojiViewerUiModule
import bereal.app.relationship.di.relationshipModule
import bereal.app.report.di.reportModule
import bereal.app.rgpd.di.rgpdModule
import bereal.app.settings.ui.di.settingsUiModule
import bereal.app.terms.domain.di.termsDomainModule
import bereal.app.timeline.ui.di.timelineModule
import bereal.app.timeline.worker.di.timelineWorkerModule
import bereal.app.user.di.userDomainModule
import bereal.app.user.ui.di.myUserUiModule
import bereal.app.video.domain.di.beRealVideoDomainModule
import bereal.app.video.ui.di.beRealVideoPlayerUiModule
import bereal.app.whistler.di.whistlerUiModule
import bereal.app.whistler.di.whistlerWorkerModule
import bereal.app.whistler.notification.di.whistlerNotificationModule
import bereal.app.whistler.ui.di.messagingUiSharedModule
import org.koin.dsl.module
import org.koin.ksp.generated.module

val featuresModule = module {
    includes(
        berealviewModule,
        dualViewModule,
        moduleBTSDomain,
        MemoriesRecapDataModule().module,
        memoriesRecapDomainModule,
        MemoriesRecapNotificationModule().module,
        memoriesRecapUiModule,
        antibotDataModule,
        antibotDomainModule,
        authDomainModule,
        beRealVideoDomainModule,
        beRealVideoPlayerUiModule,
        btsUiModule,
        cameraModule,
        cameraNavigationImplModule,
        captionDomainModule,
        captionModule,
        customerCareUiModule,
        customerCareDomainModule,
        deeplinksUiModule,
        deviceDomainModule,
        friendRecommendationsDataModule,
        friendRecommendationsDomainModule,
        friendRecommendationsUiModule,
        inAppMessagingUiModule,
        inAppMessagingDomainModule,
        inAppMessagingDataModule,
        inFeedDomainModule,
        inFeedUiModule,
        mapModule,
        mediaServiceDataModule,
        memoriesDataModelsModule,
        memoriesDataModule,
        memoriesDomainModule,
        memoriesUiModule,
        memoriesNotificationModule,
        moderationUseCasesModule,
        momentDomainModule,
        momentNotificationModule,
        musicUiModule,
        myPostDomainModule,
        myPostUiModule,
        myUserUiModule,
        notificationDataModule,
        notificationDomainModule,
        notificationUiModule,
        officialAccountsCommonDomainModule,
        officialAccountsOnboardingUiModule,
        officialAccountsRecommendationsDomainModule,
        officialAccountsRecommendationsUiModule,
        officialAccountsRelationshipDomainModule,
        officialProfileDomainModule,
        onBoardingModule,
        onboardingDataModule,
        onboardingDomainModule,
        mainScreenModuleUi,
        passkeysDataModule,
        passkeysDomainModule,
        passkeysUiModule,
        postDomainModule,
        postUiModule,
        settingsUiModule,
        profileUiModule,
        reactionsModule,
        reactionsDomainModule,
        realmojiDataModule,
        realMojiPickerModule,
        realMojiViewerUiModule,
        realMojiViewerDomainModule,
        relationshipDomainModule,
        relationshipModule,
        reportModule,
        resharingDataModule,
        resharingDomainModule,
        resharingUiModule,
        rgpdModule,
        sharingModule,
        sharingDomainModule,
        sharingDataModule,
        searchDataModule,
        searchDomainModule,
        searchUiModule,
        sunsetSuggestionsDomainModule,
        streaksDomainModule,
        streaksNotificationModule,
        streaksUiModule,
        signedUrlsDomainModule,
        taggingDataModule,
        taggingDomainModule,
        taggingUiModule,
        termsDomainModule,
        timelineModule,
        userDomainModule,
        whistlerNavigationModule,
        whistlerNotificationModule,
        whistlerUiModule,
        messagingUiSharedModule,
        whistlerWorkerModule,
        adsApplovinDomainModule,
        adsDataModule,
        adsDomainModule,
        adsUiModule,
        timelineWorkerModule,
        publicPostsDomainModule,
        activityCenterUiModule,
        activityCenterDomainModule,
        activityCenterDataModule,
        profileNavigationModule,
        captionV2UiModule,
        captionV2DomainModule,
        captionV2DataModule,
        captionV2NavigationImplModule,
        cameraOrganiseeUiModule,
        cameraOrganiseeDomainModule,
        cameraOrganiseeDataModule,
        discoveriesUiModule,
        feedDataModule,
        feedDomainModule,
        feedUiModule,
        whatYouMissedDomainModule,
        contactBookUiModule,
        contactBookDomainModule,
        contactBookDataModule,
        multiAccountUiModule,
        multiAccountDomainModule,
    )
}
