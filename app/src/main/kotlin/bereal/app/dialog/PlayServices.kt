package bereal.app.dialog

import android.app.Activity
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability

object PlayServices {

    fun displayErrorIfNotAvailable(activity: Activity) {
        val googleApiAvailability = GoogleApiAvailability.getInstance()
        val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(activity)
        if (resultCode != ConnectionResult.SUCCESS) {
            val actionOnClose = {
                // on cancel, close the app
                activity.finish()
            }

            googleApiAvailability.getErrorDialog(
                activity,
                resultCode,
                2404, // can be any number, it's just a request code
            )?.also {
                it.setOnCancelListener {
                    actionOnClose()
                }
                it.setOnDismissListener {
                    actionOnClose()
                }
            }?.show()
        }
    }
}
