package bereal.app.dialog

import androidx.compose.runtime.Composable
import bereal.app.common.exhaustive
import bereal.app.dialog.model.AppDialogsModel
import bereal.app.rgpd.ui.RGPDFullScreen

/**
 * Dialogs that can be displayed above the app
 */
@Composable
fun AppDialogs(
    showAppDialog: AppDialogsModel?,
) {
    when (showAppDialog) {
        is AppDialogsModel.RGPD -> {
            RGPDFullScreen(showAppDialog.dialog)
        }
        null -> {
            // no-op
        }
    }.exhaustive
}
