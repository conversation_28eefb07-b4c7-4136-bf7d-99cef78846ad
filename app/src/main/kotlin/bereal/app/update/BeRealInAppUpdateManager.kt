package bereal.app.update

import android.app.Activity
import android.content.Context
import bereal.app.commonandroid.StringProvider
import bereal.app.commonandroid.isInAppUpdateEnabled
import bereal.app.design.feedback.BeRealFeedbackManager
import bereal.app.design.feedback.snack.ui.FeedbackMessageDuration
import bereal.app.design.feedback.snack.ui.FeedbackMessageType
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.android.play.core.ktx.isFlexibleUpdateAllowed
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.koin.core.annotation.Factory
import bereal.app.translations.R.string as translations

@Factory
class BeRealInAppUpdateManager(
    context: Context,
    private val feedbackManager: BeRealFeedbackManager,
    private val applicationScope: CoroutineScope,
    private val stringProvider: StringProvider,
) {

    private val appUpdateManager = AppUpdateManagerFactory.create(context)
    private val isInAppUpdateEnabled by lazy { context.isInAppUpdateEnabled() }

    private val installStateUpdatedListener = InstallStateUpdatedListener {
        if (isInAppUpdateEnabled && it.installStatus() == InstallStatus.DOWNLOADED) {
            applicationScope.launch {
                feedbackManager.displaySnack(
                    message = stringProvider[translations.android_in_app_update_title],
                    type = FeedbackMessageType.SUCCESS,
                    actionText = stringProvider[translations.android_in_app_update_cta],
                    action = { appUpdateManager.completeUpdate() },
                    duration = FeedbackMessageDuration.INDEFINITE,
                )
            }
        }
    }

    fun registerInAppListener() {
        if (isInAppUpdateEnabled) {
            appUpdateManager.registerListener(installStateUpdatedListener)
        }
    }

    fun unregisterInAppListener() {
        if (isInAppUpdateEnabled) {
            appUpdateManager.unregisterListener(installStateUpdatedListener)
        }
    }

    fun checkForInAppUpdate(activity: Activity) {
        if (!isInAppUpdateEnabled) return
        appUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
            val isUpdateAvailable = appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
            if (
                isUpdateAvailable &&
                appUpdateInfo.isFlexibleUpdateAllowed &&
                (appUpdateInfo.clientVersionStalenessDays() ?: -1) >= MINIMUM_DAYS_BETWEEN_UPDATE
            ) // wait at least 3 days before showing the update
                {
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activity,
                        AppUpdateOptions.newBuilder(AppUpdateType.FLEXIBLE).build(),
                        REQUEST_CODE_IN_APP_UPDATE,
                    )
                }
        }
    }

    companion object {
        const val REQUEST_CODE_IN_APP_UPDATE = 12345
        private const val MINIMUM_DAYS_BETWEEN_UPDATE = 3
    }
}
