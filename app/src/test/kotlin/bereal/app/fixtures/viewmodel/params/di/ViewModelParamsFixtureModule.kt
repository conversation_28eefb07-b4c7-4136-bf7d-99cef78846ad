package bereal.app.fixtures.viewmodel.params.di

import androidx.lifecycle.SavedStateHandle
import bereal.app.analytics.model.AnalyticsView
import bereal.app.caption.EditCaptionParams
import bereal.app.caption.model.EditCaptionOpenedFrom
import bereal.app.care.request.form.ui.viewmodel.SupportRequestParams
import bereal.app.entities.BeRealMedia
import bereal.app.entities.PostPlaceHolder
import bereal.app.entities.User
import bereal.app.entities.UserId
import bereal.app.features.official.accounts.profile.ProfileFeedDestination
import bereal.app.features.tagging.ui.TaggingDirection
import bereal.app.memories.ui.calendarfull.FullCalendarMemoriesViewModel
import bereal.app.music.model.MusicProviderUiModel
import bereal.app.music.model.MusicUiModel
import bereal.app.music.open.bottomsheet.viewmodel.OpenMusicProviderViewModel
import bereal.app.navigation.model.direction.DetailFeedDestination
import bereal.app.navigation.model.direction.ReactionsDirection
import bereal.app.profile.ui.v2.profilefeed.model.ProfileFeedType
import bereal.app.profile.ui.v2.qrcodesharing.SharingScreenWithQrCodeParam
import bereal.app.reactions.viewmodel.ReactionsViewModel
import bereal.app.rgpd.domain.model.RgpdAcceptanceRequest
import bereal.app.rgpd.ui.model.RGPDDialog
import io.mockk.mockk
import org.koin.dsl.module

val viewModelParamsFixtureModule = module {

    // used in EditMyProfileViewModel
    single { User.generate("username") }

    single {
        SavedStateHandle(
            mapOf(
                ReactionsDirection.KEY_POST_ID to "postId",
                TaggingDirection.KEY_POST_OWNER_USERNAME to "",
                "feedId" to "",
                "initialPostId" to "",
                "type" to DetailFeedDestination.Type.Pager,
                "profileType" to ProfileFeedDestination.Type.Profile,
                "userName" to "userName",
                "userId" to "userId",
            ),
        )
    }

    single {
        SupportRequestParams(
            savedStateHandle = SavedStateHandle(),
        )
    }

    single {
        RGPDDialog(
            onAccepted = {},
            request = RgpdAcceptanceRequest(
                terms = false,
                privacy = false,
            ),
        )
    }

    single {
        EditCaptionParams(
            postId = "postId",
            openedFrom = EditCaptionOpenedFrom.ReactionView,
        )
    }

    single {
        OpenMusicProviderViewModel.Params(
            music = MusicUiModel.generate(),
            postId = "postId",
            analyticsView = AnalyticsView.Reactions,
            currentUserLoggedProvider = MusicProviderUiModel.Unknown("unknown"),
        )
    }

    single {
        FullCalendarMemoriesViewModel.Params(
            analyticsView = AnalyticsView.RealUpdatesHeader,
        )
    }

    single {
        ReactionsViewModel.Params(
            origin = AnalyticsView.TimelineFof,
            initialPostId = "homero",
            from = ReactionsDirection.ReactionsFrom.MyPost,
            reactionsTo = ReactionsDirection.ReactionsTo.RealMojis,
            feedSource = null,
            placeholder = PostPlaceHolder(
                userName = "Cyrus Sherman",
                isMyPost = false,
                retakeCounter = 5919,
                lateInSeconds = null,
                takenAtMs = 2040,
                primaryImage = BeRealMedia(
                    uri = "sem",
                    height = 6786,
                    width = 7706,
                    mediaType = BeRealMedia.MediaType.VIDEO,
                ),
                secondaryImage = BeRealMedia(
                    uri = "auctor",
                    height = 8288,
                    width = 9240,
                    mediaType = BeRealMedia.MediaType.VIDEO,
                ),
                btsContent = null,
                caption = null,
                isMainPostOnTime = false,
            ),
            fromAnalyticsView = AnalyticsView.FriendList,
            realmojisViewerDelegate = mockk(),
            reactionsPostDataInteractionsDelegate = mockk(),
            analyticsOriginExtraParams = emptyList(),
        )
    }

    single {
        SharingScreenWithQrCodeParam(
            analyticsView = AnalyticsView.FriendList,
        )
    }

    single { ProfileFeedType.Profile }
    single { UserId("") }
}
