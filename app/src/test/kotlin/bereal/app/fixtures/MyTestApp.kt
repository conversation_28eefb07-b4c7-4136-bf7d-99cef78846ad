package bereal.app.fixtures

import android.app.Application
import androidx.room.Room
import bereal.app.di.initializeBerealDependencies
import bereal.app.entities.DeviceId
import bereal.app.fixtures.viewmodel.params.di.viewModelParamsFixtureModule
import bereal.app.whistler.datasource.local.room.ChatDatabase
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import io.mockk.every
import io.mockk.mockk
import org.koin.core.context.startKoin
import org.koin.dsl.module

class MyTestApp : Application() {
    override fun onCreate() {
        super.onCreate()
        startKoin {
            initializeBerealDependencies(
                application = this@MyTestApp,
                noOp = true,
            )

            // mock some classes
            modules(
                module {
                    single {
                        DeviceId("fake device id")
                    }
                    single<FirebaseAuth> {
                        val mock = mockk<FirebaseAuth>()
                        val mockUser = mockk<FirebaseUser>(relaxed = true)

                        every { mock.currentUser } returns mockUser
                        mock
                    }
                    single<ChatDatabase> {
                        Room.inMemoryDatabaseBuilder(get(), ChatDatabase::class.java).build()
                    }
                },
                viewModelParamsFixtureModule,
            )
        }
    }
}
