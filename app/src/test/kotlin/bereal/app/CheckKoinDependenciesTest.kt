package bereal.app

import bereal.app.common.test.BeRealRobolectricTestRunner
import bereal.app.entities.User
import bereal.app.fixtures.MyTestApp
import bereal.app.profile.ui.editprofile.viewmodel.EditMyProfileViewModelParam
import io.mockk.mockkClass
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.stopKoin
import org.koin.test.KoinTest
import org.koin.test.check.checkModules
import org.koin.test.mock.MockProviderRule
import org.robolectric.annotation.Config

@Config(application = MyTestApp::class, sdk = [35])
@RunWith(BeRealRobolectricTestRunner::class)
class CheckKoinDependenciesTest : KoinTest {

    @get:Rule
    val mockProvider = MockProviderRule.create { clazz -> mockkClass(clazz, relaxed = true) }

    @Test
    fun `verify all koin dependencies`() {
        getKoin().checkModules {
            withInstance(
                EditMyProfileViewModelParam(
                    fieldToEditText = null,
                    myProfileInitialInfos = User.generate(),
                ),
            )
        }

        stopKoin()
    }
}
