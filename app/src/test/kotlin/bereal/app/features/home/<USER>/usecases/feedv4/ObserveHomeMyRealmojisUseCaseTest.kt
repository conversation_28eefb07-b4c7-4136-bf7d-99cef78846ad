package bereal.app.features.home.domain.usecases.feedv4

import app.cash.turbine.test
import bereal.app.commonandroid.injection.ValueDispatcherProvider
import bereal.app.entities.PostId
import bereal.app.entities.RealMojiType
import bereal.app.features.feed.domain.common.model.realmoji.MyRealmojiOnPostDomainModel
import bereal.app.features.feed.domain.common.model.realmoji.MyRealmojisOnPostDomainModel
import bereal.app.features.feed.domain.common.usecases.realmojis.ObserveMyRankedRealmojisUseCase
import bereal.app.features.feed.domain.common.usecases.realmojis.ObserveRealmojisCreatedForMyUser
import bereal.app.features.feed.domain.common.usecases.realmojis.ObserveRealmojisRankingExperimentEnabledUseCase
import bereal.app.features.feed.domain.home.usecases.feedv4.ObserveHomeMyRealmojisUseCase
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.UnconfinedTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertEquals

@ExtendWith(MockKExtension::class)
class ObserveHomeMyRealmojisUseCaseTest {

    @MockK
    private lateinit var observeRealmojisCreatedForMyUser: ObserveRealmojisCreatedForMyUser

    @MockK
    private lateinit var observeRealmojisRankingExperimentEnabledUseCase: ObserveRealmojisRankingExperimentEnabledUseCase

    @MockK
    private lateinit var observeMyRankedRealmojisUseCase: ObserveMyRankedRealmojisUseCase

    private lateinit var useCase: ObserveHomeMyRealmojisUseCase

    private val testDispatcher = UnconfinedTestDispatcher()

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        useCase = ObserveHomeMyRealmojisUseCase(
            observeRealmojisCreatedForMyUser = observeRealmojisCreatedForMyUser,
            observeMyRankedRealmojisUseCase = observeMyRankedRealmojisUseCase,
            observeRealmojisRankingExperimentEnabledUseCase = observeRealmojisRankingExperimentEnabledUseCase,
            dispatcherProvider = ValueDispatcherProvider(testDispatcher),
        )
    }

    @Test
    fun `should use ranked realmojis when experiment is enabled`() = runTest(testDispatcher) {
        // Given
        val postId = PostId("test")
        val up = MyRealmojiOnPostDomainModel.AlreadyCreated(
            type = RealMojiType.Up,
            imageUrl = "",
            state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.Selected,
        )
        val clown = MyRealmojiOnPostDomainModel.AlreadyCreated(
            type = RealMojiType.Clown,
            imageUrl = "",
            state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
        )
        val rockHand = MyRealmojiOnPostDomainModel.AlreadyCreated(
            type = RealMojiType.RockHand,
            imageUrl = "",
            state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
        )
        val all = listOf(
            MyRealmojiOnPostDomainModel.Instant,
            clown,
            up,
            MyRealmojiOnPostDomainModel.AlreadyCreated(
                type = RealMojiType.Cry,
                imageUrl = "",
                state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
            ),
            rockHand,
        )
        val ranked = listOf(
            up,
            rockHand,
            clown,
            MyRealmojiOnPostDomainModel.Instant,
            MyRealmojiOnPostDomainModel.AlreadyCreated(
                type = RealMojiType.Cry,
                imageUrl = "",
                state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
            ),
        )

        every { observeRealmojisRankingExperimentEnabledUseCase() } returns flowOf(true)
        every { observeMyRankedRealmojisUseCase(all) } returns flowOf(ranked)
        every { observeRealmojisCreatedForMyUser(postId) } returns flowOf(
            MyRealmojisOnPostDomainModel(realmojis = all, selected = null),
        )

        // When
        useCase(postId).test {
            // Then
            assertEquals(ranked, awaitItem().realmojis)
            awaitComplete()
        }
    }

    @Test
    fun `should not use ranked realmojis when experiement is disabled`() = runTest(testDispatcher) {
        // Given
        val postId = PostId("test")
        val up = MyRealmojiOnPostDomainModel.AlreadyCreated(
            type = RealMojiType.Up,
            imageUrl = "",
            state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.Selected,
        )
        val clown = MyRealmojiOnPostDomainModel.AlreadyCreated(
            type = RealMojiType.Clown,
            imageUrl = "",
            state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
        )
        val rockHand = MyRealmojiOnPostDomainModel.AlreadyCreated(
            type = RealMojiType.RockHand,
            imageUrl = "",
            state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
        )
        val all = listOf(
            MyRealmojiOnPostDomainModel.Instant,
            clown,
            up,
            MyRealmojiOnPostDomainModel.AlreadyCreated(
                type = RealMojiType.Cry,
                imageUrl = "",
                state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
            ),
            rockHand,
        )
        val ranked = listOf(
            up,
            rockHand,
            clown,
            MyRealmojiOnPostDomainModel.Instant,
            MyRealmojiOnPostDomainModel.AlreadyCreated(
                type = RealMojiType.Cry,
                imageUrl = "",
                state = MyRealmojiOnPostDomainModel.AlreadyCreated.State.None,
            ),
        )

        every { observeRealmojisRankingExperimentEnabledUseCase() } returns flowOf(false)
        every { observeMyRankedRealmojisUseCase(all) } returns flowOf(ranked)
        every { observeRealmojisCreatedForMyUser(postId) } returns flowOf(
            MyRealmojisOnPostDomainModel(realmojis = all, selected = null),
        )

        // When
        useCase(postId).test {
            // Then
            assertEquals(all, awaitItem().realmojis)
            awaitComplete()
        }
    }
}
