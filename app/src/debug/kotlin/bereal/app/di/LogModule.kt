package bereal.app.di

import bereal.app.common.network.grpc.flipper.FlipperGrpcPlugin
import bereal.app.common.network.okhttp.OkHttpClientProvider
import bereal.app.commonandroid.applogger.AppLogger
import bereal.app.commonandroid.applogger.AppLoggerNoOp
import com.facebook.flipper.android.AndroidFlipperClient
import com.facebook.flipper.core.FlipperClient
import com.facebook.flipper.plugins.databases.DatabasesFlipperPlugin
import com.facebook.flipper.plugins.databases.impl.SqliteDatabaseDriver
import com.facebook.flipper.plugins.network.NetworkFlipperPlugin
import com.facebook.soloader.SoLoader
import io.grpc.Channel
import org.koin.android.ext.koin.androidApplication
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module
import timber.log.Timber

fun logModule(noOp: Boolean) = module {
    if (noOp) {
        singleOf(::AppLoggerNoOp) {
            bind<AppLogger>()
        }
    } else {
        // FlipperClient cannot be initialized in unit tests, we disable it in `noOp` mode
        single<FlipperClient> {
            val application = androidApplication()
            SoLoader.init(application, false)
            AndroidFlipperClient.getInstance(application)
        }
        singleOf(::AppLoggerImpl) {
            bind<AppLogger>()
        }
    }
    single {
        NetworkFlipperPlugin()
    }
}

class AppLoggerImpl(
    private val flipperClient: FlipperClient,
    private val networkFlipperPlugin: NetworkFlipperPlugin,
    private val flipperGrpcPlugin: FlipperGrpcPlugin,
    private val context: android.content.Context,
    private val okHttpClient: OkHttpClientProvider, // I inject OkHttpClientProvider here to be sure the code inside is called before using .start() on the client
    private val channel: Channel, // I inject Channel here to be sure the code inside is called before using .start() on the client
) : AppLogger {
    override fun start() {
        Timber.plant(Timber.DebugTree())

        flipperClient.addPlugin(networkFlipperPlugin)
        flipperClient.addPlugin(flipperGrpcPlugin)
        flipperClient.addPlugin(
            DatabasesFlipperPlugin(
                SqliteDatabaseDriver(context) {
                    listOf(
                        context.getDatabasePath("bereal.core.database.db"),
                    )
                },
            ),
        )

        flipperClient.start()
    }
}
