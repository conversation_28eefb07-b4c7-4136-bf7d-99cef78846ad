<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application>
        <!-- Sandbox receiver that allows us to fake firebase notifications via adb -->
        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="@null"
            tools:replace="android:permission">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
                <category android:name="com.bereal.ft.sandbox.debug" />
            </intent-filter>
        </receiver>
    </application>
</manifest>