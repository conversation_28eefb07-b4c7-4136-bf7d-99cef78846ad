package bereal.app.di

import bereal.app.commonandroid.applogger.AppLogger
import bereal.app.commonandroid.applogger.AppLoggerNoOp
import org.koin.core.module.dsl.bind
import org.koin.core.module.dsl.singleOf
import org.koin.dsl.module
import timber.log.Timber

fun logModule(noOp: Boolean) = module {
    singleOf(::AppLoggerNoOp) {
        bind<AppLogger>()
    }
    Timber.plant(Timber.DebugTree())
}
