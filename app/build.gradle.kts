@file:Suppress("UnstableApiUsage")

import java.time.Instant

plugins {
    alias(libs.plugins.android.application)
    id("org.gradle.android.cache-fix")
    alias(libs.plugins.browserStack)
    alias(libs.plugins.datadog)
    alias(libs.plugins.firebase.crashlytics)
    alias(libs.plugins.firebase.perf)
    alias(libs.plugins.gms)
    alias(libs.plugins.junit5)
    id(libs.plugins.bereal.obfuscator.get().pluginId)
    id(libs.plugins.bereal.compose.get().pluginId)
    id("kotlin-android")
    id(libs.plugins.bereal.baseline.profile.get().pluginId)
    id(libs.plugins.bereal.koin.base.get().pluginId)
    id("applovin-quality-service")
}

android {
    namespace = "bereal.app"

    compileSdk = Config.compileSdk

    setupCommonAndroidModule()

    defaultConfig {
        applicationId = "com.bereal.ft"
        minSdk = Config.minSdk
        targetSdk = Config.targetSdk

        versionCode = versionCodeFromGit()
        versionName = "3.42.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }

        resourceConfigurations += mutableSetOf(
            "de",
            "en",
            "es",
            "fr",
            "hi",
            "in",
            "it",
            "ja",
            "ko",
            "nl",
            "pt",
            "pt-rBR",
            "sv",
            "tr",
            "zh-rTW",
        )

        manifestPlaceholders += "MAPS_API_KEY" to "AIzaSyDj5F96M7uxeSW9XQ-79ORXFWmCD5mDTrs" // Google Sandbox Project Key as default API KEY

        ndk {
            // Specifies the ABI configurations of your native
            // libraries Gradle should build and package with your app.
            abiFilters += listOf("x86", "x86_64", "armeabi-v7a", "arm64-v8a")
        }
    }

    datadog {
        site = "US5"
    }

    lint {
        // TODO check how we handle this
        disable += listOf("MissingTranslation", "MissingQuantity", "ImpliedQuantity")
        lintConfig = file("lint.xml")
    }

    signingConfigs {
        named("debug") {
            storeFile = file("src/debug/debug.keystore")
            storePassword = "android"
            keyAlias = "androiddebugkey"
            keyPassword = "android"
        }
        register("release") {
            storeFile = file("bereal.keystore")
            storePassword = System.getenv("SIGNING_STORE_PASSWORD")
            keyAlias = System.getenv("SIGNING_KEY_ALIAS")
            keyPassword = System.getenv("SIGNING_KEY_PASSWORD")
        }

        register("external") { // the external
            storeFile = file("src/external/external.jks")
            storePassword = System.getenv("SIGNING_EXTERNAL_STORE_PASSWORD")
            keyAlias = "bereal.external"
            keyPassword = System.getenv("SIGNING_EXTERNAL_KEY_PASSWORD")
        }
    }

    buildTypes {
        debug {
            versionNameSuffix = "-debug"
            applicationIdSuffix = ".debug"
            signingConfig = signingConfigs.getByName("debug")
            extra["enableCrashlytics"] = false
        }

        release {
            isMinifyEnabled = isMinifyEnabled()
            isShrinkResources = isMinifyEnabled()
            isDebuggable = false
            signingConfig = if (forceDebugSignatureForRelease()) {
                signingConfigs.getByName("debug")
            } else {
                signingConfigs.getByName("release")
            }
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "common-obfuscation-rules.pro",
            )
            manifestPlaceholders += "MAPS_API_KEY" to "AIzaSyDLLQJ9O_KQrLYQ5L9kmjh5OBu00HgG02k" // Google Prod Project Key
        }

        maybeCreate("appDistribution")
        getByName("appDistribution") {
            initWith(getByName("release"))
            applicationIdSuffix = ".debug"
            signingConfig = signingConfigs.getByName("debug")
        }

        if (isBenchmarkEnabled()) {
            create("benchmark") {
                isDebuggable = false
                initWith(getByName("release"))
                signingConfig = signingConfigs.getByName("debug")
                matchingFallbacks += listOf("benchmark", "release")
                // Only use benchmark proguard rules
                proguardFiles("benchmark-rules.pro")
            }
        }

        /**
         * A debuggable version of release, without the .debug suffix
         */
        create("internal") {
            initWith(getByName("release"))
            isDebuggable = !isMinifyEnabled()
            isMinifyEnabled = isMinifyEnabled()
            isShrinkResources = isMinifyEnabled()
            signingConfig = signingConfigs.getByName("debug")
            matchingFallbacks += listOf("release")
        }

        /**
         * A version that matches the release one (playstore), but with a different signature
         * We upload this one to our external partners that pre-install it on some devices without using the play store
         */
        create("external") {
            initWith(getByName("release"))
            signingConfig = signingConfigs.getByName("external")
            matchingFallbacks += listOf("release")
        }
    }

    // Specifies one flavor dimension.
    flavorDimensions += "version"
    productFlavors {
        create("prod") {
            // Assigns this product flavor to the "version" flavor dimension.
            // If you are using only one dimension, this property is optional,
            // and the plugin automatically assigns all the module's flavors to
            // that dimension.
            dimension = "version"
            versionNameSuffix = findBranchName()
        }
        create("sandbox") {
            versionNameSuffix = findBranchName() + "-sandbox"
            dimension = "version"
            applicationIdSuffix = ".sandbox"
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true

        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    testOptions {
        unitTests.all {
            it.useJUnitPlatform()
        }
        unitTests.isIncludeAndroidResources = true
    }

    // https://github.com/JLLeitschuh/ktlint-gradle/issues/524
    // https://github.com/JLLeitschuh/ktlint-gradle/pull/558
    // Adds the given source directories to this set.
    sourceSets.configureEach {
        java.srcDirs("src/$name/kotlin")
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            excludes += "**/*.proto"
            pickFirsts += "builddef.lst"
        }
        jniLibs.pickFirsts.add("lib/x86/libc++_shared.so")
        jniLibs.pickFirsts.add("lib/x86_64/libc++_shared.so")
        jniLibs.pickFirsts.add("lib/armeabi-v7a/libc++_shared.so")
        jniLibs.pickFirsts.add("lib/arm64-v8a/libc++_shared.so")
    }
}

browserStackConfig {
    username = "berealgradle_HtsdNXNR"
    accessKey = "********************"
}

applovin {
    apiKey = AppLovinConfig.reviewKey
    enabled = isApplovinQualityServiceEnabled()
}

dependencies {
    coreLibraryDesugaring(libs.desugar)

    implementation(projects.features.antibot.domain)
    implementation(projects.features.antibot.data)

    implementation(projects.features.passkeys.data)
    implementation(projects.features.passkeys.domain)
    implementation(projects.features.passkeys.ui)

    implementation(projects.platform.config)

    implementation(projects.features.berealview.ui)

    implementation(projects.features.camera.lib)
    implementation(projects.features.camera.navigation)
    implementation(projects.features.camera.navigationImpl)
    implementation(projects.features.caption.domain)
    implementation(projects.features.caption.ui)
    implementation(projects.features.comment.ui)

    implementation(projects.features.deeplinks.ui)
    implementation(projects.features.customerCare.ui)
    implementation(projects.features.customerCare.domain)
    implementation(projects.features.inAppMessaging.ui)
    implementation(projects.features.inAppMessaging.domain)
    implementation(projects.features.inAppMessaging.data)
    implementation(projects.features.inFeedMemories.data)
    implementation(projects.features.inFeedMemories.domain)
    implementation(projects.features.inFeedMemories.ui)
    implementation(projects.features.whatYouMissed.data)
    implementation(projects.features.whatYouMissed.domain)
    implementation(projects.features.map.lib)
    implementation(projects.features.memories.ui)
    implementation(projects.features.memories.data)
    implementation(projects.features.memories.domain)
    implementation(projects.features.memories.modelsData)
    implementation(projects.features.memories.modelsDomain)
    implementation(projects.features.memories.notification)
    implementation(projects.features.moment.domain)
    implementation(projects.features.moment.notification)
    implementation(projects.features.moderation.domain)
    implementation(projects.features.mypost.domain)
    implementation(projects.features.mypost.ui)
    implementation(projects.features.notification.domain)
    implementation(projects.features.notification.ui)
    implementation(projects.features.onboarding.lib)
    implementation(projects.features.onboarding.nav)
    implementation(projects.features.onboarding.data)
    implementation(projects.features.onboarding.domain)
    implementation(projects.features.profile.navigation)
    implementation(projects.features.profile.ui)
    implementation(projects.features.settings.ui)
    implementation(projects.features.profile.domain)
    implementation(projects.features.reactions.domain)
    implementation(projects.features.reactions.ui)
    implementation(projects.features.realmoji.realmojiUi)
    implementation(projects.features.realmoji.realmojiDomain)
    implementation(projects.features.realmoji.realmojiData)
    implementation(projects.features.realmojiViewer.domain)
    implementation(projects.features.realmojiViewer.ui)
    implementation(projects.features.relationship.lib)
    implementation(projects.features.relationship.domain)
    implementation(projects.features.relationship.data)
    implementation(projects.features.report.lib)
    implementation(projects.features.rgpd.lib)

    implementation(projects.features.activityCenter.ui)
    implementation(projects.features.activityCenter.domain)
    implementation(projects.features.activityCenter.data)

    // MediaService
    implementation(projects.features.mediaService.dataImpl)
    implementation(projects.features.timeline.lib)
    implementation(projects.features.timeline.timelineDomain)
    implementation(projects.features.timeline.worker)
    implementation(projects.features.myUser.domain)
    implementation(projects.features.myUser.ui)

    //region whistler
    implementation(projects.features.whistler.ui)
    implementation(projects.features.whistler.uiShared)

    implementation(projects.features.whistler.navigation)

    implementation(projects.features.whistler.dataCore)
    implementation(projects.features.whistler.dataImpl)

    implementation(projects.features.whistler.workerCore)
    implementation(projects.features.whistler.workerImpl)

    implementation(projects.features.whistler.notificationCore)
    implementation(projects.features.whistler.notificationImpl)

    implementation(projects.features.whistler.domain)

    implementation(projects.features.whistler.entities)
    // enregion

    implementation(projects.platform.analytics.analytics)
    implementation(projects.platform.analytics.analyticsImpl)
    implementation(projects.platform.common)
    implementation(projects.platform.commonAndroid)
    implementation(projects.platform.commonAndroidImpl)
    implementation(projects.platform.commonNetwork.commonNetwork)
    implementation(projects.platform.commonNetwork.commonNetworkDomain)
    implementation(projects.platform.commonProto)
    implementation(projects.platform.data.coreNetwork)
    implementation(projects.platform.data.coreStorage.data)
    implementation(projects.platform.design.core)
    implementation(projects.platform.design.topbar)
    implementation(projects.platform.haptic.core)
    implementation(projects.platform.haptic.impl)
    implementation(projects.platform.entities)
    implementation(projects.platform.navigation)
    implementation(projects.platform.location.domain)
    implementation(projects.platform.location.data)
    implementation(projects.platform.clear)
    implementation(projects.platform.domain)
    implementation(projects.platform.debugMode.debugModeCore)
    implementation(projects.platform.review.core)
    implementation(projects.platform.review.impl)
    implementation(projects.platform.remoteLogger.remoteLogger)
    implementation(projects.platform.remoteLogger.remoteLoggerImpl)
    implementation(projects.platform.requireUser)
    implementation(projects.platform.error)
    implementation(projects.platform.security)
    implementation(projects.platform.time)
    implementation(projects.platform.image.coreUi)
    implementation(projects.platform.image.ui)
    implementation(projects.platform.image.remoteImage.core)
    implementation(projects.platform.image.remoteImage.implCoil)
    implementation(projects.platform.music.core)
    implementation(projects.platform.music.spotifyImpl)
    implementation(projects.platform.music.playerImpl)
    implementation(projects.platform.music.musicSetup)
    implementation(projects.platform.music.domain)
    implementation(projects.platform.music.ui)
    implementation(projects.platform.permissions.core)
    implementation(projects.platform.permissions.ui)
    implementation(projects.platform.store.filestore)
    implementation(projects.platform.translations)
    implementation(projects.platform.frameworkZendesk)
    implementation(projects.platform.videoProcessing.videoProcessing)
    implementation(projects.platform.videoProcessing.videoProcessingLightcompressorImpl)
    implementation(projects.platform.videoProcessing.videoProcessingMedia3Impl)
    implementation(projects.platform.videoGeneration.videoGenerationMuxer)
    implementation(projects.platform.videoGeneration.videoGeneration)
    implementation(projects.platform.videoGeneration.berealVideoGeneration)

    val prodAppDistributionImplementation by configurations.creating
    val prodDebugImplementation by configurations.creating
    val sandboxImplementation by configurations

    sandboxImplementation(projects.platform.debugMode.debugModeOp)
    prodDebugImplementation(projects.platform.debugMode.debugModeOp)
    releaseImplementation(projects.platform.debugMode.debugModeNoOp)
    externalImplementation(projects.platform.debugMode.debugModeNoOp)
    internalImplementation(projects.platform.debugMode.debugModeOp)

    if (System.getenv("WITH_DEBUG_TOOLS")?.toBoolean() == true) {
        prodAppDistributionImplementation(projects.platform.debugMode.debugModeOp)
    } else {
        prodAppDistributionImplementation(projects.platform.debugMode.debugModeNoOp)
    }

    if (isBenchmarkEnabled()) {
        dependencies.add("benchmarkImplementation", projects.platform.debugMode.debugModeNoOp)
    }

    implementation(projects.platform.device.domain)
    implementation(projects.platform.data.core.coreImpl)
    implementation(projects.platform.commonNetwork.commonNetworkImpl)
    implementation(projects.platform.data.auth.domain)
    implementation(projects.platform.data.auth.authImpl)

    implementation(projects.platform.data.realmojiAutoOpen.realmojiAutoOpenImpl)
    implementation(projects.platform.data.region.regionImpl)

    implementation(projects.features.friending.domain)
    implementation(projects.features.friending.data.dataImpl)
    implementation((projects.platform.device.dataImpl))
    implementation(projects.platform.data.moderation.moderationImpl)
    implementation(projects.features.moment.domain)
    implementation(projects.features.moment.dataImpl)
    implementation(projects.platform.data.music.musicImpl)
    implementation(projects.features.myUser.dataImpl)
    implementation(projects.features.notification.data.impl)
    implementation(projects.platform.data.post.post)
    implementation(projects.platform.data.post.postRemoteEntities)
    implementation(projects.platform.data.post.postImpl)
    implementation(projects.platform.data.onboarding.onboardingImpl)

    implementation(projects.platform.data.realTime.realTime)
    implementation(projects.platform.data.realTime.realTimeImpl)

    implementation(projects.platform.data.review.reviewImpl)
    implementation(projects.platform.settings.data.impl)

    implementation(projects.platform.signedUrls.domain)
    implementation(projects.platform.signedUrls.dataImpl)

    implementation(projects.features.terms.domain)
    implementation(projects.features.terms.data.dataImpl)
    implementation(projects.platform.data.user.user)
    implementation(projects.platform.data.user.userImpl)

    implementation(projects.features.captionV2.domain)
    implementation(projects.features.captionV2.data)
    implementation(projects.features.captionV2.ui)
    implementation(projects.features.captionV2.navigationImpl)

    implementation(projects.features.profile.ui)

    implementation(projects.platform.settings.domain)
    implementation(projects.features.post.domain)
    implementation(projects.features.post.ui)
    implementation(projects.features.post.publicPostsDomain)

    implementation(projects.features.friendRecommendations.data)
    implementation(projects.features.friendRecommendations.domain)
    implementation(projects.features.friendRecommendations.ui)

    implementation(projects.features.video.ui)
    implementation(projects.features.video.domain)
    implementation(projects.features.video.data)

    implementation(projects.features.streaks.domain)
    implementation(projects.features.streaks.ui)
    implementation(projects.features.streaks.notification)

    implementation(projects.features.tagging.data)
    implementation(projects.features.tagging.domain)
    implementation(projects.features.tagging.ui)

    implementation(projects.features.resharing.data)
    implementation(projects.features.resharing.domain)
    implementation(projects.features.resharing.ui)

    implementation(projects.features.bts.data)
    implementation(projects.features.bts.domain)
    implementation(projects.features.bts.ui)

    implementation(projects.features.memoriesRecap.data)
    implementation(projects.features.memoriesRecap.domain)
    implementation(projects.features.memoriesRecap.ui)
    implementation(projects.features.memoriesRecap.notification)

    // Official Accounts
    implementation(projects.features.officialAccounts.common.data.dataCore)
    implementation(projects.features.officialAccounts.common.data.dataImpl)
    implementation(projects.features.officialAccounts.common.domain)
    implementation(projects.features.officialAccounts.common.navigation)
    implementation(projects.features.officialAccounts.onboarding.ui)
    implementation(projects.features.profile.data)
    implementation(projects.features.officialAccounts.profile.domain)
    implementation(projects.features.officialAccounts.recommendations.domain)
    implementation(projects.features.officialAccounts.recommendations.navigation)
    implementation(projects.features.officialAccounts.recommendations.ui)
    implementation(projects.features.officialAccounts.relationship.domain)
    implementation(projects.features.officialAccounts.suggestions.data) // To be removed
    implementation(projects.features.officialAccounts.suggestions.domain)
    // end Official Accounts

    implementation(projects.features.main.ui)

    implementation(projects.features.ads.ui)
    implementation(projects.features.ads.domain)
    implementation(projects.features.ads.data)

    implementation(projects.features.feed.ui)
    implementation(projects.features.feed.data)
    implementation(projects.features.feed.domain)

    implementation(projects.features.discoveries.ui)

    implementation(projects.features.sharing.data)
    implementation(projects.features.sharing.domain)
    implementation(projects.features.sharing.ui)

    implementation(projects.platform.shakebugs)

    implementation(projects.features.search.data)
    implementation(projects.features.search.domain)
    implementation(projects.features.search.ui)

    implementation(projects.features.cameraOrganisee.data)
    implementation(projects.features.cameraOrganisee.domain)
    implementation(projects.features.cameraOrganisee.ui)

    implementation(projects.features.contactbook.data)
    implementation(projects.features.contactbook.domain)
    implementation(projects.features.contactbook.ui)

    implementation(projects.features.multiAccount.domain)
    implementation(projects.features.multiAccount.ui)

    implementation(libs.bundles.firebase)
    implementation(libs.bundles.koin)
    implementation(libs.grpc.android)

    implementation(libs.datadog.android)
    implementation(libs.datadog.android.compose)
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.compose.material.navigation)
    implementation(libs.accompanist.permissions)
    implementation(libs.firebase.messaging)
    implementation(libs.kotlinx.datetime)
    implementation(libs.timber)
    implementation(libs.kronos)
    implementation(libs.koin.worker)
    implementation(libs.kotlinx.serialization.json)
    implementation(libs.coil.compose)
    implementation(libs.semver)
    implementation(libs.androidx.lifecycle.runtimeCompose)

    implementation(libs.androidx.profileInstaller)

    debugImplementation(libs.bundles.flipper)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.performance) // only in app/

    implementation(libs.play.inapp.update)
    implementation(libs.play.inapp.update.ktx)

    testImplementation(libs.room.ktx)
    testImplementation(projects.features.whistler.dataImpl)

    testImplementation(libs.bundles.tests)
    testImplementation(projects.platform.device.domain)
    testImplementation(projects.platform.commonTestUi)

    if (isBenchmarkEnabled()) {
        dependencies.add("baselineProfile", ":benchmark")
    }

    // disabled outside the CI to save some configuration time
    if (isKoverMergeReportEnabled()) {
        project.parent?.allprojects?.forEach { otherProject ->
            if (otherProject.name !in listOf("app", "arkose")) {
                kover(otherProject)
            }
        }
    }

    implementation(projects.features.ads.adsApi)
    implementation(projects.features.ads.adsApplovin)
    implementation(projects.features.ads.privacy)

    implementation(projects.platform.shakebugs)
}

/**
 * Utility function to retrieve the name of the current git branch.
 * Will not work if build tool detaches head after checkout, which some do!
 */
fun gitCurrentBranch(): String? = try {
    val byteOut = org.apache.commons.io.output.ByteArrayOutputStream()
    project.exec {
        commandLine = "git rev-parse --abbrev-ref HEAD".split(" ")
        standardOutput = byteOut
    }
    String(byteOut.toByteArray()).trim().let {
        if (it == "HEAD") {
            // might be from a tag
            logger.warn("Unable to determine current branch: Project is checked out with detached head!")
            null
        } else {
            it
        }
    }
} catch (e: Exception) {
    // "Unable to determine current branch
    null
}

fun findBranchName(): String {
    return if (System.getenv("ALLOW_VERSION_NAMES_FOLLOWING_GIT") == "true") {
        val gitBranch = gitCurrentBranch()
        return if (gitBranch == null) {
            ""
        } else {
            val branchWithoutFolder = if (gitBranch.contains("/")) {
                gitBranch.split("/").last()
            } else {
                gitBranch
            }
            if (branchWithoutFolder == "master") {
                ""
            } else {
                "-$branchWithoutFolder"
            }
        }
    } else ""
}

fun versionCodeFromGit(): Int = if (isRunningOnCI()) {
    // Retrieve the timestamp of the last commit in Unix time (seconds since 1970-01-01).
    val lastCommitTimestamp = execute(command = "git show -s --format=%ct").trim().let { output ->
        // Convert the output (commit timestamp) to an integer, or throw an error if it's not a valid integer.
        output.toIntOrNull() ?: error("Output not an int: $output")
    }

    // Define a fixed starting date for calculating the build code. This is in ISO 8601 format with timezone info.
    val startDate = Instant.parse("2025-03-07T14:20:00.00+01:00").epochSecond.toInt()
    // This is the starting build code used at the given start date.
    val startCode = 3516170

    // Calculate the number of seconds between the last commit timestamp and the fixed start date.
    val numberOfSecondsBetweenCommitAndDate = lastCommitTimestamp - startDate
    // Convert the number of seconds into minutes.
    val numberOfMinutesBetweenCommitAndDate = numberOfSecondsBetweenCommitAndDate / 60

    println("numberOfSecondsBetweenCommitAndDate: $numberOfSecondsBetweenCommitAndDate")
    println("numberOfMinutesBetweenCommitAndDate: $numberOfMinutesBetweenCommitAndDate")

    // Assume that builds are generated at a rate of not less than 1 per 10 minute
    val numberOfSameBuildCodePerMinute = 10

    // Calculate how many "build codes" should be added based on the time difference (in minutes) and the build rate.
    val buildCodeAddition = numberOfMinutesBetweenCommitAndDate / numberOfSameBuildCodePerMinute
    println("buildCodeAddition: $buildCodeAddition")

    // Calculate the current build code by adding the build code addition to the starting build code.
    val currentCode = startCode + buildCodeAddition
    println("currentCode: $currentCode")

    // Return the current build code.
    currentCode
} else {
    31337
}.also { version -> logger.lifecycle("** VersionCode --> $version") }

tasks.register("writeVersionCode") {
    doLast {
        val outputFile = file("../versionCode")
        outputFile.writeText(versionCodeFromGit().toString())
    }
}

// this fixes a safe-sdk (applovin plugin) issue
afterEvaluate {
    val mergeTask = tasks.findByName("mergeProdAppDistributionGlobalSynthetics")
    val safeSdkTask = tasks.findByName("safedkInstrumentationProdAppDistribution")

    if (mergeTask != null && safeSdkTask != null) {
        safeSdkTask.dependsOn(mergeTask)
    }

    val mergeSandboxTask = tasks.findByName("mergeSandboxAppDistributionGlobalSynthetics")
    val safeSdkSandboxTask = tasks.findByName("safedkInstrumentationSandboxAppDistribution")

    if (mergeSandboxTask != null && safeSdkSandboxTask != null) {
        safeSdkSandboxTask.dependsOn(mergeSandboxTask)
    }
}
