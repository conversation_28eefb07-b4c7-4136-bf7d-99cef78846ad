# frozen_string_literal: true

require 'net/http'

module Fastlane
  module Actions
    class LokaliseMetadataAction < Action
      class << self
        def run(params)
          @params = params
          release_number = params[:release_number]
          UI.user_error! 'Release number is required when using `update_googleplay` action (should be an integer and greater that 0)' unless release_number.is_a?(Integer) && release_number&.positive?
          key_file = metadata_key_file_googleplay
          metadata = get_metadata_from_lokalise_googleplay
          save_metadata_to_files metadata, release_number
        end

        def metadata_key_file_googleplay
          {
            'playstore.app.name' => 'title',
            'playstore.app.description' => 'full_description',
            'playstore.app.short_description' => 'short_description',
            'playstore.app.release_notes.master' => 'changelogs'
          }
        end

        def bereal_fix_language_name(name, unknownLocale, _for_lokalise = false)
          # from lokalize, we have to map to supported languages in play store
          # see https://support.google.com/googleplay/android-developer/table/4419860?hl=en
          # https://www.ibabbleon.com/Google-Play-Store-Language-Codes.html

          name = name.gsub('_', '-')

          # de ------- German
          return ['de-DE'] if name == 'de'

          # en ------- English (UK + US)
          return %w[en-US en-GB] if name == 'en'

          # es ------- Spanish (Spain)
          return ['es-ES'] if name == 'es'

          # es-MX -------- Spanish (Latin America)
          return ['es-419'] if name == 'es-MX'

          # fr ------ French (France)
          return ['fr-FR'] if name == 'fr'

          # hi ------ Hindi
          return ['hi-IN'] if name == 'hi'

          # id -------- Indonesian
          return ['id'] if name == 'id'

          # it --------- Italian
          return ['it-IT'] if name == 'it'

          # ja --------- Japanese
          return ['ja-JP'] if name == 'ja'

          # ko --------- Korean (South Korea)
          return ['ko-KR'] if name == 'ko'
          # nl ------- Dutch
          return ['nl-NL'] if name == 'nl'
          # pt ------ Portuguese (Portugal) + Portuguese (Brazil)
          return %w[pt-BR pt-PT] if name == 'pt'
          # tr ------ Turkish
          return ['tr-TR'] if name == 'tr'
          # zh-CN ------- Chinese (Simplified)
          return ['zh-CN'] if name == 'zh-CN'

          unknownLocale.push name
          [] # skip this locale
        end

        def save_metadata_to_files(metadata, release_number)
          translations = {}

          metadata_key_file_googleplay.each do |key, parameter|
            final_translations = {}

            metadata.each do |lang, translations|
              unless translations.empty?
                translation = translations[key]
                final_translations[lang] = translation if !translation.nil? && !translation.empty?
              end
            end

            translations[parameter.to_sym] = final_translations
          end

          FileUtils.rm_rf Dir['fastlane/metadata/android/*']

          translations.each do |key, parameter|
            parameter.each do |lang, text|
              path = "fastlane/metadata/android/#{lang}/#{key}.txt"
              path = "fastlane/metadata/android/#{lang}/changelogs/#{release_number}.txt" if key.to_s == 'changelogs'
              dirname = File.dirname(path)
              FileUtils.mkdir_p dirname unless File.directory?(dirname)
              File.write path, text
            end
          end
        end

        def make_request(path, data, resp_type = :post, allow_fail = false)
          uri = URI("https://api.lokalise.com/api2/projects/#{@params[:project_identifier]}/#{path}")
          request = nil

          case resp_type
          when :post
            request = Net::HTTP::Post.new uri, 'Content-Type' => 'application/json', 'Accept' => 'application/json'
            request.body = data.to_json
          when :put
            request = Net::HTTP::Put.new uri, 'Content-Type' => 'application/json', 'Accept' => 'application/json'
            request.body = data.to_json
          else
            request = Net::HTTP::Get.new uri, 'Accept' => 'application/json'
          end

          request.add_field 'x-api-token', @params[:api_token]
          http = Net::HTTP.new uri.host, uri.port
          http.use_ssl = true
          response = http.request request

          jsonResponse = JSON.parse(response.body)

          unless allow_fail
            raise "Bad response 🉐\n#{response.body}" unless jsonResponse.is_a? Hash

            if response.is_a? Net::HTTPSuccess
              UI.success 'Recieved metadatas from lokalize 🚀'
            elsif jsonResponse['error']
              code = jsonResponse['error']['code']
              message = jsonResponse['error']['message']
              raise "Response error code #{code} (#{message}) 📟"
            else
              raise "Bad response 🉐\n#{jsonResponse}"
            end
          end

          jsonResponse
        end

        def get_metadata_from_lokalise(valid_keys)
          response = make_request "keys?include_translations=1&filter_keys=#{valid_keys.join(',')}", nil, :get
          valid_languages = google_play_languages_in_lokalise
          metadata = {}

          unknownLocale = []

          response['keys'].each do |raw_key|
            raw_key['translations'].each do |raw_translation|
              lang = raw_translation['language_iso']

              key = raw_key['key_name']['other']
              translation = raw_translation['translation']

              next unless !translation.nil? && !translation.empty?

              fixed_lang_name_arrays = bereal_fix_language_name(lang, unknownLocale)
              fixed_lang_name_arrays.each do |lang_fixed|
                next if lang_fixed.nil?

                fixed_lang_name_arrays.each do |fixed_lang_name|
                  metadata[fixed_lang_name] = {} unless metadata.key?(fixed_lang_name)
                  metadata[fixed_lang_name] = metadata[fixed_lang_name].merge({ key => translation })
                end
              end
            end
          end

          unknownLocale = unknownLocale.uniq
          UI.message  ''
          UI.message  '|-----Unknown locales------|'
          UI.message  '|                          |'
          unknownLocale.each do |unknownLocal|
            UI.message "|- #{unknownLocal}"
          end
          UI.message  '|                          |'
          UI.message  '|--------------------------|'

          metadata
        end

        def get_metadata_from_lokalise_googleplay
          get_metadata_from_lokalise metadata_keys_googleplay
        end

        def metadata_keys_googleplay
          metadata_key_file_googleplay.keys
        end

        def google_play_languages_in_lokalise
          google_play_languages.map do |lang|
            fix_language_name lang, true
          end
        end

        def google_play_languages
          %w[
            af
            am
            ar
            hy
            az-AZ
            eu-ES
            be
            bn-BD
            bg
            my
            ca
            zh-CN
            zh-TW
            zh-HK
            hr
            cs
            da
            nl-NL
            en-AU
            en-CA
            en-IN
            en-SG
            en-ZA
            en-GB
            en-US
            et-EE
            fil
            fi
            fr-CA
            fr-FR
            gl-ES
            ka-GE
            de-DE
            el-GR
            he
            hi-IN
            hu
            is-IS
            id
            it-IT
            ja
            kn-IN
            km-KH
            ko
            ky
            lo
            lv-LV
            lt-LT
            mk-MK
            ms
            ml-IN
            mr
            mn-MN
            ne-NP
            no
            no-NO
            fa
            pl
            pt-BR
            pt-PT
            ro
            ru-RU
            sr
            si
            sk
            sl-SI
            es-419
            es-ES
            es-US
            sw
            sv-SE
            ta-IN
            te-IN
            th
            tr
            uk
            vi
            zu
          ]
        end

        def fix_language_name(name, for_lokalise = false)
          if for_lokalise
            name = name.gsub('-', '_')
            name = 'tr' if name == 'tr_TR'
            name = 'hy' if name == 'hy_AM'
            name = 'my' if name == 'my_MM'
            name = 'ms' if name == 'ms_MY'
            name = 'cs' if name == 'cs_CZ'
            name = 'da' if name == 'da_DK'
            name = 'et_EE' if name == 'et'
            name = 'fi' if name == 'fi_FI'
            name = 'he' if name == 'iw_IL'
            name = 'hu' if name == 'hu_HU'
            name = 'ja' if name == 'ja_JP'
            name = 'ko' if name == 'ko_KR'
            name = 'ky' if name == 'ky_KG'
            name = 'lo' if name == 'lo_LA'
            name = 'lv_LV' if name == 'lv'
            name = 'lt_LT' if name == 'lt'
            name = 'mr' if name == 'mr_IN'
            name = 'no' if name == 'no_NO'
            name = 'pl' if name == 'pl_PL'
            name = 'si' if name == 'si_LK'
            name = 'sl_SI' if name == 'sl'
          else
            name = 'tr-TR' if name == 'tr'
            name = 'hy-AM' if name == 'hy'
            name = 'my-MM' if name == 'my'
            name = 'ms-MY' if name == 'ms'
            name = 'cs-CZ' if name == 'cs'
            name = 'da-DK' if name == 'da'
            name = 'et' if name == 'et-EE'
            name = 'fi-FI' if name == 'fi'
            name = 'iw-IL' if name == 'he'
            name = 'hu-HU' if name == 'hu'
            name = 'ja-JP' if name == 'ja'
            name = 'ko-KR' if name == 'ko'
            name = 'ky-KG' if name == 'ky'
            name = 'lo-LA' if name == 'lo'
            name = 'lv' if name == 'lv-LV'
            name = 'lt' if name == 'lt-LT'
            name = 'mr-IN' if name == 'mr'
            name = 'no-NO' if name == 'no'
            name = 'pl-PL' if name == 'pl'
            name = 'si-LK' if name == 'si'
            name = 'sl' if name == 'sl-SI'
          end
          name
        end

        def available_options
          [
            FastlaneCore::ConfigItem.new(key: :api_token,
                                         env_name: 'LOKALISE_API_TOKEN',
                                         description: 'API Token for Lokalise',
                                         verify_block: proc do |value|
                                           UI.user_error! "No API token for Lokalise given, pass using `api_token: 'token'`" unless value && !value.empty?
                                         end),
            FastlaneCore::ConfigItem.new(key: :project_identifier,
                                         env_name: 'LOKALISE_PROJECT_ID',
                                         description: 'Lokalise Project ID',
                                         verify_block: proc do |value|
                                           UI.user_error! "No Project Identifier for Lokalise given, pass using `project_identifier: 'identifier'`" unless value && !value.empty?
                                         end),
            FastlaneCore::ConfigItem.new(key: :add_languages,
                                         description: 'Add missing languages in lokalise',
                                         optional: true,
                                         is_string: false,
                                         default_value: false,
                                         verify_block: proc do |value|
                                           UI.user_error! 'Add languages should be true or false' unless [true,
                                                                                                          false].include? value
                                         end),
            FastlaneCore::ConfigItem.new(key: :override_translation,
                                         description: 'Override translations in lokalise',
                                         optional: true,
                                         is_string: false,
                                         default_value: false,
                                         verify_block: proc do |value|
                                           UI.user_error! 'Override translation should be true or false' unless [true,
                                                                                                                 false].include? value
                                         end),
            FastlaneCore::ConfigItem.new(key: :action,
                                         description: 'Action to perform (can be update_lokalise_itunes or update_lokalise_googleplay or update_itunes or update_googleplay)',
                                         optional: false,
                                         is_string: true,
                                         verify_block: proc do |value|
                                           UI.user_error! 'Action should be update_lokalise_googleplay or update_lokalise_itunes or update_itunes or update_googleplay' unless %w[
                                             update_lokalise_itunes update_lokalise_googleplay update_itunes update_googleplay
                                           ].include? value
                                         end),
            FastlaneCore::ConfigItem.new(key: :release_number,
                                         description: 'Release number is required to update google play',
                                         optional: true,
                                         is_string: false),
            FastlaneCore::ConfigItem.new(key: :validate_only,
                                         description: 'Only validate the metadata (works with only update_googleplay action)',
                                         optional: true,
                                         is_string: false,
                                         default_value: false,
                                         verify_block: proc do |value|
                                                         UI.user_error! 'Validate only should be true or false' unless [true,
                                                                                                                        false].include? value
                                                       end)
          ]
        end
      end
    end
  end
end
