# frozen_string_literal: true

opt_out_usage
# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

# do not generate a new version code, just read from file `../versionCode`
def readOnlyVersionCode
  lane_context[SharedValues::ANDROID_VERSION_CODE] = sh('cat ../versionCode', log: false).to_i
  puts lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
end

def readOnlyInstantVersionCode
  lane_context[SharedValues::ANDROID_VERSION_CODE] = sh('cat ../features/instant-light-flow/versionCode', log: false).to_i
  puts lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
end

def generateAndReadVersionCode
  gradle(task: 'writeVersionCode')
  readOnlyVersionCode
end

def getInstantVersionCode
  gradle(task: 'getInstantVersionCode')
  readOnlyInstantVersionCode
end

desc 'Clean'
lane :clean do
  setup_ci if ENV['CI']
  gradle(
    task: 'clean',
    flags: "--warning-mode=none --quiet"
  )
end

desc 'Compile an APK'
lane :compile do
  setup_ci if ENV['CI']
  gradle(
    task: 'clean',
    flags: "--warning-mode=none --quiet"
  )
  gradle(
    task: ':app:assemble',
    flavor: 'prod',
    build_type: 'Release',
    flags: "--warning-mode=none --quiet"
  )
end

desc 'Build a sandbox APK to Firebase App Distribution'
lane :buildSandbox do
  setup_ci if ENV['CI']

  gradle(task: 'clean')
  gradle(
    task: ':app:assemble',
    flavor: 'sandbox',
    build_type: 'AppDistribution',
    flags: "--warning-mode=none --quiet"
  )
end

desc 'Submit a sandbox APK to Firebase App Distribution'
lane :deploySandbox do
  setup_ci if ENV['CI']

  note = ENV['APP_DISTRIBUTION_DESCRIPTION'] || 'new sandbox version'

  firebase_app_distribution(
    app: '1:257333950537:android:12f8b282165afecd67a798',
    groups: 'internal',
    release_notes: note,
    android_artifact_type: 'APK',
    android_artifact_path: 'app/build/outputs/apk/sandbox/appDistribution/app-sandbox-appDistribution.apk'
  )
end

def createFileIfNotExist(params)
  filename = params[:path]
  if File.exist?(filename)
    puts "File already exists: #{filename}"
  else
    File.new(filename, 'w')
    puts "File created: #{filename}"
  end
end

desc 'Build a prod APK to Firebase App Distribution'
lane :buildAppDistribution do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'app/build.gradle.kts')
    generateAndReadVersionCode

    note = ENV['APP_DISTRIBUTION_DESCRIPTION'] || 'version: ' + versionCode.to_s

    gradle(
        task: 'clean',
        flags: "--warning-mode=none --quiet"
    )

    projectFlavor = 'prod'
    projectBuildType = 'AppDistribution'

    gradle(
      task: ':app:assemble',
      flavor: projectFlavor,
      build_type: projectBuildType,
      flags: "--warning-mode=none --quiet"
    )

    # run dependency injection tests
    # gradle(
    #   task: ':app:test',
    #   flavor: projectFlavor,
    #   build_type: projectBuildType
    # )
  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    if ENV['SLACK_NOTIFICATIONS']
      slack(
        message: 'Failed to build new App Distribution (Kotlin)',
        success: false,
        payload: {
          'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
          'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
          'Description' => note,
          'Reason' => e
        },
        default_payloads: []
      )
    end
    # re-raise the exception after slack message
    raise e
  else
    puts 'build success'
  end
end

desc 'Build a prod/internal APK to Firebase App Distribution'
lane :buildInternal do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'app/build.gradle.kts')
    generateAndReadVersionCode

    note = ENV['APP_DISTRIBUTION_DESCRIPTION'] || 'version: ' + versionCode.to_s

    gradle(task: 'clean')

    projectFlavor = 'prod'
    projectBuildType = 'Internal'

    gradle(
      task: ':app:assemble',
      flavor: projectFlavor,
      build_type: projectBuildType,
      flags: "--warning-mode=none --quiet"
    )

  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    raise e
  else
    puts 'build success'
  end
end

desc 'Build a prod/external APK'
lane :buildExternal do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'app/build.gradle.kts')
    generateAndReadVersionCode

    note = ENV['APP_DISTRIBUTION_DESCRIPTION'] || 'version: ' + versionCode.to_s

    gradle(task: 'clean')

    projectFlavor = 'prod'
    projectBuildType = 'External'

    gradle(
      task: ':app:assemble',
      flavor: projectFlavor,
      build_type: projectBuildType,
      flags: "--warning-mode=none --quiet"
    )

  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    raise e
  else
    puts 'build success'
  end
end

desc 'Submit a prod APK to Firebase App Distribution'
lane :deployAppDistribution do
  projectFlavor = 'prod'
  projectBuildType = 'AppDistribution'

  #gradle(
  #  task: ':app:uploadMapping',
  #  flavor: projectFlavor,
  #  build_type: projectBuildType
  #)

  # allows to get this infos : lane_context[SharedValues::ANDROID_VERSION_CODE] & ANDROID_VERSION_NAME
  android_get_version_name(gradle_file: 'app/build.gradle.kts')
  generateAndReadVersionCode

  versionCode = lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
  note = ENV['APP_DISTRIBUTION_DESCRIPTION'] || 'version: ' + versionCode.to_s
  groups = ENV['APP_DISTRIBUTION_GROUP'] || 'internal'

  firebase_app_distribution(
    app: '1:405768487586:android:b390fc4e398383a7dc8dfb',
    groups: groups,
    debug: true,
    release_notes: note,
    android_artifact_type: 'APK',
    android_artifact_path: 'app/build/outputs/apk/prod/appDistribution/app-prod-appDistribution.apk'
  )
rescue StandardError => e
  puts "\n\nError found #{e}\n"

  if ENV['SLACK_NOTIFICATIONS']
    slack(
      message: 'Failed to deploy new version to App Distribution (Kotlin)',
      success: false,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Description' => note,
        'Reason' => e
      },
      default_payloads: []
    )
  end
  # re-raise the exception after slack message
  raise e
else
  most_recent_tags = sh('git tag --sort=-version:refname | head -n 2').split("\n")
  changelog_from_git_commits(
    between: most_recent_tags,
    pretty: '- %s',
    quiet: true
  )
  if ENV['SLACK_NOTIFICATIONS']
    slack(
      message: 'New version deployed to App Distribution (kotlin) 🚀',
      success: true,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Description' => ENV['APP_DISTRIBUTION_DESCRIPTION']
      },
      default_payloads: []
    )
  end
end

desc 'Submit a internal APK to Firebase App Distribution'
lane :deployInternal do
  projectFlavor = 'prod'
  projectBuildType = 'Internal'

  # allows to get this infos : lane_context[SharedValues::ANDROID_VERSION_CODE] & ANDROID_VERSION_NAME
  android_get_version_name(gradle_file: 'app/build.gradle.kts')
  generateAndReadVersionCode

  versionCode = lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
  note = ENV['APP_DISTRIBUTION_DESCRIPTION'] || 'version: ' + versionCode.to_s
  groups = ENV['APP_DISTRIBUTION_GROUP'] || 'internal'

  firebase_app_distribution(
    app: '1:405768487586:android:5c3e788d715d72c2dc8dfb',
    groups: groups,
    debug: true,
    release_notes: note,
    android_artifact_type: 'APK',
    android_artifact_path: 'app/build/outputs/apk/prod/internal/app-prod-internal.apk'
  )
rescue StandardError => e
  puts "\n\nError found #{e}\n"

  raise e
else
  puts "\n\nSuccess"
end

desc 'Try the update metadata'
lane :tryMetaData do
  readOnlyVersionCode
  lokalise_metadata(
    action: 'update_googleplay',
    release_number: lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
  )
end

desc 'Assemble a Prod Release AAB for play store'
lane :assemblePlayStoreVersion do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'app/build.gradle.kts')
    generateAndReadVersionCode

    gradle(task: 'clean')

    projectFlavor = 'prod'
    projectBuildType = 'Release'

    gradle(
      task: ':app:bundle',
      flavor: projectFlavor,
      build_type: projectBuildType,
      flags: "--warning-mode=none --quiet"
    )
  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    slack(
      message: 'Failed to build a new Play Store version',
      success: false,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Reason' => e
      },
      default_payloads: []
    )
    # re-raise the exception after slack message
    raise e
  else
    puts 'New Play Store version successfully built'
  end
end

desc 'Assemble an Instant Prod Release AAB for play store'
lane :assembleInstantPlayStoreVersion do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'features/instant-light-flow/app/build.gradle.kts')
    getInstantVersionCode

    gradle(task: 'clean')

#     projectFlavor = 'prod'
    projectBuildType = 'Release'

#       flavor: projectFlavor,
    gradle(
      task: ':features:instant-light-flow:app:bundle',
      build_type: projectBuildType
    )
  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    slack(
      message: 'Failed to build a new Instant Play Store version',
      success: false,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Reason' => e
      },
      default_payloads: []
    )
    # re-raise the exception after slack message
    raise e
  else
    puts 'New Instant Play Store version successfully built'
  end
end

desc 'Try the playstore api'
lane :tryPlayStoreApi do
    # just before this build a release .aab using the real keystore ./gradlew :app:bundleProdRelease
    upload_to_play_store(
      track: 'internal',
      aab: 'app/build/outputs/bundle/prodRelease/app-prod-release.aab',
      version_name: "3.38.0",
      version_code: 3529145, # last 3529144 + 1
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
end

desc 'Submit a Prod AAB to Play Store'
lane :deployPlayStore do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'app/build.gradle.kts')
    readOnlyVersionCode

    projectFlavor = 'prod'
    projectBuildType = 'Release'

    gradle(
      task: ':app:uploadMapping',
      flavor: projectFlavor,
      build_type: projectBuildType,
      flags: "--warning-mode=none --quiet"
    )

    lokalise_metadata(
      release_number: lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
    )

    uploadVersionName = "#{lane_context[SharedValues::ANDROID_VERSION_CODE]} (#{lane_context[SharedValues::ANDROID_VERSION_NAME]})"
    uploadVersionCode = lane_context[SharedValues::ANDROID_VERSION_CODE]
    puts 'version name : ' + uploadVersionName
    puts 'version code : ' + uploadVersionCode.to_s

    upload_to_play_store(
      track: 'internal',
      aab: 'app/build/outputs/bundle/prodRelease/app-prod-release.aab',
      version_name: uploadVersionName,
      version_code: uploadVersionCode,
      skip_upload_metadata: false,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    raise e
  else
    most_recent_tags = sh('git tag --sort=-version:refname | head -n 2').split("\n")
    changelog_from_git_commits(
      between: most_recent_tags,
      pretty: '- %s',
      quiet: true
    )
  end
end

desc 'Submit a Instant Prod AAB to Play Store'
lane :deployInstantPlayStore do
  setup_ci if ENV['CI']
  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'features/instant-light-flow/app/build.gradle.kts')
    readOnlyInstantVersionCode

#     projectFlavor = 'prod'
    projectBuildType = 'Release'

    # run dependency injection tests
    # gradle(
    #  task: ':app:test',
    #  flavor: projectFlavor,
    #  build_type: projectBuildType
    # )

    gradle(
      task: ':features:instant-light-flow:app:uploadMapping',
#       flavor: projectFlavor,
      build_type: projectBuildType
    )

    lokalise_metadata(
      release_number: lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
    )

    uploadVersionName = "#{lane_context[SharedValues::ANDROID_VERSION_CODE]} (#{lane_context[SharedValues::ANDROID_VERSION_NAME]})"
    uploadVersionCode = lane_context[SharedValues::ANDROID_VERSION_CODE]
    puts 'version name : ' + uploadVersionName
    puts 'version code : ' + uploadVersionCode.to_s

    upload_to_play_store(
      track: 'internal',
      aab: 'features/instant-light-flow/app/build/outputs/bundle/release/app-release.aab',
      version_name: uploadVersionName,
      version_code: uploadVersionCode,
      skip_upload_metadata: false,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  rescue StandardError => e
    puts "\n\nError found #{e}\n"

    slack(
      message: 'Failed to deploy new instant version to Play Store (Internal track)',
      success: false,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Reason' => e
      },
      default_payloads: []
    )
    # re-raise the exception after slack message
    raise e
  else
    most_recent_tags = sh('git tag --sort=-version:refname | head -n 2').split("\n")
    changelog_from_git_commits(
      between: most_recent_tags,
      pretty: '- %s',
      quiet: true
    )
    slack(
      message: 'New instant version deployed to Play Store (Internal track) 🚀',
      success: true,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Changelog' => lane_context[SharedValues::FL_CHANGELOG]
      },
      default_payloads: []
    )
  end
end

desc 'Update last playstore version Metadatas'
lane :updateLastVersionMetadatas do |options|
  setup_ci if ENV['CI']

  if options[:track].nil?
    UI.user_error! 'Please define TRACK environment variable'
    return
  end

  track = options[:track]

  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'app/build.gradle.kts')
    generateAndReadVersionCode

    lokalise_metadata(
      release_number: lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
    )

    puts "\n\nupdating metadatas of version #{lane_context[SharedValues::ANDROID_VERSION_NAME]} track #{track}\n"

    upload_to_play_store(
      track: track,
      version_name: "#{lane_context[SharedValues::ANDROID_VERSION_CODE]} (#{lane_context[SharedValues::ANDROID_VERSION_NAME]})",
      version_code: lane_context[SharedValues::ANDROID_VERSION_CODE].to_s,
      skip_upload_metadata: false,
      skip_upload_changelogs: false,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      skip_upload_apk: true,
      skip_upload_aab: true
    )
  rescue StandardError => e
    slack(
      message: 'Failed to update metadatas of published version on Play Store',
      success: false,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Reason' => e
      },
      default_payloads: []
    )
    # re-raise the exception after slack message
    raise e
  else
    slack(
      message: 'Updated metadatas of last deployed version on Play Store 🚀',
      success: true,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Changelog' => lane_context[SharedValues::FL_CHANGELOG]
      },
      default_payloads: []
    )
  end
end

desc 'Update last playstore instant version Metadatas'
lane :updateLastInstantVersionMetadatas do |options|
  setup_ci if ENV['CI']

  if options[:track].nil?
    UI.user_error! 'Please define TRACK environment variable'
    return
  end

  track = options[:track]

  begin
    # Store version name/code for slack notifications
    android_get_version_name(gradle_file: 'features/instant-light-flow/app/build.gradle.kts')
    getInstantVersionCode

    lokalise_metadata(
      release_number: lane_context[SharedValues::ANDROID_VERSION_CODE].to_i
    )

    puts "\n\nupdating metadatas of version #{lane_context[SharedValues::ANDROID_VERSION_NAME]} track #{track}\n"

    upload_to_play_store(
      track: track,
      version_name: "#{lane_context[SharedValues::ANDROID_VERSION_CODE]} (#{lane_context[SharedValues::ANDROID_VERSION_NAME]})",
      version_code: lane_context[SharedValues::ANDROID_VERSION_CODE].to_s,
      skip_upload_metadata: false,
      skip_upload_changelogs: false,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      skip_upload_apk: true,
      skip_upload_aab: true
    )
  rescue StandardError => e
    slack(
      message: 'Failed to update metadatas of published version on Play Store',
      success: false,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Reason' => e
      },
      default_payloads: []
    )
    # re-raise the exception after slack message
    raise e
  else
    slack(
      message: 'Updated metadatas of last deployed version on Play Store 🚀',
      success: true,
      payload: {
        'Version' => lane_context[SharedValues::ANDROID_VERSION_NAME],
        'Build number' => lane_context[SharedValues::ANDROID_VERSION_CODE],
        'Changelog' => lane_context[SharedValues::FL_CHANGELOG]
      },
      default_payloads: []
    )
  end
end
