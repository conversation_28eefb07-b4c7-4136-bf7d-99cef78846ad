fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### clean

```sh
[bundle exec] fastlane clean
```

Clean

### compile

```sh
[bundle exec] fastlane compile
```

Compile an APK

### buildSandbox

```sh
[bundle exec] fastlane buildSandbox
```

Build a sandbox APK to Firebase App Distribution

### deploySandbox

```sh
[bundle exec] fastlane deploySandbox
```

Submit a sandbox APK to Firebase App Distribution

### buildAppDistribution

```sh
[bundle exec] fastlane buildAppDistribution
```

Build a prod APK to Firebase App Distribution

### buildInternal

```sh
[bundle exec] fastlane buildInternal
```

Build a prod/internal APK to Firebase App Distribution

### buildExternal

```sh
[bundle exec] fastlane buildExternal
```

Build a prod/external APK

### deployAppDistribution

```sh
[bundle exec] fastlane deployAppDistribution
```

Submit a prod APK to Firebase App Distribution

### deployInternal

```sh
[bundle exec] fastlane deployInternal
```

Submit a internal APK to Firebase App Distribution

### tryMetaData

```sh
[bundle exec] fastlane tryMetaData
```

Try the update metadata

### assemblePlayStoreVersion

```sh
[bundle exec] fastlane assemblePlayStoreVersion
```

Assemble a Prod Release AAB for play store

### assembleInstantPlayStoreVersion

```sh
[bundle exec] fastlane assembleInstantPlayStoreVersion
```

Assemble an Instant Prod Release AAB for play store

### tryPlayStoreApi

```sh
[bundle exec] fastlane tryPlayStoreApi
```

Try the playstore api

### deployPlayStore

```sh
[bundle exec] fastlane deployPlayStore
```

Submit a Prod AAB to Play Store

### deployInstantPlayStore

```sh
[bundle exec] fastlane deployInstantPlayStore
```

Submit a Instant Prod AAB to Play Store

### updateLastVersionMetadatas

```sh
[bundle exec] fastlane updateLastVersionMetadatas
```

Update last playstore version Metadatas

### updateLastInstantVersionMetadatas

```sh
[bundle exec] fastlane updateLastInstantVersionMetadatas
```

Update last playstore instant version Metadatas

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
