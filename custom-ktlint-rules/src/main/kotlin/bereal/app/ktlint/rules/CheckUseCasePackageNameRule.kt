package bereal.app.ktlint.rules

import com.pinterest.ktlint.rule.engine.core.api.Rule
import com.pinterest.ktlint.rule.engine.core.api.RuleId
import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtFile
import org.jetbrains.kotlin.psi.stubs.elements.KtFileElementType

class CheckUseCasePackageNameRule : Rule(RuleId("usecase:package"), About()) {

    companion object {
        val regex = Regex("[a-zA-Z]+UseCase$")
    }

    override fun beforeVisitChildNodes(
        node: ASTNode,
        autoCorrect: Boolean,
        emit: (offset: Int, errorMessage: String, canBeAutoCorrected: Boolean) -> Unit,
    ) {
        if (node.elementType == KtFileElementType.INSTANCE && (node.psi as KtFile).name.contains(regex)) {
            val packageName = (node.psi as KtFile).packageFqName.asString()

            if (!packageName.contains("usecases", false)) {
                emit(node.startOffset, "package name containing UseCase files should be named usecases", false)
            }
        }
    }
}
