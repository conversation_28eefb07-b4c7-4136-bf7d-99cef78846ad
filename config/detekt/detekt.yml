style:
  MaxLineLength:
    active: false
  MagicNumber:
    active: false
  ReturnCount:
    active: false
  DestructuringDeclarationWithTooManyEntries:
    active: false
  UtilityClassWithPublicConstructor:
    active: false
  LoopWithTooManyJumpStatements:
    active: false
  UnusedPrivateMember:
    ignoreAnnotated: [ 'Preview', 'PreviewParameter', 'PreviewLightDark' ]
  ForbiddenComment:
    active: false
  ThrowsCount:
    active: false

complexity:
  LongParameterList:
    active: false
  LongMethod:
    active: false
  CyclomaticComplexMethod:
    active: false
  TooManyFunctions:
    active: false
  ComplexCondition:
    active: false
  NestedBlockDepth:
    active: false
  LargeClass:
    active: false

naming:
  MatchingDeclarationName:
    active: false
  FunctionNaming:
    functionPattern: '[a-zA-Z][a-zA-Z0-9]*'  # Allows PascalCase
    ignoreAnnotated: [ 'Composable' ] # Skip rule for @Composable functions

exceptions:
  SwallowedException:
    active: false
  TooGenericExceptionCaught:
    active: false
  InstanceOfCheckForException:
    active: false
  TooGenericExceptionThrown:
    active: false

coroutines:
  GlobalCoroutineUsage:
    active: true
  SuspendFunSwallowedCancellation:
    active: true

performance:
    SpreadOperator:
        active: false